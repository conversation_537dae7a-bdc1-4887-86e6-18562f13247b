#!/bin/bash
set -x

# Build the main process
echo "Building main process..."
npx cross-env NODE_ENV=development webpack --config webpack.main.dev.js

# Build the preload script
echo "Building preload script..."
npx cross-env NODE_ENV=development webpack --config webpack.preload.dev.js

# Start the renderer process in the background
echo "Starting renderer process..."
npx cross-env NODE_ENV=development webpack serve --config webpack.renderer.simple.js &
RENDERER_PID=$!

# Wait for the renderer to be ready
echo "Waiting for renderer to be ready..."
npx wait-on http://localhost:3001

# Start the Electron app
echo "Starting Electron app..."
DEBUG=* npx cross-env NODE_ENV=development electron dist/main/main.js

# Kill the renderer process when the script exits
trap "kill $RENDERER_PID" EXIT
