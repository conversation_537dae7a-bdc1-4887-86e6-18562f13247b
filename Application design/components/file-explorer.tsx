"use client"

import { useState } from "react"
import {
  ChevronRight,
  File,
  FileText,
  FileImage,
  FileCode,
  FileArchive,
  FileAudio,
  FileVideo,
  Folder,
  FolderOpen,
  Search,
  Grid2X2,
  List,
  SortAsc,
  Plus,
  MoreHorizontal,
  Home,
  Star,
  Download,
  Trash2,
  Share2,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// File type icons mapping
const fileIcons = {
  pdf: <FileText className="h-10 w-10 text-red-500" />,
  doc: <FileText className="h-10 w-10 text-blue-500" />,
  docx: <FileText className="h-10 w-10 text-blue-500" />,
  xls: <FileText className="h-10 w-10 text-green-500" />,
  xlsx: <FileText className="h-10 w-10 text-green-500" />,
  ppt: <FileText className="h-10 w-10 text-orange-500" />,
  pptx: <FileText className="h-10 w-10 text-orange-500" />,
  jpg: <FileImage className="h-10 w-10 text-purple-500" />,
  jpeg: <FileImage className="h-10 w-10 text-purple-500" />,
  png: <FileImage className="h-10 w-10 text-purple-500" />,
  gif: <FileImage className="h-10 w-10 text-purple-500" />,
  svg: <FileImage className="h-10 w-10 text-purple-500" />,
  mp3: <FileAudio className="h-10 w-10 text-pink-500" />,
  wav: <FileAudio className="h-10 w-10 text-pink-500" />,
  mp4: <FileVideo className="h-10 w-10 text-indigo-500" />,
  mov: <FileVideo className="h-10 w-10 text-indigo-500" />,
  zip: <FileArchive className="h-10 w-10 text-yellow-500" />,
  rar: <FileArchive className="h-10 w-10 text-yellow-500" />,
  html: <FileCode className="h-10 w-10 text-cyan-500" />,
  css: <FileCode className="h-10 w-10 text-cyan-500" />,
  js: <FileCode className="h-10 w-10 text-yellow-500" />,
  jsx: <FileCode className="h-10 w-10 text-yellow-500" />,
  ts: <FileCode className="h-10 w-10 text-blue-500" />,
  tsx: <FileCode className="h-10 w-10 text-blue-500" />,
  json: <FileCode className="h-10 w-10 text-gray-500" />,
  md: <FileText className="h-10 w-10 text-gray-500" />,
  txt: <FileText className="h-10 w-10 text-gray-500" />,
}

// Sample data structure
const sampleData = {
  folders: [
    { id: 1, name: "Documents", items: 15 },
    { id: 2, name: "Images", items: 42 },
    { id: 3, name: "Videos", items: 8 },
    { id: 4, name: "Projects", items: 5 },
    { id: 5, name: "Downloads", items: 23 },
  ],
  files: [
    { id: 1, name: "Project Proposal.pdf", type: "pdf", size: "2.4 MB", modified: "Today, 2:30 PM", starred: true },
    { id: 2, name: "Budget 2023.xlsx", type: "xlsx", size: "1.8 MB", modified: "Yesterday, 10:15 AM" },
    { id: 3, name: "Meeting Notes.docx", type: "docx", size: "845 KB", modified: "May 10, 2023" },
    { id: 4, name: "Profile Picture.png", type: "png", size: "3.2 MB", modified: "Apr 28, 2023" },
    { id: 5, name: "Presentation.pptx", type: "pptx", size: "5.7 MB", modified: "Apr 15, 2023" },
    { id: 6, name: "Source Code.zip", type: "zip", size: "12.5 MB", modified: "Mar 22, 2023" },
    { id: 7, name: "Audio Recording.mp3", type: "mp3", size: "8.3 MB", modified: "Mar 10, 2023" },
    { id: 8, name: "Product Demo.mp4", type: "mp4", size: "45.2 MB", modified: "Feb 28, 2023" },
    { id: 9, name: "index.html", type: "html", size: "12 KB", modified: "Feb 15, 2023" },
    { id: 10, name: "styles.css", type: "css", size: "8 KB", modified: "Feb 15, 2023" },
    { id: 11, name: "script.js", type: "js", size: "15 KB", modified: "Feb 15, 2023" },
    { id: 12, name: "README.md", type: "md", size: "4 KB", modified: "Feb 10, 2023" },
  ],
}

export default function FileExplorer() {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [currentFolder, setCurrentFolder] = useState<string>("Home")
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({
    favorites: true,
    locations: true,
  })

  // Filter files based on search query
  const filteredFiles = sampleData.files.filter((file) => file.name.toLowerCase().includes(searchQuery.toLowerCase()))

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  return (
    <div className="flex h-screen bg-white">
      {/* Sidebar */}
      <div className="w-64 border-r border-gray-200 bg-gray-50 p-4 flex flex-col">
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">File Explorer</h2>
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search files..."
              className="pl-8 bg-white"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        {/* Favorites Section */}
        <div className="mb-4">
          <div
            className="flex items-center justify-between cursor-pointer py-1 text-gray-700 hover:text-gray-900"
            onClick={() => toggleSection("favorites")}
          >
            <span className="font-medium">Favorites</span>
            <ChevronRight
              className={cn("h-4 w-4 transition-transform", expandedSections.favorites ? "rotate-90" : "")}
            />
          </div>
          {expandedSections.favorites && (
            <div className="ml-2 mt-1 space-y-1">
              <div className="flex items-center py-1 px-2 text-sm rounded-md hover:bg-gray-200 cursor-pointer">
                <Star className="h-4 w-4 mr-2 text-yellow-500" />
                <span>Starred Files</span>
              </div>
              <div className="flex items-center py-1 px-2 text-sm rounded-md hover:bg-gray-200 cursor-pointer">
                <Download className="h-4 w-4 mr-2 text-gray-500" />
                <span>Recent Downloads</span>
              </div>
            </div>
          )}
        </div>

        {/* Locations Section */}
        <div className="mb-4">
          <div
            className="flex items-center justify-between cursor-pointer py-1 text-gray-700 hover:text-gray-900"
            onClick={() => toggleSection("locations")}
          >
            <span className="font-medium">Locations</span>
            <ChevronRight
              className={cn("h-4 w-4 transition-transform", expandedSections.locations ? "rotate-90" : "")}
            />
          </div>
          {expandedSections.locations && (
            <div className="ml-2 mt-1 space-y-1">
              <div
                className={cn(
                  "flex items-center py-1 px-2 text-sm rounded-md hover:bg-gray-200 cursor-pointer",
                  currentFolder === "Home" && "bg-gray-200 font-medium",
                )}
                onClick={() => setCurrentFolder("Home")}
              >
                <Home className="h-4 w-4 mr-2 text-gray-500" />
                <span>Home</span>
              </div>
              {sampleData.folders.map((folder) => (
                <div
                  key={folder.id}
                  className={cn(
                    "flex items-center py-1 px-2 text-sm rounded-md hover:bg-gray-200 cursor-pointer",
                    currentFolder === folder.name && "bg-gray-200 font-medium",
                  )}
                  onClick={() => setCurrentFolder(folder.name)}
                >
                  <Folder className="h-4 w-4 mr-2 text-gray-500" />
                  <span>{folder.name}</span>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="mt-auto pt-4 border-t border-gray-200">
          <div className="flex items-center text-sm text-gray-500">
            <div className="flex-1">Storage</div>
            <div>45.8 GB / 100 GB</div>
          </div>
          <div className="w-full h-1.5 bg-gray-200 rounded-full mt-2">
            <div className="h-full bg-blue-500 rounded-full" style={{ width: "45.8%" }}></div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="border-b border-gray-200 p-4 flex items-center justify-between">
          <div className="flex items-center space-x-1 text-sm text-gray-600">
            <Button variant="ghost" size="sm" className="h-8">
              <Home className="h-4 w-4 mr-1" />
              <span>Home</span>
            </Button>
            {currentFolder !== "Home" && (
              <>
                <span>/</span>
                <Button variant="ghost" size="sm" className="h-8">
                  {currentFolder}
                </Button>
              </>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className={cn(viewMode === "grid" && "bg-gray-100")}
                    onClick={() => setViewMode("grid")}
                  >
                    <Grid2X2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Grid view</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className={cn(viewMode === "list" && "bg-gray-100")}
                    onClick={() => setViewMode("list")}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>List view</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <SortAsc className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Sort files</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Plus className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>New item</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Files and Folders */}
        <div className="flex-1 p-6 overflow-auto">
          {/* Folders */}
          {currentFolder === "Home" && (
            <>
              <h3 className="text-sm font-medium text-gray-500 mb-3">Folders</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
                {sampleData.folders.map((folder) => (
                  <div
                    key={folder.id}
                    className="flex items-center p-3 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm cursor-pointer transition-all"
                    onClick={() => setCurrentFolder(folder.name)}
                  >
                    <FolderOpen className="h-10 w-10 text-blue-400 mr-3" />
                    <div>
                      <div className="font-medium">{folder.name}</div>
                      <div className="text-sm text-gray-500">{folder.items} items</div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}

          {/* Files */}
          <h3 className="text-sm font-medium text-gray-500 mb-3">Files</h3>
          {viewMode === "grid" ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {filteredFiles.map((file) => (
                <div
                  key={file.id}
                  className="group flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm cursor-pointer transition-all"
                >
                  <div className="relative">
                    {fileIcons[file.type as keyof typeof fileIcons] || <File className="h-10 w-10 text-gray-400" />}
                    {file.starred && (
                      <Star className="absolute -top-1 -right-1 h-4 w-4 text-yellow-500 fill-yellow-500" />
                    )}
                  </div>
                  <div className="mt-2 text-center">
                    <div className="font-medium text-sm truncate w-full max-w-[120px]">{file.name}</div>
                    <div className="text-xs text-gray-500">{file.size}</div>
                  </div>
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-7 w-7">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Download className="h-4 w-4 mr-2" />
                          <span>Download</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Share2 className="h-4 w-4 mr-2" />
                          <span>Share</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-500">
                          <Trash2 className="h-4 w-4 mr-2" />
                          <span>Delete</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Name
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Size
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Modified
                    </th>
                    <th scope="col" className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredFiles.map((file) => (
                    <tr key={file.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center">
                            {fileIcons[file.type as keyof typeof fileIcons] || (
                              <File className="h-6 w-6 text-gray-400" />
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="flex items-center">
                              <div className="text-sm font-medium text-gray-900">{file.name}</div>
                              {file.starred && <Star className="ml-2 h-3.5 w-3.5 text-yellow-500 fill-yellow-500" />}
                            </div>
                            <div className="text-sm text-gray-500">{file.type.toUpperCase()}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{file.size}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{file.modified}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Download className="h-4 w-4 mr-2" />
                              <span>Download</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Share2 className="h-4 w-4 mr-2" />
                              <span>Share</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem className="text-red-500">
                              <Trash2 className="h-4 w-4 mr-2" />
                              <span>Delete</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
