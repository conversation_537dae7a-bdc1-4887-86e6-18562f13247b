"use client"

import { useState } from "react"
import { ChevronDown, ChevronRight, Folder, FolderOpen, Search, Settings, Plus, MoreHorizontal } from "lucide-react"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

// Improved code file type icons with distinctive symbols - LARGER and more visible
export const CodeFileIcon = ({
  extension,
  className,
}: {
  extension: string
  className?: string
}) => {
  // Define icon colors for different file types
  const getIconColor = () => {
    const colors: Record<string, string> = {
      js: "text-yellow-400",
      ts: "text-blue-500",
      jsx: "text-cyan-400",
      tsx: "text-cyan-500",
      php: "text-indigo-400",
      py: "text-green-500",
      rb: "text-red-500",
      java: "text-amber-600",
      c: "text-blue-400",
      cpp: "text-blue-600",
      cs: "text-purple-500",
      go: "text-cyan-500",
      rs: "text-orange-600",
      html: "text-orange-500",
      css: "text-blue-400",
      scss: "text-pink-500",
      json: "text-yellow-600",
      yaml: "text-purple-400",
      yml: "text-purple-400",
      xml: "text-orange-400",
      md: "text-gray-400",
      sql: "text-blue-300",
      sh: "text-gray-400",
      bash: "text-gray-400",
      graphql: "text-pink-600",
      vue: "text-emerald-500",
      svelte: "text-red-600",
      dart: "text-cyan-600",
      kt: "text-purple-400",
      swift: "text-orange-500",
    }

    return colors[extension] || "text-muted-foreground"
  }

  // Get background color for icon
  const getIconBgColor = () => {
    const bgColors: Record<string, string> = {
      js: "bg-yellow-400/15",
      ts: "bg-blue-500/15",
      jsx: "bg-cyan-400/15",
      tsx: "bg-cyan-500/15",
      php: "bg-indigo-400/15",
      py: "bg-green-500/15",
      rb: "bg-red-500/15",
      java: "bg-amber-600/15",
      c: "bg-blue-400/15",
      cpp: "bg-blue-600/15",
      cs: "bg-purple-500/15",
      go: "bg-cyan-500/15",
      rs: "bg-orange-600/15",
      html: "bg-orange-500/15",
      css: "bg-blue-400/15",
      scss: "bg-pink-500/15",
      json: "bg-yellow-600/15",
      yaml: "bg-purple-400/15",
      yml: "bg-purple-400/15",
      xml: "bg-orange-400/15",
      md: "bg-gray-400/15",
      sql: "bg-blue-300/15",
      sh: "bg-gray-400/15",
      bash: "bg-gray-400/15",
      graphql: "bg-pink-600/15",
      vue: "bg-emerald-500/15",
      svelte: "bg-red-600/15",
      dart: "bg-cyan-600/15",
      kt: "bg-purple-400/15",
      swift: "bg-orange-500/15",
    }

    return bgColors[extension] || "bg-muted-foreground/15"
  }

  // Simple, clear file type indicators
  const getFileIcon = () => {
    switch (extension) {
      case "js":
        return <div className="font-bold">JS</div>
      case "ts":
        return <div className="font-bold">TS</div>
      case "jsx":
        return <div className="font-bold">JSX</div>
      case "tsx":
        return <div className="font-bold">TSX</div>
      case "php":
        return <div className="font-bold">PHP</div>
      case "py":
        return <div className="font-bold">PY</div>
      case "rb":
        return <div className="font-bold">RB</div>
      case "java":
        return <div className="font-bold">JV</div>
      case "c":
        return <div className="font-bold">C</div>
      case "cpp":
        return <div className="font-bold">C++</div>
      case "cs":
        return <div className="font-bold">C#</div>
      case "go":
        return <div className="font-bold">GO</div>
      case "rs":
        return <div className="font-bold">RS</div>
      case "html":
        return <div className="font-bold">{"<>"}</div>
      case "css":
        return <div className="font-bold">CSS</div>
      case "scss":
        return <div className="font-bold">SC</div>
      case "json":
        return <div className="font-bold">{"{}"}</div>
      case "yaml":
      case "yml":
        return <div className="font-bold">YML</div>
      case "xml":
        return <div className="font-bold">XML</div>
      case "md":
        return <div className="font-bold">MD</div>
      case "sql":
        return <div className="font-bold">SQL</div>
      case "sh":
      case "bash":
        return <div className="font-bold">SH</div>
      case "graphql":
        return <div className="font-bold">GQL</div>
      case "vue":
        return <div className="font-bold">VUE</div>
      case "svelte":
        return <div className="font-bold">SV</div>
      case "dart":
        return <div className="font-bold">DRT</div>
      case "kt":
        return <div className="font-bold">KT</div>
      case "swift":
        return <div className="font-bold">SWF</div>
      default:
        return <div className="font-bold">DOC</div>
    }
  }

  return (
    <div
      className={cn(
        "flex items-center justify-center text-xs w-5 h-5 rounded-sm",
        getIconColor(),
        getIconBgColor(),
        className,
      )}
    >
      {getFileIcon()}
    </div>
  )
}

// Sample project structure with code files
const sampleProjects = [
  {
    id: 1,
    name: "Web Application",
    expanded: true,
    files: [
      { id: 1, name: "index.html", type: "html" },
      { id: 2, name: "styles.css", type: "css" },
      { id: 3, name: "app.js", type: "js" },
      { id: 4, name: "config.json", type: "json" },
      {
        id: 5,
        name: "components",
        type: "folder",
        expanded: true,
        files: [
          { id: 6, name: "Header.jsx", type: "jsx" },
          { id: 7, name: "Footer.tsx", type: "tsx" },
          { id: 8, name: "Sidebar.jsx", type: "jsx" },
        ],
      },
      {
        id: 9,
        name: "utils",
        type: "folder",
        expanded: false,
        files: [
          { id: 10, name: "helpers.js", type: "js" },
          { id: 11, name: "api.ts", type: "ts" },
        ],
      },
    ],
  },
  {
    id: 2,
    name: "Backend API",
    expanded: false,
    files: [
      { id: 12, name: "server.js", type: "js" },
      { id: 13, name: "database.php", type: "php" },
      { id: 14, name: "routes.py", type: "py" },
      { id: 15, name: "schema.graphql", type: "graphql" },
      {
        id: 16,
        name: "models",
        type: "folder",
        expanded: false,
        files: [
          { id: 17, name: "User.php", type: "php" },
          { id: 18, name: "Product.php", type: "php" },
        ],
      },
    ],
  },
  {
    id: 3,
    name: "Mobile App",
    expanded: false,
    files: [
      { id: 19, name: "App.java", type: "java" },
      { id: 20, name: "MainActivity.kt", type: "kt" },
      { id: 21, name: "styles.xml", type: "xml" },
      {
        id: 22,
        name: "screens",
        type: "folder",
        expanded: false,
        files: [
          { id: 23, name: "Home.swift", type: "swift" },
          { id: 24, name: "Profile.dart", type: "dart" },
        ],
      },
    ],
  },
]

// Recursive component to render file tree
const FileTreeItem = ({
  item,
  level = 0,
  onToggle,
  onSelect,
  selectedFile,
}: {
  item: any
  level?: number
  onToggle: (id: number) => void
  onSelect: (file: any) => void
  selectedFile: any | null
}) => {
  const isFolder = item.type === "folder" || Array.isArray(item.files)
  const indent = level * 16
  const isSelected = selectedFile && selectedFile.id === item.id

  return (
    <>
      <div
        className={cn(
          "flex items-center py-1 px-2 text-sm rounded-md cursor-pointer group",
          "transition-colors duration-100",
          isSelected ? "bg-accent text-accent-foreground" : "hover:bg-accent/20",
        )}
        style={{ paddingLeft: `${indent + 8}px` }}
        onClick={(e) => {
          e.stopPropagation()
          if (isFolder) {
            onToggle(item.id)
          } else {
            onSelect(item)
          }
        }}
      >
        {isFolder ? (
          item.expanded ? (
            <ChevronDown className="h-3.5 w-3.5 mr-1.5 text-muted-foreground flex-shrink-0" />
          ) : (
            <ChevronRight className="h-3.5 w-3.5 mr-1.5 text-muted-foreground flex-shrink-0" />
          )
        ) : (
          <div className="w-5 mr-1.5 flex-shrink-0" />
        )}

        {isFolder ? (
          item.expanded ? (
            <FolderOpen className="h-4 w-4 mr-1.5 text-blue-400 flex-shrink-0" />
          ) : (
            <Folder className="h-4 w-4 mr-1.5 text-blue-400 flex-shrink-0" />
          )
        ) : (
          <CodeFileIcon extension={item.type} className="mr-1.5 flex-shrink-0" />
        )}

        <span className="truncate">{item.name}</span>

        {!isFolder && (
          <div className="ml-auto opacity-0 group-hover:opacity-100">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-5 w-5 text-muted-foreground">
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation()
                    console.log(`Rename ${item.name}`)
                  }}
                >
                  Rename
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation()
                    console.log(`Delete ${item.name}`)
                  }}
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {isFolder &&
        item.expanded &&
        item.files &&
        item.files.map((file: any) => (
          <FileTreeItem
            key={file.id}
            item={file}
            level={level + 1}
            onToggle={onToggle}
            onSelect={onSelect}
            selectedFile={selectedFile}
          />
        ))}
    </>
  )
}

export default function FileSidebar({ onFileSelect }: { onFileSelect: (file: any) => void }) {
  const [projects, setProjects] = useState(sampleProjects)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedFile, setSelectedFile] = useState<any>(null)

  const toggleFolder = (id: number) => {
    const updateFolderState = (items: any[]): any[] => {
      return items.map((item) => {
        if (item.id === id) {
          return { ...item, expanded: !item.expanded }
        }

        if (item.files && Array.isArray(item.files)) {
          return {
            ...item,
            files: updateFolderState(item.files),
          }
        }

        return item
      })
    }

    setProjects(updateFolderState(projects))
  }

  const handleFileSelect = (file: any) => {
    setSelectedFile(file)
    onFileSelect(file)
  }

  // Filter files based on search query
  const filterFiles = (items: any[], query: string): any[] => {
    if (!query) return items

    return items.reduce((filtered: any[], item) => {
      if (item.type === "folder" || Array.isArray(item.files)) {
        const filteredFiles = filterFiles(item.files || [], query)
        if (filteredFiles.length > 0 || item.name.toLowerCase().includes(query.toLowerCase())) {
          filtered.push({
            ...item,
            expanded: filteredFiles.length > 0 ? true : item.expanded,
            files: filteredFiles,
          })
        }
      } else if (item.name.toLowerCase().includes(query.toLowerCase())) {
        filtered.push(item)
      }
      return filtered
    }, [])
  }

  const filteredProjects = searchQuery ? filterFiles(projects, searchQuery) : projects

  return (
    <div className="h-full bg-editor-sidebar-bg text-editor-sidebar-fg flex flex-col">
      <div className="p-4 border-b border-editor-border">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium">Explorer</h2>
          <div className="flex items-center space-x-1">
            <Button variant="ghost" size="icon" className="h-7 w-7 text-muted-foreground hover:text-foreground">
              <Plus className="h-3.5 w-3.5" />
            </Button>
            <Button variant="ghost" size="icon" className="h-7 w-7 text-muted-foreground hover:text-foreground">
              <Settings className="h-3.5 w-3.5" />
            </Button>
          </div>
        </div>

        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-3.5 w-3.5 text-muted-foreground" />
          <Input
            placeholder="Search files..."
            className="pl-8 h-9 bg-background border-input text-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-2">
          <div className="flex items-center justify-between py-1 px-2 mb-1">
            <span className="text-xs font-medium uppercase tracking-wider text-muted-foreground">Projects</span>
            <Button variant="ghost" size="icon" className="h-6 w-6 text-muted-foreground hover:text-foreground">
              <Plus className="h-3.5 w-3.5" />
            </Button>
          </div>

          {filteredProjects.map((project) => (
            <div key={project.id} className="mb-2">
              <FileTreeItem
                item={project}
                onToggle={toggleFolder}
                onSelect={handleFileSelect}
                selectedFile={selectedFile}
              />
            </div>
          ))}
        </div>
      </ScrollArea>

      <div className="p-3 border-t border-editor-border flex items-center justify-between text-xs text-muted-foreground">
        <span>3 projects</span>
        <span>24 files</span>
      </div>
    </div>
  )
}
