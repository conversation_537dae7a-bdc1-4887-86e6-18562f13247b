"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON><PERSON>, GitBranch, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { CodeFileIcon } from "@/components/file-sidebar"
import { useTheme } from "next-themes"

// Sample code content for different file types
const sampleCodeContent: Record<string, string> = {
  html: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div id="app"></div>
  <script src="app.js"></script>
</body>
</html>`,
  css: `body {
  font-family: 'Inter', sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
}

#app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}`,
  js: `// Main application file
const app = document.getElementById('app');

function init() {
  console.log('Application initialized');
  renderApp();
}

function renderApp() {
  app.innerHTML = '<h1>Hello World</h1>';
  
  // Add event listeners
  setupEventListeners();
}

function setupEventListeners() {
  document.addEventListener('click', (e) => {
    console.log('Document clicked', e.target);
  });
}

// Initialize the application
init();`,
  jsx: `import React, { useState, useEffect } from 'react';

function Header({ title }) {
  const [isScrolled, setIsScrolled] = useState(false);
  
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  return (
    <header className={isScrolled ? 'header scrolled' : 'header'}>
      <h1>{title}</h1>
      <nav>
        <ul>
          <li><a href="/">Home</a></li>
          <li><a href="/about">About</a></li>
          <li><a href="/contact">Contact</a></li>
        </ul>
      </nav>
    </header>
  );
}

export default Header;`,
  tsx: `import React, { useState, useEffect } from 'react';

interface FooterProps {
  companyName: string;
  year: number;
  links?: Array<{
    name: string;
    url: string;
  }>;
}

const Footer: React.FC<FooterProps> = ({ companyName, year, links = [] }) => {
  const [currentYear, setCurrentYear] = useState(year);
  
  useEffect(() => {
    setCurrentYear(new Date().getFullYear());
  }, []);
  
  return (
    <footer className="footer">
      <div className="footer-content">
        <p>&copy; {currentYear} {companyName}. All rights reserved.</p>
        {links.length > 0 && (
          <ul className="footer-links">
            {links.map((link, index) => (
              <li key={index}>
                <a href={link.url}>{link.name}</a>
              </li>
            ))}
          </ul>
        )}
      </div>
    </footer>
  );
};

export default Footer;`,
  json: `{
  "name": "web-application",
  "version": "1.0.0",
  "description": "A modern web application",
  "main": "index.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "build": "webpack --mode production",
    "test": "jest"
  },
  "dependencies": {
    "express": "^4.17.1",
    "react": "^17.0.2",
    "react-dom": "^17.0.2"
  },
  "devDependencies": {
    "jest": "^27.0.6",
    "nodemon": "^2.0.12",
    "webpack": "^5.52.0",
    "webpack-cli": "^4.8.0"
  }
}`,
  py: `import os
import sys
from typing import List, Dict, Any, Optional

class RouteHandler:
    """
    Handles API routes and their corresponding functions
    """
    
    def __init__(self, base_path: str = "/api"):
        self.routes: Dict[str, callable] = {}
        self.base_path = base_path
        
    def register(self, path: str, method: str = "GET"):
        """Decorator to register a new route"""
        full_path = f"{self.base_path}{path}"
        
        def decorator(func):
            self.routes[(full_path, method.upper())] = func
            return func
        
        return decorator
        
    def handle_request(self, path: str, method: str, **kwargs) -> Any:
        """Handle an incoming request"""
        handler = self.routes.get((path, method.upper()))
        
        if handler is None:
            return {"error": "Not found"}, 404
            
        try:
            return handler(**kwargs)
        except Exception as e:
            print(f"Error handling request: {e}", file=sys.stderr)
            return {"error": str(e)}, 500

# Create a route handler
routes = RouteHandler()

@routes.register("/users")
def get_users():
    """Get all users"""
    return {"users": ["user1", "user2", "user3"]}

@routes.register("/users", method="POST")
def create_user(name: str, email: str):
    """Create a new user"""
    # In a real app, this would save to a database
    print(f"Creating user: {name}, {email}")
    return {"status": "success", "user": {"name": name, "email": email}}

if __name__ == "__main__":
    # Example usage
    result, status = routes.handle_request("/api/users", "GET")
    print(f"Status: {status}, Result: {result}")`,
  php: `<?php
/**
 * Database connection and model class
 */
class Database {
    private $connection;
    private $host;
    private $username;
    private $password;
    private $database;
    
    /**
     * Constructor
     */
    public function __construct($host, $username, $password, $database) {
        $this->host = $host;
        $this->username = $username;
        $this->password = $password;
        $this->database = $database;
    }
    
    /**
     * Connect to the database
     */
    public function connect() {
        try {
            $this->connection = new PDO(
                "mysql:host={$this->host};dbname={$this->database}",
                $this->username,
                $this->password
            );
            $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            return true;
        } catch (PDOException $e) {
            echo "Connection failed: " . $e->getMessage();
            return false;
        }
    }
    
    /**
     * Execute a query
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            echo "Query failed: " . $e->getMessage();
            return false;
        }
    }
    
    /**
     * Get results as an array
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetchAll(PDO::FETCH_ASSOC) : [];
    }
}

// Example usage
$db = new Database("localhost", "root", "password", "my_database");
if ($db->connect()) {
    $users = $db->fetchAll("SELECT * FROM users WHERE active = ?", [1]);
    print_r($users);
}
?>`,
  graphql: `# Schema definition
type User {
  id: ID!
  username: String!
  email: String!
  posts: [Post!]
  profile: Profile
  createdAt: String!
  updatedAt: String
}

type Post {
  id: ID!
  title: String!
  content: String!
  published: Boolean!
  author: User!
  comments: [Comment!]
  createdAt: String!
  updatedAt: String
}

type Comment {
  id: ID!
  text: String!
  author: User!
  post: Post!
  createdAt: String!
  updatedAt: String
}

type Profile {
  id: ID!
  bio: String
  location: String
  website: String
  avatar: String
  user: User!
}

# Queries
type Query {
  user(id: ID!): User
  users(limit: Int): [User!]!
  post(id: ID!): Post
  posts(limit: Int, published: Boolean): [Post!]!
}

# Mutations
type Mutation {
  createUser(username: String!, email: String!, password: String!): User!
  updateUser(id: ID!, username: String, email: String): User!
  deleteUser(id: ID!): Boolean!
  
  createPost(title: String!, content: String!, published: Boolean!, authorId: ID!): Post!
  updatePost(id: ID!, title: String, content: String, published: Boolean): Post!
  deletePost(id: ID!): Boolean!
  
  createComment(text: String!, authorId: ID!, postId: ID!): Comment!
  deleteComment(id: ID!): Boolean!
}`,
  java: `package com.example.app;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Main application class
 */
public class App {
    public static void main(String[] args) {
        System.out.println("Starting application...");
        
        // Create some users
        List<User> users = new ArrayList<>();
        users.add(new User(1, "john_doe", "<EMAIL>"));
        users.add(new User(2, "jane_smith", "<EMAIL>"));
        users.add(new User(3, "bob_johnson", "<EMAIL>"));
        
        // Print all users
        System.out.println("All users:");
        users.forEach(System.out::println);
        
        // Filter users
        List<User> filteredUsers = users.stream()
            .filter(user -> user.getUsername().startsWith("j"))
            .collect(Collectors.toList());
            
        System.out.println("\\nFiltered users (username starts with 'j'):");
        filteredUsers.forEach(System.out::println);
    }
}

/**
 * User model class
 */
class User {
    private int id;
    private String username;
    private String email;
    
    public User(int id, String username, String email) {
        this.id = id;
        this.username = username;
        this.email = email;
    }
    
    public int getId() {
        return id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public String getEmail() {
        return email;
    }
    
    @Override
    public String toString() {
        return "User{" +
            "id=" + id +
            ", username='" + username + "'" +
            ", email='" + email + "'" +
            "}";
    }
}`,
}

// Default code for file types not in the sample content
const defaultCode = `// No preview available for this file type
// Select another file to view its content`

// Map file extensions to language names for highlighting
const getLanguageFromExtension = (extension: string): string => {
  const languageMap: Record<string, string> = {
    html: "html",
    css: "css",
    js: "javascript",
    jsx: "jsx",
    ts: "typescript",
    tsx: "tsx",
    json: "json",
    py: "python",
    php: "php",
    java: "java",
    graphql: "graphql",
    md: "markdown",
    yaml: "yaml",
    yml: "yaml",
    sh: "bash",
    bash: "bash",
    sql: "sql",
  }

  return languageMap[extension] || "javascript"
}

// Custom syntax highlighting component
const CodeBlock = ({ code, language }: { code: string; language: string }) => {
  const { theme } = useTheme()
  const isDark = theme === "dark"

  // Process the code for syntax highlighting
  const highlightedCode = processCodeForHighlighting(code, language, isDark)

  return (
    <pre className="p-4 text-sm font-mono overflow-auto">
      <code className="monaco-syntax" dangerouslySetInnerHTML={{ __html: highlightedCode }} />
    </pre>
  )
}

// Function to process code for syntax highlighting
const processCodeForHighlighting = (code: string, language: string, isDark: boolean): string => {
  // Simple syntax highlighting for demonstration
  // In a real implementation, you would use a more sophisticated parser

  // Replace this with a proper syntax highlighting library if needed
  const processedCode = code.replace(/</g, "&lt;").replace(/>/g, "&gt;")

  // Keywords
  const keywords = [
    "function",
    "const",
    "let",
    "var",
    "return",
    "if",
    "else",
    "for",
    "while",
    "class",
    "import",
    "export",
    "from",
    "extends",
    "implements",
    "interface",
    "type",
    "enum",
    "public",
    "private",
    "protected",
    "static",
    "async",
    "await",
    "try",
    "catch",
    "finally",
    "throw",
    "new",
    "this",
    "super",
    "typeof",
    "def",
    "package",
  ]

  // Add line numbers
  const lines = processedCode.split("\n")
  const lineNumberWidth = String(lines.length).length

  const processedLines = lines.map((line, i) => {
    const lineNumber = i + 1
    const paddedLineNumber = String(lineNumber).padStart(lineNumberWidth, " ")

    // Apply syntax highlighting to the line
    let highlightedLine = line

    // Highlight keywords
    keywords.forEach((keyword) => {
      const regex = new RegExp(`\\b${keyword}\\b`, "g")
      highlightedLine = highlightedLine.replace(regex, `<span class="monaco-keyword">${keyword}</span>`)
    })

    // Highlight strings
    highlightedLine = highlightedLine.replace(/(["'`])(.*?)\1/g, '<span class="monaco-string">$&</span>')

    // Highlight comments
    highlightedLine = highlightedLine.replace(
      /(\/\/.*$|\/\*[\s\S]*?\*\/|#.*$)/gm,
      '<span class="monaco-comment">$&</span>',
    )

    // Highlight numbers
    highlightedLine = highlightedLine.replace(/\b(\d+(\.\d+)?)\b/g, '<span class="monaco-number">$&</span>')

    // Highlight function names
    highlightedLine = highlightedLine.replace(
      /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/g,
      '<span class="monaco-function">$1</span>(',
    )

    // Highlight JSX/HTML tags
    if (language === "jsx" || language === "tsx" || language === "html") {
      highlightedLine = highlightedLine.replace(/(&lt;\/?[a-zA-Z][a-zA-Z0-9]*)/g, '<span class="monaco-tag">$&</span>')
    }

    // Highlight JSX/TSX attributes
    if (language === "jsx" || language === "tsx") {
      highlightedLine = highlightedLine.replace(
        /\b([a-zA-Z][a-zA-Z0-9]*)(=)(?=["'{])/g,
        '<span class="monaco-attribute">$1</span>$2',
      )
    }

    // Highlight types in TypeScript
    if (language === "typescript" || language === "tsx") {
      highlightedLine = highlightedLine.replace(/:\s*([A-Z][a-zA-Z0-9_$]*)/g, ': <span class="monaco-type">$1</span>')
      highlightedLine = highlightedLine.replace(
        /\binterface\s+([A-Z][a-zA-Z0-9_$]*)/g,
        'interface <span class="monaco-type">$1</span>',
      )
    }

    // Highlight variables
    highlightedLine = highlightedLine.replace(/\b([a-zA-Z_$][a-zA-Z0-9_$]*)\b(?!\()/g, (match, p1) => {
      if (keywords.includes(p1)) {
        return match // Skip keywords
      }
      return `<span class="monaco-variable">${p1}</span>`
    })

    return `<div class="monaco-line"><span class="monaco-line-number">${paddedLineNumber}</span>${highlightedLine}</div>`
  })

  return processedLines.join("")
}

// AI suggestion component
const AiSuggestion = ({ visible }: { visible: boolean }) => {
  if (!visible) return null

  // Using a string template to avoid JSX parsing issues with arrow functions
  const codeSnippet = `
<span class="monaco-keyword">function</span> <span class="monaco-function">debounce</span>(fn, delay) {
  <span class="monaco-keyword">let</span> timer;
  <span class="monaco-keyword">return</span> <span class="monaco-keyword">function</span>(...args) {
    clearTimeout(timer);
    timer = setTimeout(() <span class="monaco-keyword">=&gt;</span> fn.apply(this, args), delay);
  };
}

<span class="monaco-comment">// Use debounced version of the scroll handler</span>
<span class="monaco-keyword">const</span> debouncedHandleScroll = <span class="monaco-function">debounce</span>(handleScroll, <span class="monaco-number">100</span>);
window.<span class="monaco-function">addEventListener</span>(<span class="monaco-string">'scroll'</span>, debouncedHandleScroll);
  `.trim()

  return (
    <div className="border-t border-editor-border p-4 bg-background/50">
      <div className="flex items-center mb-2">
        <Sparkles className="h-4 w-4 text-editor-highlight mr-2" />
        <span className="text-sm font-medium text-editor-highlight">AI Suggestion</span>
      </div>
      <p className="text-xs text-muted-foreground mb-3">
        Consider using a more efficient approach for event handling by implementing debounce:
      </p>
      <pre className="bg-accent p-2 rounded-md text-xs font-mono">
        <code className="monaco-syntax" dangerouslySetInnerHTML={{ __html: codeSnippet }} />
      </pre>
      <div className="flex justify-end mt-2">
        <Button variant="ghost" size="sm" className="text-xs h-7 px-2 text-muted-foreground hover:text-foreground">
          Ignore
        </Button>
        <Button
          size="sm"
          className="text-xs h-7 px-2 bg-editor-highlight text-editor-highlight-fg hover:bg-editor-highlight/90 ml-2"
        >
          Apply Suggestion
        </Button>
      </div>
    </div>
  )
}

// Tab component for open files
const EditorTab = ({
  file,
  active,
  onClick,
  onClose,
}: {
  file: any
  active: boolean
  onClick: () => void
  onClose: () => void
}) => {
  return (
    <div
      className={cn(
        "flex items-center h-9 px-3 border-r border-editor-border cursor-pointer group",
        active
          ? "bg-editor-tab-active-bg text-editor-tab-active-fg"
          : "bg-editor-tab-bg text-editor-tab-fg hover:bg-accent/50",
      )}
      onClick={onClick}
    >
      <CodeFileIcon extension={file.type} className="mr-2" />
      <span className="text-sm">{file.name}</span>
      <Button
        variant="ghost"
        size="icon"
        className="h-5 w-5 ml-2 opacity-0 group-hover:opacity-100"
        onClick={(e) => {
          e.stopPropagation()
          onClose()
        }}
      >
        <X className="h-3 w-3" />
      </Button>
    </div>
  )
}

export default function CodeEditor({ file }: { file: any | null }) {
  const [openFiles, setOpenFiles] = useState<any[]>([])
  const [activeFile, setActiveFile] = useState<any | null>(null)
  const [showAiSuggestion, setShowAiSuggestion] = useState(false)

  // Add file to open tabs when a new file is selected
  useEffect(() => {
    if (file && !openFiles.some((f) => f.id === file.id)) {
      setOpenFiles((prev) => [...prev, file])
    }
    if (file) {
      setActiveFile(file)
    }
  }, [file])

  // Get code content for the active file
  const getCodeContent = (fileType: string) => {
    return sampleCodeContent[fileType] || defaultCode
  }

  // Close a tab
  const closeTab = (fileToClose: any) => {
    setOpenFiles((prev) => prev.filter((f) => f.id !== fileToClose.id))
    if (activeFile && activeFile.id === fileToClose.id) {
      setActiveFile(openFiles.length > 1 ? openFiles.find((f) => f.id !== fileToClose.id) : null)
    }
  }

  // Show AI suggestion after a delay for certain file types
  useEffect(() => {
    if (activeFile && ["js", "jsx", "tsx"].includes(activeFile.type)) {
      const timer = setTimeout(() => {
        setShowAiSuggestion(true)
      }, 2000)
      return () => clearTimeout(timer)
    } else {
      setShowAiSuggestion(false)
    }
  }, [activeFile])

  return (
    <div className="flex flex-col h-full bg-editor-bg text-editor-fg">
      {/* Tabs for open files */}
      <div className="flex border-b border-editor-border overflow-x-auto hide-scrollbar">
        {openFiles.map((openFile) => (
          <EditorTab
            key={openFile.id}
            file={openFile}
            active={activeFile && activeFile.id === openFile.id}
            onClick={() => setActiveFile(openFile)}
            onClose={() => closeTab(openFile)}
          />
        ))}
      </div>

      {/* Editor content */}
      <div className="flex-1 overflow-hidden">
        {activeFile ? (
          <div className="h-full flex flex-col">
            <ScrollArea className="flex-1">
              <CodeBlock code={getCodeContent(activeFile.type)} language={getLanguageFromExtension(activeFile.type)} />
            </ScrollArea>
            <AiSuggestion visible={showAiSuggestion} />
          </div>
        ) : (
          <div className="h-full flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <p>No file selected</p>
              <p className="text-sm mt-2">Select a file from the sidebar to start editing</p>
            </div>
          </div>
        )}
      </div>

      {/* Status bar */}
      <div className="h-6 border-t border-editor-border bg-editor-statusbar-bg flex items-center px-4 text-xs text-editor-statusbar-fg">
        <div className="flex items-center">
          <GitBranch className="h-3 w-3 mr-1" />
          <span>main</span>
        </div>
        <div className="ml-4 flex items-center">
          {activeFile && (
            <>
              <span>{activeFile.type.toUpperCase()}</span>
              <span className="mx-2">•</span>
              <span>UTF-8</span>
            </>
          )}
        </div>
        <div className="ml-auto flex items-center space-x-4">
          <span>Ln 1, Col 1</span>
          <span>Spaces: 2</span>
        </div>
      </div>
    </div>
  )
}
