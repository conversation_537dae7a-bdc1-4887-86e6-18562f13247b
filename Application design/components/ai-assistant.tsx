"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, Send, X, Maximize2, Minimize2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"

type Message = {
  id: number
  content: string
  sender: "user" | "ai"
  timestamp: Date
}

export default function AiAssistant() {
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [input, setInput] = useState("")
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      content: "Hello! I'm your AI coding assistant. How can I help you today?",
      sender: "ai",
      timestamp: new Date(),
    },
  ])

  const handleSendMessage = () => {
    if (!input.trim()) return

    // Add user message
    const userMessage: Message = {
      id: messages.length + 1,
      content: input,
      sender: "user",
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInput("")

    // Simulate AI response
    setTimeout(() => {
      const aiResponses: Record<string, string> = {
        help: "I can help you with coding questions, explain code, suggest improvements, or generate code snippets. Just ask away!",
        hello: "Hello there! How can I assist with your coding today?",
        generate:
          "What kind of code would you like me to generate? I can help with JavaScript, TypeScript, React, HTML, CSS, and more.",
      }

      // Find a matching response or use default
      const matchingKey = Object.keys(aiResponses).find((key) => input.toLowerCase().includes(key.toLowerCase()))

      const responseContent = matchingKey
        ? aiResponses[matchingKey]
        : "I'm here to help with your coding questions. Could you provide more details about what you need?"

      const aiMessage: Message = {
        id: messages.length + 2,
        content: responseContent,
        sender: "ai",
        timestamp: new Date(),
      }

      setMessages((prev) => [...prev, aiMessage])
    }, 1000)
  }

  if (!isOpen) {
    return (
      <Button
        className="fixed bottom-4 right-4 rounded-full h-12 w-12 bg-purple-600 hover:bg-purple-700 shadow-lg"
        onClick={() => setIsOpen(true)}
      >
        <Sparkles className="h-5 w-5" />
      </Button>
    )
  }

  return (
    <div
      className={cn(
        "fixed bottom-4 right-4 bg-gray-900 border border-gray-800 rounded-lg shadow-xl transition-all duration-200 overflow-hidden",
        isMinimized ? "w-64 h-12" : "w-80 h-96",
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-800 bg-gray-900">
        <div className="flex items-center">
          <Sparkles className="h-4 w-4 text-purple-400 mr-2" />
          <span className="font-medium text-sm">AI Assistant</span>
        </div>
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-gray-400 hover:text-gray-300"
            onClick={() => setIsMinimized(!isMinimized)}
          >
            {isMinimized ? <Maximize2 className="h-3 w-3" /> : <Minimize2 className="h-3 w-3" />}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-gray-400 hover:text-gray-300"
            onClick={() => setIsOpen(false)}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* Messages */}
          <ScrollArea className="h-[calc(100%-96px)] p-3">
            <div className="space-y-3">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "p-2 rounded-lg max-w-[85%]",
                    message.sender === "user" ? "bg-purple-600 text-white ml-auto" : "bg-gray-800 text-gray-300",
                  )}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className="text-xs opacity-70 mt-1">
                    {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                  </p>
                </div>
              ))}
            </div>
          </ScrollArea>

          {/* Input */}
          <div className="p-3 border-t border-gray-800">
            <div className="flex items-center">
              <Input
                placeholder="Ask a question..."
                className="bg-gray-800 border-gray-700 text-sm focus-visible:ring-gray-700"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleSendMessage()
                  }
                }}
              />
              <Button size="icon" className="ml-2 bg-purple-600 hover:bg-purple-700" onClick={handleSendMessage}>
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
