"use client"

import type React from "react"
import { createContext, useContext, useState } from "react"
import { useBoard } from "./board-context"
import { BoardAgentAPI } from "@/lib/board-agent-api"

// Agent types
export type AgentStatus = "idle" | "working" | "paused" | "error"
export type AgentType = "task-manager" | "developer" | "tester" | "documentation" | "devops" | "designer"

export interface Agent {
  id: string
  name: string
  type: AgentType
  status: AgentStatus
  currentTaskId?: string
  capabilities: string[]
  resourceUsage: {
    cpu: number
    memory: number
    tokens: number
  }
}

// Context type
interface AgentBoardControllerContextType {
  agents: Agent[]
  isAgentSystemActive: boolean
  toggleAgentSystem: () => void
  registerAgent: (agent: Agent) => void
  removeAgent: (agentId: string) => void
  assignTaskToAgent: (agentId: string, taskId: string) => void
  unassignTaskFromAgent: (agentId: string) => void
  updateAgentStatus: (agentId: string, status: AgentStatus, taskId?: string) => void
  updateAgentResourceUsage: (agentId: string, resourceUsage: Partial<Agent["resourceUsage"]>) => void
  getAgentById: (agentId: string) => Agent | undefined
  getAgentsByType: (type: AgentType) => Agent[]
  getAgentsByStatus: (status: AgentStatus) => Agent[]
  getAgentAssignedCards: (agentId: string) => any[]
  getAgentsAssignedToCard: (cardId: string) => Agent[] // Added this function
  pauseAgent: (agentId: string) => void
  resumeAgent: (agentId: string) => void
}

// Initial agents
const initialAgents: Agent[] = [
  {
    id: "agent-1",
    name: "TaskBot",
    type: "task-manager",
    status: "idle",
    capabilities: ["task-management", "prioritization", "scheduling"],
    resourceUsage: { cpu: 0, memory: 0, tokens: 0 },
  },
  {
    id: "agent-2",
    name: "CodeAssistant",
    type: "developer",
    status: "idle",
    capabilities: ["javascript", "react", "node"],
    resourceUsage: { cpu: 0, memory: 0, tokens: 0 },
  },
  {
    id: "agent-3",
    name: "QATester",
    type: "tester",
    status: "idle",
    capabilities: ["testing", "automation", "quality-assurance"],
    resourceUsage: { cpu: 0, memory: 0, tokens: 0 },
  },
  {
    id: "agent-4",
    name: "DocWriter",
    type: "documentation",
    status: "idle",
    capabilities: ["documentation", "technical-writing", "markdown"],
    resourceUsage: { cpu: 0, memory: 0, tokens: 0 },
  },
]

// Create context
const AgentBoardControllerContext = createContext<AgentBoardControllerContextType | undefined>(undefined)

// Provider component
export function AgentBoardControllerProvider({ children }: { children: React.ReactNode }) {
  const [agents, setAgents] = useState<Agent[]>(initialAgents)
  const [isAgentSystemActive, setIsAgentSystemActive] = useState(false)
  const boardContext = useBoard()
  const boardAgentAPI = new BoardAgentAPI(boardContext)

  // Toggle agent system
  const toggleAgentSystem = () => {
    const newState = !isAgentSystemActive
    setIsAgentSystemActive(newState)

    // Update agent statuses when toggling
    if (!newState) {
      setAgents(
        agents.map((agent) => ({
          ...agent,
          status: "idle",
          currentTaskId: undefined,
        })),
      )
    }
  }

  // Register a new agent
  const registerAgent = (agent: Agent) => {
    setAgents((prevAgents) => [...prevAgents, agent])
  }

  // Remove an agent
  const removeAgent = (agentId: string) => {
    setAgents((prevAgents) => prevAgents.filter((agent) => agent.id !== agentId))
  }

  // Assign task to agent
  const assignTaskToAgent = (agentId: string, taskId: string) => {
    setAgents((prevAgents) =>
      prevAgents.map((agent) =>
        agent.id === agentId ? { ...agent, currentTaskId: taskId, status: "working" } : agent,
      ),
    )
  }

  // Unassign task from agent
  const unassignTaskFromAgent = (agentId: string) => {
    setAgents((prevAgents) =>
      prevAgents.map((agent) =>
        agent.id === agentId ? { ...agent, currentTaskId: undefined, status: "idle" } : agent,
      ),
    )
  }

  // Update agent status
  const updateAgentStatus = (agentId: string, status: AgentStatus, taskId?: string) => {
    setAgents((prevAgents) =>
      prevAgents.map((agent) =>
        agent.id === agentId
          ? {
              ...agent,
              status,
              currentTaskId: taskId !== undefined ? taskId : agent.currentTaskId,
            }
          : agent,
      ),
    )
  }

  // Update agent resource usage
  const updateAgentResourceUsage = (agentId: string, resourceUsage: Partial<Agent["resourceUsage"]>) => {
    setAgents((prevAgents) =>
      prevAgents.map((agent) =>
        agent.id === agentId
          ? {
              ...agent,
              resourceUsage: { ...agent.resourceUsage, ...resourceUsage },
            }
          : agent,
      ),
    )
  }

  // Get agent by ID
  const getAgentById = (agentId: string) => {
    return agents.find((agent) => agent.id === agentId)
  }

  // Get agents by type
  const getAgentsByType = (type: AgentType) => {
    return agents.filter((agent) => agent.type === type)
  }

  // Get agents by status
  const getAgentsByStatus = (status: AgentStatus) => {
    return agents.filter((agent) => agent.status === status)
  }

  // Get cards assigned to an agent
  const getAgentAssignedCards = (agentId: string) => {
    // This is a mock implementation
    return []
  }

  // Get agents assigned to a card
  const getAgentsAssignedToCard = (cardId: string) => {
    // This is a mock implementation
    // In a real implementation, you would check the card's agentAssignments
    // and return the corresponding agents
    return agents
      .filter(
        (agent) =>
          // For demo purposes, randomly assign some agents to cards
          // In a real implementation, you would check if the agent is assigned to the card
          Math.random() > 0.7,
      )
      .slice(0, Math.floor(Math.random() * 3))
  }

  // Pause agent
  const pauseAgent = (agentId: string) => {
    updateAgentStatus(agentId, "paused")
  }

  // Resume agent
  const resumeAgent = (agentId: string) => {
    const agent = getAgentById(agentId)
    if (agent) {
      updateAgentStatus(agentId, agent.currentTaskId ? "working" : "idle")
    }
  }

  return (
    <AgentBoardControllerContext.Provider
      value={{
        agents,
        isAgentSystemActive,
        toggleAgentSystem,
        registerAgent,
        removeAgent,
        assignTaskToAgent,
        unassignTaskFromAgent,
        updateAgentStatus,
        updateAgentResourceUsage,
        getAgentById,
        getAgentsByType,
        getAgentsByStatus,
        getAgentAssignedCards,
        getAgentsAssignedToCard, // Added this function to the context
        pauseAgent,
        resumeAgent,
      }}
    >
      {children}
    </AgentBoardControllerContext.Provider>
  )
}

// Custom hook to use the agent board controller
export function useAgentBoardController() {
  const context = useContext(AgentBoardControllerContext)
  if (!context) {
    throw new Error("useAgentBoardController must be used within an AgentBoardControllerProvider")
  }
  return context
}

// Export the context so it can be accessed directly
export { AgentBoardControllerContext }
