"use client"

import type React from "react"
import { useState } from "react"
import { useDroppable } from "@dnd-kit/core"
import { MoreH<PERSON>zontal, PlusCircle, Trash2, Edit } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card" // This is fine as it's from ui/card
import { KanbanCard } from "./kanban-card"
import type { Card, Column, CardType, Agent } from "./kanban-board"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"

interface KanbanColumnProps {
  column: Column
  onAddCard: () => void
  onDeleteColumn: () => void
  onEditColumn: (newTitle: string) => void
  onCardUpdate?: (updatedCard: Card) => void
  onDeleteCard?: (cardId: string) => void // Add this line
  isBoardOver: boolean // Renamed from isOver to clarify it's from board state
  isBoardDragging: boolean // Renamed from isDragging
  cardTypes: CardType[]
  agents?: Agent[]
}

export function KanbanColumn({
  column,
  onAddCard,
  onDeleteColumn,
  onEditColumn,
  onCardUpdate,
  onDeleteCard, // Add this line
  isBoardOver, // Use this for board-level highlighting if needed
  isBoardDragging, // Use this for board-level dragging state
  cardTypes,
  agents,
}: KanbanColumnProps) {
  // Update the useDroppable hook to include the necessary data
  const { setNodeRef, isOver } = useDroppable({
    id: `column-${column.id}`, // Keep ID simple for non-swimlane columns
    data: {
      type: "column", // This type is for the main column view
      columnId: column.id,
    },
  })

  const [isEditing, setIsEditing] = useState(false)
  const [editTitle, setEditTitle] = useState(column.title)

  const getColumnBackground = () => {
    const title = column.title.toLowerCase()
    if (title.includes("backlog")) {
      return "bg-gradient-to-b from-gray-50/80 to-gray-100/50 dark:from-gray-900/30 dark:to-gray-800/20"
    }
    if (title.includes("ready")) {
      return "bg-gradient-to-b from-blue-50/80 to-blue-100/50 dark:from-blue-950/30 dark:to-blue-900/20"
    }
    if (title.includes("development")) {
      return "bg-gradient-to-b from-amber-50/80 to-amber-100/50 dark:from-amber-950/30 dark:to-amber-900/20"
    }
    if (title.includes("review")) {
      return "bg-gradient-to-b from-purple-50/80 to-purple-100/50 dark:from-purple-950/30 dark:to-purple-900/20"
    }
    if (title.includes("testing") || title.includes("qa")) {
      return "bg-gradient-to-b from-indigo-50/80 to-indigo-100/50 dark:from-indigo-950/30 dark:to-indigo-900/20"
    }
    if (title.includes("done") || title.includes("complete")) {
      return "bg-gradient-to-b from-green-50/80 to-green-100/50 dark:from-green-950/30 dark:to-green-900/20"
    }
    return "bg-gradient-to-b from-gray-50/80 to-gray-100/50 dark:from-gray-900/30 dark:to-gray-800/20"
  }

  const handleEditSave = () => {
    if (editTitle.trim()) {
      onEditColumn(editTitle)
      setIsEditing(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleEditSave()
    } else if (e.key === "Escape") {
      setEditTitle(column.title)
      setIsEditing(false)
    }
  }

  const handleCardUpdate = (updatedCard: Card) => {
    if (onCardUpdate) {
      onCardUpdate(updatedCard)
    }
  }

  // Use `isOver` from the local useDroppable hook for primary styling
  const showAsDropTarget = isOver && isBoardDragging

  return (
    <div
      className={`
    flex flex-col w-[280px] min-w-[280px] max-w-[280px] max-h-full rounded-md
    border border-[#e0e0e0] dark:border-[#3e3e42]
    ${getColumnBackground()}
    backdrop-blur-[2px]
    transition-all duration-150 ease-in-out
    ${showAsDropTarget ? "ring-2 ring-primary shadow-lg scale-[1.01]" : ""} 
    ${isBoardDragging && !showAsDropTarget ? "opacity-70" : ""} 
  `}
    >
      <CardHeader className="p-3 border-b border-[#e0e0e0] dark:border-[#3e3e42] flex flex-row items-center justify-between">
        {isEditing ? (
          <div className="flex items-center gap-1 w-full">
            <Input
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              onBlur={handleEditSave}
              onKeyDown={handleKeyDown}
              className="h-7 text-sm"
              autoFocus
            />
          </div>
        ) : (
          <div className="flex items-center justify-between w-full">
            <CardTitle className="text-sm font-medium flex items-center">
              {column.title}
              <span className="ml-2 text-xs text-muted-foreground">{column.cards.length}</span>
            </CardTitle>
            <div className="flex items-center">
              <Button variant="ghost" size="icon" className="h-7 w-7" onClick={onAddCard}>
                <PlusCircle className="h-4 w-4" />
                <span className="sr-only">Add card</span>
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-7 w-7">
                    <MoreHorizontal className="h-4 w-4" />
                    <span className="sr-only">More options</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[160px]">
                  <DropdownMenuItem onClick={() => setIsEditing(true)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Column
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={onDeleteColumn} className="text-destructive focus:text-destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Column
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        )}
      </CardHeader>
      {/* This is the actual droppable area for cards in this column */}
      <div ref={setNodeRef} className="flex flex-col gap-2 p-2 overflow-y-auto overflow-x-hidden flex-1 min-h-[60px]">
        {" "}
        {/* Added flex-1 and min-h */}
        {column.cards.map((card, index) => (
          <KanbanCard
            key={card.id}
            card={card}
            index={index}
            columnId={column.id} // Pass current columnId to card
            onCardUpdate={handleCardUpdate}
            onDeleteCard={onDeleteCard}
            cardTypes={cardTypes}
            // agents={agents} // Pass agents if KanbanCard uses it
          />
        ))}
        {column.cards.length === 0 && (
          <div
            className={`
            flex items-center justify-center h-20 rounded-md
            border-2 border-dashed 
            transition-all duration-200
            ${showAsDropTarget ? "border-primary/70 bg-primary/10 scale-105" : "border-muted-foreground/20"}
            ${isBoardDragging && !showAsDropTarget ? "border-muted-foreground/10 bg-muted/5" : ""}
          `}
          >
            <p className="text-xs text-muted-foreground">{showAsDropTarget ? "Drop here" : "No cards"}</p>
          </div>
        )}
      </div>
    </div>
  )
}
