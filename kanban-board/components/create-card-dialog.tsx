"use client"

import type React from "react"

import { Badge } from "@/components/ui/badge"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Plus, Trash2 } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { Checkbox } from "@/components/ui/checkbox"

interface CreateCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateCard: (card: any) => void
  cardTypes: any[]
  existingCards?: any[]
  agents?: any[]
}

export function CreateCardDialog({
  open,
  onOpenChange,
  onCreateCard,
  cardTypes,
  existingCards = [],
  agents = [],
}: CreateCardDialogProps) {
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [priority, setPriority] = useState<"feature" | "bug" | "tech-debt" | "enhancement">("feature")
  const [projectId, setProjectId] = useState("")
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined)
  const [assignee, setAssignee] = useState("")
  const [tags, setTags] = useState("")
  const [storyPoints, setStoryPoints] = useState<number | undefined>(undefined)
  const [subtasks, setSubtasks] = useState<{ id: string; title: string; completed: boolean }[]>([])
  const [newSubtask, setNewSubtask] = useState("")

  // Generate project ID based on priority
  const generateProjectId = () => {
    const cardType = cardTypes.find((type) => type.id === priority)
    const prefix = cardType
      ? cardType.name.substring(0, 4).toUpperCase()
      : priority === "feature"
        ? "FEAT"
        : priority === "bug"
          ? "BUG"
          : priority === "tech-debt"
            ? "TECH"
            : "ENH"

    const randomNum = Math.floor(Math.random() * 900) + 100 // 3-digit number
    return `${prefix}-${randomNum}`
  }

  const handleCreateCard = () => {
    if (title.trim()) {
      onCreateCard({
        title,
        description,
        priority,
        projectId: projectId || generateProjectId(),
        dueDate: dueDate ? dueDate.toISOString() : undefined,
        assignee: assignee || undefined,
        tags: tags
          .split(",")
          .map((tag) => tag.trim())
          .filter(Boolean),
        storyPoints: storyPoints,
        subtasks: subtasks.length > 0 ? subtasks : undefined,
        // New fields for AI agent integration
        agentAssignments: [],
        dependencies: [],
        resourceMetrics: {
          tokenUsage: 0,
          cpuTime: 0,
          memoryUsage: 0,
        },
      })

      // Reset form
      setTitle("")
      setDescription("")
      setPriority("feature")
      setProjectId("")
      setDueDate(undefined)
      setAssignee("")
      setTags("")
      setStoryPoints(undefined)
      setSubtasks([])
      setNewSubtask("")

      onOpenChange(false)
    }
  }

  const handleAddSubtask = () => {
    if (newSubtask.trim()) {
      setSubtasks([
        ...subtasks,
        {
          id: `subtask-${Date.now()}`,
          title: newSubtask.trim(),
          completed: false,
        },
      ])
      setNewSubtask("")
    }
  }

  const handleDeleteSubtask = (id: string) => {
    setSubtasks(subtasks.filter((subtask) => subtask.id !== id))
  }

  const handleToggleSubtask = (id: string) => {
    setSubtasks(
      subtasks.map((subtask) => (subtask.id === id ? { ...subtask, completed: !subtask.completed } : subtask)),
    )
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleAddSubtask()
    }
  }

  const developmentTags = [
    "react",
    "node",
    "api",
    "database",
    "ui",
    "ux",
    "performance",
    "security",
    "testing",
    "mobile",
    "auth",
    "refactor",
    "documentation",
  ]

  const storyPointOptions = [1, 2, 3, 5, 8, 13, 21]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add new card</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="grid items-center gap-2">
              <Label htmlFor="card-priority">Type</Label>
              <select
                id="card-priority"
                value={priority}
                onChange={(e) => {
                  setPriority(e.target.value as "feature" | "bug" | "tech-debt" | "enhancement")
                  // Auto-generate project ID when type changes
                  if (!projectId) {
                    setProjectId(generateProjectId())
                  }
                }}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {cardTypes.map((type) => (
                  <option key={type.id} value={type.id}>
                    {type.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="grid items-center gap-2">
              <Label htmlFor="project-id">ID</Label>
              <Input
                id="project-id"
                value={projectId}
                onChange={(e) => setProjectId(e.target.value)}
                placeholder={generateProjectId()}
              />
            </div>
          </div>

          <div className="grid items-center gap-2">
            <Label htmlFor="card-title">Title *</Label>
            <Input
              id="card-title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="e.g., Implement user authentication"
            />
          </div>

          <div className="grid items-center gap-2">
            <Label htmlFor="card-description">Description</Label>
            <Textarea
              id="card-description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe the task..."
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid items-center gap-2">
              <Label htmlFor="story-points">Story Points</Label>
              <select
                id="story-points"
                value={storyPoints?.toString() || ""}
                onChange={(e) => setStoryPoints(e.target.value ? Number.parseInt(e.target.value) : undefined)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">Select points</option>
                {storyPointOptions.map((points) => (
                  <option key={points} value={points.toString()}>
                    {points}
                  </option>
                ))}
              </select>
            </div>

            <div className="grid items-center gap-2">
              <Label htmlFor="card-due-date">Due Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn("w-full justify-start text-left font-normal", !dueDate && "text-muted-foreground")}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dueDate ? format(dueDate, "PPP") : "Select a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar mode="single" selected={dueDate} onSelect={setDueDate} initialFocus />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="grid items-center gap-2">
            <Label htmlFor="card-assignee">Assignee</Label>
            <Input
              id="card-assignee"
              value={assignee}
              onChange={(e) => setAssignee(e.target.value)}
              placeholder="e.g., John Doe"
            />
          </div>

          <div className="grid items-center gap-2">
            <Label htmlFor="card-tags">Tags (comma separated)</Label>
            <div className="relative">
              <Input
                id="card-tags"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                placeholder="e.g., react, api, auth"
              />
              <div className="mt-2 flex flex-wrap gap-1">
                {developmentTags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="outline"
                    className="cursor-pointer hover:bg-primary/10"
                    onClick={() => {
                      const currentTags = tags
                        .split(",")
                        .map((t) => t.trim())
                        .filter(Boolean)
                      if (!currentTags.includes(tag)) {
                        setTags(tags ? `${tags}, ${tag}` : tag)
                      }
                    }}
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Subtasks Section */}
          <div className="grid items-start gap-2">
            <Label htmlFor="subtasks">Subtasks</Label>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Input
                  id="new-subtask"
                  value={newSubtask}
                  onChange={(e) => setNewSubtask(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Add a subtask..."
                  className="flex-1"
                />
                <Button type="button" onClick={handleAddSubtask} size="sm" disabled={!newSubtask.trim()}>
                  <Plus className="h-4 w-4" />
                  Add
                </Button>
              </div>

              {subtasks.length > 0 ? (
                <div className="space-y-2 mt-2">
                  {subtasks.map((subtask) => (
                    <div key={subtask.id} className="flex items-center gap-2 group">
                      <Checkbox
                        id={subtask.id}
                        checked={subtask.completed}
                        onCheckedChange={() => handleToggleSubtask(subtask.id)}
                      />
                      <Label
                        htmlFor={subtask.id}
                        className={cn(
                          "text-sm flex-1 cursor-pointer",
                          subtask.completed && "line-through text-muted-foreground",
                        )}
                      >
                        {subtask.title}
                      </Label>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => handleDeleteSubtask(subtask.id)}
                      >
                        <Trash2 className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground italic">No subtasks added yet</div>
              )}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button type="submit" onClick={handleCreateCard} disabled={!title.trim()}>
            Create Card
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
