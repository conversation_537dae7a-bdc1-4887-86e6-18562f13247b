// src/preload/preload.ts
import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import * as events from 'events';

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld(
  'electron',
  {
    ipcRenderer: {
      send: (channel: string, data: any) => {
        // whitelist channels
        const validChannels = [
          'example-channel',
          'agent-system:toggle',
          'agent:pause',
          'agent:resume'
        ];
        if (validChannels.includes(channel)) {
          ipcRenderer.send(channel, data);
        }
      },
      invoke: async (channel: string, data: any) => {
        // whitelist channels
        const validChannels = [
          'ping',
          'fs:readDirectory',
          'fs:readFile',
          'fs:writeFile',
          'fs:createFile',
          'fs:createDirectory',
          'fs:openDirectory',
          'fs:delete',
          'fs:rename',
          'git:isRepo',
          'git:status',
          'git:stage',
          'git:unstage',
          'git:currentBranch',
          'git:commit',
          'terminal:create',
          'terminal:write',
          'terminal:close',
          'terminal:changeCwd',
          'agent:initialize',
          'agent:process',
          'agent:getAgents',
          'open-external',
          'board:get-state',
          'board:create-card',
          'board:update-card',
          'board:move-card',
          'board:delete-card',
          'board:assign-agent',
          'board:create-column',
          'board:update-column',
          'board:delete-column',
          'board:create-swimlane',
          'board:update-swimlane',
          'board:delete-swimlane',
          'board:toggle-swimlane-expansion',
          'board:update-card-types',
          'board:update-agents',
          'board:save-state',
          'board:load-state',
          'board:unassign-agent',
          'board:get-agent-status',
          'board:get-agent-resource-usage',
          'agent-system:toggle'
        ];
        if (validChannels.includes(channel)) {
          return await ipcRenderer.invoke(channel, data);
        }
        return null;
      },
      on: (channel: string, listener: (...args: any[]) => void) => {
        // Check if the channel is valid using string or regex pattern match
        const baseChannels = ['example-response'];
        const isChannelValid =
          baseChannels.includes(channel) ||
          channel.startsWith('terminal:data:') ||
          channel.startsWith('terminal:error:') ||
          channel.startsWith('terminal:exit:') ||
          channel.startsWith('board:agent-action') ||
          channel.startsWith('board:state-update') ||
          channel.startsWith('board:agent-status');

        if (isChannelValid) {
          // Wrap listener to handle event
          const subscription = (_event: any, ...args: any[]) => listener(...args);
          ipcRenderer.on(channel, subscription);

          // Return cleanup function
          return () => {
            ipcRenderer.removeListener(channel, subscription);
          };
        }

        return () => {}; // Empty cleanup function
      }
    },
    // Implement path utilities directly
    path: {
      basename: (filepath: string) => {
        const parts = filepath.split(/[/\\]/);
        return parts[parts.length - 1];
      },
      dirname: (filepath: string) => {
        const parts = filepath.split(/[/\\]/);
        return parts.slice(0, -1).join('/');
      },
      extname: (filepath: string) => {
        const parts = filepath.split('.');
        return parts.length > 1 ? '.' + parts[parts.length - 1].toLowerCase() : '';
      },
      join: (...paths: string[]) => {
        return paths.join('/').replace(/\/+/g, '/');
      }
    }
  }
);

// Expose the events module to the renderer process
contextBridge.exposeInMainWorld('external_node_commonjs_events', events);

// Also expose it as a global variable for compatibility
(window as any).external_node_commonjs_events = events;

// Expose a limited version of require for specific modules
contextBridge.exposeInMainWorld('nodeRequire', {
  // Add specific modules that need to be required
  path: require('path'),
  fs: require('fs'),
  events: require('events'),
  // Add any other modules needed by the renderer process
});