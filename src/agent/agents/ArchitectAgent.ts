// src/agent/agents/ArchitectAgent.ts
import { Agent, AgentConfig, AgentContext, AgentResponse } from '../base/Agent';
import { LLMService, LLMRequest } from '../services/LLMService';

export class ArchitectAgent extends Agent {
  protected llmService: LLMService;

  constructor(config: AgentConfig, llmService: LLMService) {
    super(config);
    this.llmService = llmService;
  }

  public async process(context: AgentContext): Promise<AgentResponse> {
    try {
      // Prepare context with system prompt specific to architect role
      const systemPrompt = this.getArchitectPrompt();
      const contextWithSystem = {
        ...context,
        systemPrompt
      };

      // Prepare the prompt with all relevant context
      const prompt = await this.preparePrompt(contextWithSystem);

      // Extract query and other context
      const { query, fileContent, projectPath } = context;

      // Prepare LLM request
      const request: LLMRequest = {
        model: this.getModel(),
        provider: this.getProvider(),
        messages: [
          { role: 'system', content: prompt },
          { role: 'user', content: this.buildUserMessage(query, fileContent, projectPath) }
        ]
      };

      // Call LLM service
      const response = await this.llmService.completion(request);

      // Process and return the response
      return {
        success: true,
        result: response.content,
        explanation: `Architecture design by ${this.getName()}`,
        agentId: this.getId()
      };

    } catch (error) {
      console.error(`${this.getName()} agent error:`, error);
      return {
        success: false,
        error: `${this.getName()} agent error: ${error instanceof Error ? error.message : String(error)}`,
        agentId: this.getId()
      };
    }
  }

  private getArchitectPrompt(): string {
    return `You are a senior Software Architect specialized in designing robust software systems.
Your primary responsibilities include:

1. Designing high-level system architecture and component relationships
2. Making key technical decisions that impact the overall application
3. Establishing patterns, practices, and standards for implementation
4. Breaking down complex problems into manageable components
5. Planning for scalability, performance, and maintainability

When designing architecture:
- Consider separation of concerns and modular design
- Balance pragmatism with future extensibility
- Recommend appropriate design patterns for specific use cases
- Consider trade-offs between different architectural approaches
- Pay special attention to interfaces between system components

Your responses should include diagrams (when appropriate), clear reasoning for decisions, and concrete implementation guidance.`;
  }

  private buildUserMessage(query?: string, fileContent?: string, projectPath?: string): string {
    let message = '';

    if (query) {
      message += `Architecture request: ${query}\n\n`;
    }

    if (fileContent) {
      message += `Current implementation:\n\`\`\`\n${fileContent}\n\`\`\`\n\n`;
    }

    if (projectPath) {
      message += `Project context: This is part of the project located at ${projectPath}.\n`;
    }

    return message || 'Please design an architecture for the described system.';
  }
}