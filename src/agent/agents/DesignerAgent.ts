// src/agent/agents/DesignerAgent.ts
import { Agent, AgentConfig, AgentContext, AgentResponse } from '../base/Agent';
import { LLMService, LLMRequest } from '../services/LLMService';

export class DesignerAgent extends Agent {
  protected llmService: LLMService;

  constructor(config: AgentConfig, llmService: LLMService) {
    super(config);
    this.llmService = llmService;
  }

  public async process(context: AgentContext): Promise<AgentResponse> {
    try {
      // Prepare context with system prompt specific to designer role
      const systemPrompt = this.getDesignerPrompt();
      const contextWithSystem = {
        ...context,
        systemPrompt
      };

      // Prepare the prompt with all relevant context
      const prompt = await this.preparePrompt(contextWithSystem);

      // Extract query and other context
      const { query, fileContent, projectPath } = context;

      // Prepare LLM request
      const request: LLMRequest = {
        model: this.getModel(),
        provider: this.getProvider(),
        messages: [
          { role: 'system', content: prompt },
          { role: 'user', content: this.buildUserMessage(query, fileContent, projectPath) }
        ]
      };

      // Call LLM service
      const response = await this.llmService.completion(request);

      // Process and return the response
      return {
        success: true,
        result: response.content,
        explanation: `UI/UX design by ${this.getName()}`,
        agentId: this.getId()
      };

    } catch (error) {
      console.error(`${this.getName()} agent error:`, error);
      return {
        success: false,
        error: `${this.getName()} agent error: ${error instanceof Error ? error.message : String(error)}`,
        agentId: this.getId()
      };
    }
  }

  private getDesignerPrompt(): string {
    return `You are a specialized UI/UX Designer Agent for software development.
Your primary responsibilities include:

1. Creating user interface elements following modern design principles
2. Ensuring brand compliance and visual consistency
3. Designing responsive layouts that work across different screen sizes
4. Implementing accessible UI components
5. Translating design requirements into actual component code

When designing UI/UX:
- Focus on user experience and intuitive interaction patterns
- Consider accessibility standards (WCAG)
- Ensure responsive design for different device sizes
- Follow established design systems and patterns
- Create clean, maintainable component code

Your responses should include concrete implementation details, such as HTML/CSS/React code,
design considerations, and clear explanations of your design decisions.`;
  }

  private buildUserMessage(query?: string, fileContent?: string, projectPath?: string): string {
    let message = '';

    if (query) {
      message += `Design request: ${query}\n\n`;
    }

    if (fileContent) {
      message += `Current implementation:\n\`\`\`\n${fileContent}\n\`\`\`\n\n`;
    }

    if (projectPath) {
      message += `Project context: This is part of the project located at ${projectPath}.\n`;
    }

    return message || 'Please provide a UI/UX design for the described component.';
  }
}