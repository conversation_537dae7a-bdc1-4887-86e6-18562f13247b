// src/agent/agents/MicromanagerAgent.ts
import { Agent, AgentConfig, AgentContext, AgentResponse } from '../base/Agent';
import { LLMService, LLMRequest } from '../services/LLMService';
import { AgentManager } from '../AgentManager';

export class MicromanagerAgent extends Agent {
  private llmService: LLMService;
  private agentManager: AgentManager;

  constructor(config: AgentConfig, llmService: LLMService, agentManager: AgentManager) {
    super(config);
    this.llmService = llmService;
    this.agentManager = agentManager;
  }

  public async process(context: AgentContext): Promise<AgentResponse> {
    try {
      // 1. Prepare the context with system prompt
      const systemPrompt = this.getSystemPrompt();
      const contextWithSystem = {
        ...context,
        systemPrompt
      };

      // 2. Determine which agent(s) should handle this request
      const agentSelectionResult = await this.selectAgents(contextWithSystem);

      if (!agentSelectionResult.success) {
        return {
          success: false,
          error: 'Failed to select appropriate agents',
          explanation: agentSelectionResult.explanation
        };
      }

      // 3. Delegate to selected agent
      const selectedAgentId = agentSelectionResult.selectedAgent;
      const agent = this.agentManager.getAgent(selectedAgentId);

      if (!agent) {
        return {
          success: false,
          error: `Selected agent ${selectedAgentId} not found`
        };
      }

      // 4. Process with selected agent
      return await agent.process(context);

    } catch (error) {
      console.error('Micromanager agent error:', error);
      return {
        success: false,
        error: `Micromanager agent error: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  private getSystemPrompt(): string {
    return `You are the Micromanager, responsible for coordinating between specialized coding agents.
Your job is to:
1. Understand the user request
2. Determine which specialized agent is best suited to handle it
3. Delegate the task appropriately

Available agents:
${this.getAvailableAgentsDescription()}

You should evaluate each request and determine the most suitable agent based on:
- Task complexity
- Required expertise
- Type of task (implementation, research, architecture, etc.)`;
  }

  private getAvailableAgentsDescription(): string {
    const agents = this.agentManager.getAllAgents();
    return agents
      .filter(agent => agent.getId() !== 'micromanager')
      .map(agent => `- ${agent.getName()}: ${agent.getDescription()}`)
      .join('\n');
  }

  private async selectAgents(context: AgentContext): Promise<{
    success: boolean;
    selectedAgent: string;
    explanation?: string;
  }> {
    try {
      const prompt = await this.preparePrompt(context);

      const request: LLMRequest = {
        model: this.getModel(),
        provider: this.getProvider(),
        messages: [
          { role: 'system', content: prompt },
          { role: 'user', content: context.query || 'Help me with my code' }
        ]
      };

      const response = await this.llmService.completion(request);

      // Parse the response to determine which agent to use
      // This is a simplified implementation
      const responseText = response.content.toLowerCase();

      // Very basic agent selection logic based on keywords
      if (responseText.includes('intern') || responseText.includes('boilerplate')) {
        return { success: true, selectedAgent: 'intern', explanation: response.content };
      } else if (responseText.includes('junior') || responseText.includes('basic')) {
        return { success: true, selectedAgent: 'junior', explanation: response.content };
      } else if (responseText.includes('mid') || responseText.includes('moderate')) {
        return { success: true, selectedAgent: 'mid-level', explanation: response.content };
      } else if (responseText.includes('senior') || responseText.includes('complex')) {
        return { success: true, selectedAgent: 'senior', explanation: response.content };
      } else if (responseText.includes('research')) {
        return { success: true, selectedAgent: 'researcher', explanation: response.content };
      } else if (responseText.includes('architect')) {
        return { success: true, selectedAgent: 'architect', explanation: response.content };
      } else {
        // Default to junior if no clear match
        return { success: true, selectedAgent: 'junior', explanation: 'No clear agent match, defaulting to Junior' };
      }

    } catch (error) {
      console.error('Agent selection error:', error);
      return {
        success: false,
        selectedAgent: 'junior', // Default
        explanation: `Error during agent selection: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }
}