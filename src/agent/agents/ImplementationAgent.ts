// src/agent/agents/ImplementationAgent.ts
import { Agent, AgentConfig, AgentContext, AgentResponse } from '../base/Agent';
import { LLMService, LLMRequest } from '../services/LLMService';

export abstract class ImplementationAgent extends Agent {
  protected llmService: LLMService;
  protected level: 'intern' | 'junior' | 'mid-level' | 'senior';

  constructor(config: AgentConfig, llmService: LLMService, level: 'intern' | 'junior' | 'mid-level' | 'senior') {
    super(config);
    this.llmService = llmService;
    this.level = level;
  }

  public async process(context: AgentContext): Promise<AgentResponse> {
    try {
      // Prepare context with system prompt specific to this agent level
      const systemPrompt = this.getLevelSpecificPrompt();
      const contextWithSystem = {
        ...context,
        systemPrompt
      };

      // Prepare the prompt with all relevant context
      const prompt = await this.preparePrompt(contextWithSystem);

      // Extract file content and query
      const { fileContent, query } = context;

      // Prepare LLM request
      const request: LLMRequest = {
        model: this.getModel(),
        provider: this.getProvider(),
        messages: [
          { role: 'system', content: prompt },
          { role: 'user', content: fileContent ? `File content:\n\`\`\`\n${fileContent}\n\`\`\`\n\nQuery: ${query}` : (query || 'Help me with my code') }
        ]
      };

      // Call LLM service
      const response = await this.llmService.completion(request);

      // Process and return the response
      return {
        success: true,
        result: response.content,
        explanation: `Processed with ${this.getName()} (${this.level} level)`
      };

    } catch (error) {
      console.error(`${this.level} agent error:`, error);
      return {
        success: false,
        error: `${this.level} agent error: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  protected getLevelSpecificPrompt(): string {
    // Base prompt for all implementation agents
    let prompt = `You are an ${this.level} level developer AI assistant.`;

    // Add level-specific behavior guidance
    switch (this.level) {
      case 'intern':
        prompt += `
As an intern-level developer, you:
- Focus on creating boilerplate code
- Implement simple, well-defined tasks
- Create basic file structures and scaffolding
- Follow established patterns exactly
- Keep implementations minimal and to the point
- Ask for clarification when requirements are unclear`;
        break;

      case 'junior':
        prompt += `
As a junior-level developer, you:
- Implement straightforward business logic
- Create standard UI components
- Handle simple data processing
- Follow established patterns with minor adaptations
- Add appropriate error handling
- Document your code with basic comments`;
        break;

      case 'mid-level':
        prompt += `
As a mid-level developer, you:
- Implement moderately complex features
- Handle more complex business logic
- Integrate components and services
- Optimize performance for standard operations
- Ensure code maintainability and readability
- Provide comprehensive error handling
- Document code with detailed comments`;
        break;

      case 'senior':
        prompt += `
As a senior-level developer, you:
- Implement complex algorithms and architecture
- Handle advanced integration challenges
- Solve difficult edge cases
- Create high-performance solutions
- Design scalable and maintainable code
- Consider security implications
- Create comprehensive documentation`;
        break;
    }

    return prompt;
  }

  protected override async preparePrompt(context: AgentContext): Promise<string> {
    const basePrompt = await super.preparePrompt(context);

    // Add file context if available
    if (context.fileContent) {
      return `${basePrompt}\n\nYou are working with the following file content:\n\`\`\`\n${context.fileContent}\n\`\`\``;
    }

    return basePrompt;
  }
}