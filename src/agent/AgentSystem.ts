// src/agent/AgentSystem.ts
import { AgentManager } from './AgentManager';
import { LLMService } from './services/LLMService';
import { contextManagementService } from './services/ContextManagementService';
import { MicromanagerAgent } from './agents/MicromanagerAgent';
import { InternAgent } from './agents/InternAgent';
import { JuniorAgent } from './agents/JuniorAgent';
import { MidLevelAgent } from './agents/MidLevelAgent';
import { SeniorAgent } from './agents/SeniorAgent';
import { ResearcherAgent } from './agents/ResearcherAgent';
import { ArchitectAgent } from './agents/ArchitectAgent';
import { TesterAgent } from './agents/TesterAgent';
import { DesignerAgent } from './agents/DesignerAgent';
import { AgentContext, AgentResponse } from './base/Agent';
import { v4 as uuidv4 } from 'uuid';

export class AgentSystem {
  private agentManager: AgentManager;
  private llmService: LLMService;
  private initialized: boolean = false;
  private sessionIds: Map<string, string> = new Map();

  constructor() {
    this.agentManager = new AgentManager();
    this.llmService = new LLMService();
  }

  public async initialize(apiKeys?: Record<string, string>, selectedModels?: Record<string, string>): Promise<void> {
    if (apiKeys) {
      // Set API keys for each provider
      Object.entries(apiKeys).forEach(([provider, key]) => {
        if (key) {
          this.llmService.setApiKey(provider, key);
        }
      });

      // Set default provider based on available keys
      if (apiKeys.openai) {
        this.llmService.setDefaultProvider('openai');
      } else if (apiKeys.anthropic) {
        this.llmService.setDefaultProvider('anthropic');
      } else if (apiKeys.gemini) {
        this.llmService.setDefaultProvider('gemini');
      }
    }

    // Update default models if provided
    if (selectedModels) {
      Object.entries(selectedModels).forEach(([provider, model]) => {
        if (model) {
          this.llmService.setDefaultModel(provider, model);
        }
      });
    }

    // Create and register agents
    this.registerAllAgents();

    this.initialized = true;
  }

  public isInitialized(): boolean {
    return this.initialized;
  }

  public getAgentManager(): AgentManager {
    return this.agentManager;
  }

  public getLLMService(): LLMService {
    return this.llmService;
  }

  public async processQuery(context: AgentContext): Promise<AgentResponse> {
    if (!this.initialized) {
      return {
        success: false,
        error: 'Agent system not initialized'
      };
    }

    // Generate or retrieve a session ID for this context
    const projectPath = context.projectPath || 'default';
    if (!this.sessionIds.has(projectPath)) {
      this.sessionIds.set(projectPath, uuidv4());
    }
    const sessionId = this.sessionIds.get(projectPath)!;

    // Merge with previous context if available
    const mergedContext = contextManagementService.mergeWithPrevious(sessionId, context);

    // Store this context for future use
    contextManagementService.storeContext(sessionId, mergedContext);

    return await this.agentManager.processWithMicromanager(mergedContext);
  }

  private registerAllAgents(): void {
    // Register Micromanager (unchanged)
    const micromanager = new MicromanagerAgent(
      {
        id: 'micromanager',
        name: 'Micromanager',
        description: 'Coordinates between specialized agents',
        capabilities: ['task classification', 'agent selection', 'task delegation'],
        model: 'gpt-4',
        provider: 'openai'
      },
      this.llmService,
      this.agentManager
    );
    this.agentManager.registerAgent(micromanager);

    // Register implementation agents (unchanged)
    const intern = new InternAgent(
      {
        id: 'intern',
        name: 'Intern',
        description: 'Handles boilerplate code and simple tasks',
        capabilities: ['boilerplate generation', 'simple implementations', 'scaffolding'],
        model: 'gpt-3.5-turbo',
        provider: 'openai'
      },
      this.llmService
    );
    this.agentManager.registerAgent(intern);

    const junior = new JuniorAgent(
      {
        id: 'junior',
        name: 'Junior Developer',
        description: 'Implements straightforward business logic and UI components',
        capabilities: ['basic logic implementation', 'standard UI components', 'simple data processing'],
        model: 'gpt-3.5-turbo',
        provider: 'openai'
      },
      this.llmService
    );
    this.agentManager.registerAgent(junior);

    const midLevel = new MidLevelAgent(
      {
        id: 'mid-level',
        name: 'Mid-Level Developer',
        description: 'Handles moderately complex features and integrations',
        capabilities: ['complex logic', 'component integration', 'performance optimization'],
        model: 'gpt-3.5-turbo',
        provider: 'openai'
      },
      this.llmService
    );
    this.agentManager.registerAgent(midLevel);

    const senior = new SeniorAgent(
      {
        id: 'senior',
        name: 'Senior Developer',
        description: 'Implements complex algorithms and architecture',
        capabilities: ['complex algorithms', 'advanced integration', 'high-performance solutions'],
        model: 'gpt-4',
        provider: 'openai'
      },
      this.llmService
    );
    this.agentManager.registerAgent(senior);

    // Add Researcher agent
    const researcher = new ResearcherAgent(
      {
        id: 'researcher',
        name: 'Researcher',
        description: 'Analyzes code and researches implementation approaches',
        capabilities: ['code analysis', 'pattern recognition', 'knowledge exploration', 'best practices research'],
        model: 'claude-3-opus-20240229',
        provider: 'anthropic'
      },
      this.llmService
    );
    this.agentManager.registerAgent(researcher);

    // Add Architect agent
    const architect = new ArchitectAgent(
      {
        id: 'architect',
        name: 'Architect',
        description: 'Designs high-level system architecture',
        capabilities: ['system design', 'technical decision-making', 'pattern establishment', 'interface design'],
        model: 'gpt-4',
        provider: 'openai'
      },
      this.llmService
    );
    this.agentManager.registerAgent(architect);

    // Add Designer agent
    const designer = new DesignerAgent(
      {
        id: 'designer',
        name: 'Designer',
        description: 'Creates UI/UX elements and components',
        capabilities: ['UI design', 'UX patterns', 'component styling', 'accessibility', 'responsive design'],
        model: 'gpt-4',
        provider: 'openai'
      },
      this.llmService
    );
    this.agentManager.registerAgent(designer);

    // Add Tester agent
    const tester = new TesterAgent(
      {
        id: 'tester',
        name: 'Tester',
        description: 'Creates tests and performs QA',
        capabilities: ['test case creation', 'quality assurance', 'validation', 'bug identification'],
        model: 'gpt-4',
        provider: 'openai'
      },
      this.llmService
    );
    this.agentManager.registerAgent(tester);
  }
}

// Export a singleton instance
export const agentSystem = new AgentSystem();