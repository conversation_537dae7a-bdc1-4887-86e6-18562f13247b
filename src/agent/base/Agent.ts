// src/agent/base/Agent.ts
export interface AgentConfig {
    id: string;
    name: string;
    description: string;
    capabilities: string[];
    model?: string;
    provider?: string;
  }

  export interface AgentContext {
    projectPath?: string;
    currentFile?: string;
    relatedFiles?: string[];
    fileContent?: string;
    query?: string;
    history?: Array<{role: string, content: string}>;
    systemPrompt?: string;
    provider?: string;
    model?: string;
  }

  export interface AgentResponse {
    success: boolean;
    result?: string;
    error?: string;
    explanation?: string;
    files?: Array<{path: string, content: string}>;
    changes?: Array<{
      start: number;
      end: number;
      content: string;
    }>;
    agentId?: string;
  }

  export abstract class Agent {
    protected config: AgentConfig;

    constructor(config: AgentConfig) {
      this.config = config;
    }

    public getId(): string {
      return this.config.id;
    }

    public getName(): string {
      return this.config.name;
    }

    public getDescription(): string {
      return this.config.description;
    }

    public getCapabilities(): string[] {
      return this.config.capabilities;
    }

    public getModel(): string {
      return this.config.model || 'default';
    }

    protected getProvider(): string {
      return this.config.provider || 'openai';
    }

    public abstract process(context: AgentContext): Promise<AgentResponse>;

    protected async preparePrompt(context: AgentContext): Promise<string> {
      // Base implementation for prompt preparation
      const { query, systemPrompt } = context;
      let prompt = systemPrompt || '';

      if (query) {
        prompt += `\n\nUser query: ${query}`;
      }

      return prompt;
    }
  }