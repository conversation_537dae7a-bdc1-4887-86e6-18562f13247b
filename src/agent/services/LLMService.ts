// src/agent/services/LLMService.ts
export interface LLMRequest {
    model: string;
    provider?: string;
    messages: Array<{role: string, content: string}>;
    temperature?: number;
    maxTokens?: number;
  }

  export interface LLMResponse {
    content: string;
    usage?: {
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
    };
  }

  export class LLMService {
    private apiKeys: Map<string, string> = new Map();
    private baseUrls: Map<string, string> = new Map();
    private defaultProvider: string = 'openai';
    private defaultModels: Map<string, string> = new Map();

    constructor() {
      // Initialize with default providers
      this.baseUrls.set('openai', 'https://api.openai.com/v1/chat/completions');
      this.baseUrls.set('anthropic', 'https://api.anthropic.com/v1/messages');
      this.baseUrls.set('gemini', 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent');

      // Initialize with default models
      this.defaultModels.set('openai', 'gpt-4');
      this.defaultModels.set('anthropic', 'claude-3-opus-20240229');
      this.defaultModels.set('gemini', 'gemini-pro');
    }

    public setApiKey(provider: string, apiKey: string): void {
      this.apiKeys.set(provider, apiKey);
    }

    public setBaseUrl(provider: string, url: string): void {
      this.baseUrls.set(provider, url);
    }

    public setDefaultProvider(provider: string): void {
      this.defaultProvider = provider;
    }

    public setDefaultModel(provider: string, model: string): void {
      this.defaultModels.set(provider, model);
    }

    public async completion(request: LLMRequest, retryCount = 0): Promise<LLMResponse> {
      const provider = request.provider || this.defaultProvider;
      const apiKey = this.apiKeys.get(provider);
      const baseUrl = this.baseUrls.get(provider);
      const maxRetries = 3;

      // Use default model if not specified in the request
      if (!request.model && this.defaultModels.has(provider)) {
        request.model = this.defaultModels.get(provider)!;
      }

      if (!apiKey) {
        throw new Error(`API key not set for provider: ${provider}`);
      }

      if (!baseUrl) {
        throw new Error(`Base URL not configured for provider: ${provider}`);
      }

      try {
        // Determine which completion method to use based on provider
        switch (provider) {
          case 'anthropic':
            return await this.anthropicCompletion(apiKey, baseUrl, request);
          case 'gemini':
            return await this.geminiCompletion(apiKey, baseUrl, request);
          case 'openai':
          default:
            return await this.openaiCompletion(apiKey, baseUrl, request);
        }
      } catch (error) {
        console.error(`LLM API error (${provider}):`, error);

        // Add retry logic for recoverable errors
        if (retryCount < maxRetries && this.isRecoverableError(error)) {
          console.log(`Retrying request to ${provider} (attempt ${retryCount + 1}/${maxRetries})...`);
          // Exponential backoff
          const delay = Math.pow(2, retryCount) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
          return this.completion(request, retryCount + 1);
        }

        throw error;
      }
    }

    // Helper to determine if an error is recoverable
    private isRecoverableError(error: any): boolean {
      // Network errors and rate limit errors are typically recoverable
      if (error instanceof TypeError || error.message?.includes('network')) {
        return true;
      }

      // Check for rate limiting or server errors
      if (error.status) {
        const status = typeof error.status === 'number' ? error.status : parseInt(error.status);
        // 429 = Rate limit, 5xx = Server errors
        return status === 429 || (status >= 500 && status < 600);
      }

      return false;
    }

    private async openaiCompletion(apiKey: string, baseUrl: string, request: LLMRequest): Promise<LLMResponse> {
      try {
        const response = await fetch(baseUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify({
            model: request.model,
            messages: request.messages,
            temperature: request.temperature || 0.7,
            max_tokens: request.maxTokens || 2048
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`LLM API error: ${errorData.error?.message || response.statusText}`);
        }

        const data = await response.json();

        return {
          content: data.choices[0].message.content,
          usage: {
            promptTokens: data.usage.prompt_tokens,
            completionTokens: data.usage.completion_tokens,
            totalTokens: data.usage.total_tokens
          }
        };
      } catch (error) {
        console.error('LLM API error:', error);
        throw error;
      }
    }

    private async anthropicCompletion(apiKey: string, baseUrl: string, request: LLMRequest): Promise<LLMResponse> {
      try {
        // Convert the messages format to Anthropic's expected format
        const systemPrompt = request.messages.find(m => m.role === 'system')?.content || '';
        const userMessages = request.messages.filter(m => m.role !== 'system');

        // Anthropic requires at least one user message
        if (userMessages.length === 0) {
          throw new Error('Anthropic requires at least one user message');
        }

        const response = await fetch(baseUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': apiKey,
            'anthropic-version': '2023-06-01'
          },
          body: JSON.stringify({
            model: request.model,
            system: systemPrompt,
            messages: userMessages,
            max_tokens: request.maxTokens || 2048,
            temperature: request.temperature || 0.7
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Anthropic API error: ${errorData.error?.message || response.statusText}`);
        }

        const data = await response.json();

        return {
          content: data.content[0].text,
          usage: data.usage || {
            promptTokens: 0,
            completionTokens: 0,
            totalTokens: 0
          }
        };
      } catch (error) {
        console.error('Anthropic API error:', error);
        throw error;
      }
    }

    // Add support for Google's Gemini models
    private async geminiCompletion(apiKey: string, baseUrl: string, request: LLMRequest): Promise<LLMResponse> {
      try {
        // Format for Gemini API
        const systemPrompt = request.messages.find(m => m.role === 'system')?.content || '';
        const userMessages = request.messages.filter(m => m.role !== 'system');

        const formattedMessages = userMessages.map(msg => ({
          role: msg.role === 'assistant' ? 'model' : msg.role,
          parts: [{ text: msg.content }]
        }));

        // Add system prompt as a prefix to the first user message if it exists
        if (systemPrompt && formattedMessages.length > 0 && formattedMessages[0].role === 'user') {
          formattedMessages[0].parts[0].text = `${systemPrompt}\n\n${formattedMessages[0].parts[0].text}`;
        }

        const response = await fetch(baseUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-goog-api-key': apiKey
          },
          body: JSON.stringify({
            model: request.model,
            contents: formattedMessages,
            generationConfig: {
              temperature: request.temperature || 0.7,
              maxOutputTokens: request.maxTokens || 2048,
            }
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Gemini API error: ${errorData.error?.message || response.statusText}`);
        }

        const data = await response.json();

        return {
          content: data.candidates[0].content.parts[0].text,
          usage: {
            promptTokens: data.usageMetadata?.promptTokenCount || 0,
            completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
            totalTokens: (data.usageMetadata?.promptTokenCount || 0) + (data.usageMetadata?.candidatesTokenCount || 0)
          }
        };
      } catch (error) {
        console.error('Gemini API error:', error);
        throw error;
      }
    }

    // Mock method for testing without API
    public async mockCompletion(request: LLMRequest): Promise<LLMResponse> {
      return {
        content: `This is a mock response for ${request.model} with ${request.messages.length} messages`,
        usage: {
          promptTokens: 10,
          completionTokens: 20,
          totalTokens: 30
        }
      };
    }
  }