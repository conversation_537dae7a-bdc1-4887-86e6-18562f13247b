// src/agent/services/ContextManagementService.ts
import { AgentContext } from '../base/Agent';

export class ContextManagementService {
  private contextHistory: Map<string, AgentContext[]> = new Map();
  private readonly MAX_HISTORY_LENGTH = 10;

  // Store context for a specific session
  public storeContext(sessionId: string, context: AgentContext): void {
    if (!this.contextHistory.has(sessionId)) {
      this.contextHistory.set(sessionId, []);
    }

    const history = this.contextHistory.get(sessionId)!;
    history.push(context);

    // Limit history length
    if (history.length > this.MAX_HISTORY_LENGTH) {
      history.shift();
    }
  }

  // Get previous context for a session
  public getContextHistory(sessionId: string): AgentContext[] {
    return this.contextHistory.get(sessionId) || [];
  }

  // Get the latest context for a session
  public getLatestContext(sessionId: string): AgentContext | null {
    const history = this.contextHistory.get(sessionId);
    if (!history || history.length === 0) {
      return null;
    }
    return history[history.length - 1];
  }

  // Clear context for a session
  public clearContext(sessionId: string): void {
    this.contextHistory.delete(sessionId);
  }

  // Merge the current context with the previous one
  public mergeWithPrevious(sessionId: string, currentContext: AgentContext): AgentContext {
    const latestContext = this.getLatestContext(sessionId);
    if (!latestContext) {
      return currentContext;
    }

    return {
      ...latestContext,
      ...currentContext,
      // Merge history if both have it
      history: [
        ...(latestContext.history || []),
        ...(currentContext.history || [])
      ].slice(-10) // Keep only last 10 interactions
    };
  }
}

// Export a singleton instance
export const contextManagementService = new ContextManagementService();