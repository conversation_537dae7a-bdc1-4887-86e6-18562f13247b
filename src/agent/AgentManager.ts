// src/agent/AgentManager.ts
import { Agent, AgentContext, AgentResponse } from './base/Agent';

export class AgentManager {
  private agents: Map<string, Agent> = new Map();
  private micromanager: Agent | null = null;
  
  constructor() {}
  
  public registerAgent(agent: Agent): void {
    this.agents.set(agent.getId(), agent);
    
    // If this is the micromanager agent, store it separately
    if (agent.getId() === 'micromanager') {
      this.micromanager = agent;
    }
  }
  
  public getAgent(id: string): Agent | undefined {
    return this.agents.get(id);
  }
  
  public getAllAgents(): Agent[] {
    return Array.from(this.agents.values());
  }
  
  public async processWithMicromanager(context: AgentContext): Promise<AgentResponse> {
    if (!this.micromanager) {
      return {
        success: false,
        error: 'Micromanager agent not registered'
      };
    }
    
    return await this.micromanager.process(context);
  }
  
  public async processWithAgent(agentId: string, context: AgentContext): Promise<AgentResponse> {
    const agent = this.agents.get(agentId);
    
    if (!agent) {
      return {
        success: false,
        error: `Agent ${agentId} not found`
      };
    }
    
    return await agent.process(context);
  }
}