// src/main/file-operations.ts
import { ipcMain, dialog } from 'electron';
import * as fs from 'fs';
import * as path from 'path';

export function setupFileOperations() {
  // Open directory
  ipcMain.handle('fs:openDirectory', async () => {
    try {
      const result = await dialog.showOpenDialog({
        properties: ['openDirectory']
      });
      
      if (result.canceled) {
        return { success: false, canceled: true };
      }
      
      const dirPath = result.filePaths[0];
      return { success: true, path: dirPath };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });

  // Read directory
  ipcMain.handle('fs:readDirectory', async (_, { dirPath }) => {
    try {
      const files = await fs.promises.readdir(dirPath, { withFileTypes: true });
      
      const fileList = files.map(file => ({
        name: file.name,
        path: path.join(dirPath, file.name),
        type: file.isDirectory() ? 'directory' : 'file'
      }));
      
      // Sort directories first, then files
      fileList.sort((a, b) => {
        if (a.type === b.type) {
          return a.name.localeCompare(b.name);
        }
        return a.type === 'directory' ? -1 : 1;
      });
      
      return { success: true, files: fileList };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });

  // Read file
  ipcMain.handle('fs:readFile', async (_, { filePath }) => {
    try {
      const content = await fs.promises.readFile(filePath, 'utf-8');
      return { success: true, content };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });

  // Write file
  ipcMain.handle('fs:writeFile', async (_, { filePath, content }) => {
    try {
      await fs.promises.writeFile(filePath, content, 'utf-8');
      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });

  // Create new file
  ipcMain.handle('fs:createFile', async (_, { filePath, content = '' }) => {
    try {
      // Check if file already exists
      if (fs.existsSync(filePath)) {
        return { success: false, error: 'File already exists' };
      }
      
      // Create file with initial content
      await fs.promises.writeFile(filePath, content, 'utf-8');
      return { success: true, path: filePath };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });

  // Create new directory
  ipcMain.handle('fs:createDirectory', async (_, { dirPath }) => {
    try {
      // Check if directory already exists
      if (fs.existsSync(dirPath)) {
        return { success: false, error: 'Directory already exists' };
      }
      
      // Create directory
      await fs.promises.mkdir(dirPath, { recursive: true });
      return { success: true, path: dirPath };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });

  // Delete file or directory
  ipcMain.handle('fs:delete', async (_, { path: itemPath }) => {
    try {
      const stats = await fs.promises.stat(itemPath);
      
      if (stats.isDirectory()) {
        await fs.promises.rm(itemPath, { recursive: true });
      } else {
        await fs.promises.unlink(itemPath);
      }
      
      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });

  // Rename file or directory
  ipcMain.handle('fs:rename', async (_, { oldPath, newPath }) => {
    try {
      // Check if target already exists
      if (fs.existsSync(newPath)) {
        return { success: false, error: 'Target already exists' };
      }
      
      await fs.promises.rename(oldPath, newPath);
      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });
}