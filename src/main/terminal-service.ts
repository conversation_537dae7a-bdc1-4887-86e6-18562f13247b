// src/main/terminal-service.ts
import { ipcMain, app } from 'electron';
import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';

// Store active terminal processes
const terminals: Record<string, any> = {};

// Track terminal status
const terminalStatus: Record<string, { active: boolean, pendingWrites: number }> = {};

// Add proper shell detection for different platforms
function getDefaultShell(): { shell: string, args: string[] } {
  if (process.platform === 'win32') {
    // Windows - prefer PowerShell if available, fall back to cmd.exe
    const powerShellPath = process.env.COMSPEC || 'powershell.exe';
    if (fs.existsSync(powerShellPath)) {
      return { shell: powerShellPath, args: [] };
    }
    return { shell: 'cmd.exe', args: [] };
  } else if (process.platform === 'darwin') {
    // macOS - use default shell or fall back to /bin/zsh
    return { shell: process.env.SHELL || '/bin/zsh', args: ['-l'] };
  } else {
    // Linux - use default shell or fall back to /bin/bash
    return { shell: process.env.SHELL || '/bin/bash', args: ['-l'] };
  }
}

export function setupTerminalService() {
  // Create a new terminal process
  ipcMain.handle('terminal:create', (event, { id, cwd }) => {
    try {
      // Validate the current working directory
      let workingDirectory = cwd || os.homedir();

      try {
        const stats = fs.statSync(workingDirectory);
        if (!stats.isDirectory()) {
          console.warn(`Provided path is not a directory: ${workingDirectory}`);
          workingDirectory = os.homedir();
        }
      } catch (err) {
        console.warn(`Invalid working directory: ${workingDirectory}`, err);
        workingDirectory = os.homedir();
      }

      // Use a safer logging approach
      try {
        if (process.stdout.writable) {
          console.log(`Terminal ${id} creating with cwd: ${workingDirectory}`);
        }
      } catch (logError) {
        // Silently ignore logging errors
      }

      // Get the appropriate shell for the platform
      const { shell, args } = getDefaultShell();

      // Use a safer logging approach
      try {
        if (process.stdout.writable) {
          console.log(`Using shell: ${shell} ${args.join(' ')}`);
        }
      } catch (logError) {
        // Silently ignore logging errors
      }

      // Create proper pty based on platform
      let term;
      if (process.platform === 'win32') {
        // Use node-pty with appropriate Windows settings
        const pty = require('node-pty');
        term = pty.spawn(shell, args, {
          name: 'xterm-256color',
          cols: 80,
          rows: 30,
          cwd: workingDirectory,
          env: process.env,
          useConpty: true // Use ConPTY on Windows 10+
        });
      } else {
        // Use node-pty for Unix platforms
        const pty = require('node-pty');
        term = pty.spawn(shell, args, {
          name: 'xterm-256color',
          cols: 80,
          rows: 30,
          cwd: workingDirectory,
          env: process.env
        });
      }

      // Store the terminal instance
      terminals[id] = term;

      // Initialize terminal status
      terminalStatus[id] = { active: true, pendingWrites: 0 };

      // Handle data events
      term.onData((data: string) => {
        try {
          if (!event.sender.isDestroyed()) {
            event.sender.send(`terminal:data:${id}`, data);
          }
        } catch (error) {
          console.error(`Error sending terminal data for ${id}:`, error);
        }
      });

      // Handle exit events
      term.onExit(({ exitCode, signal }: { exitCode: number; signal?: number }) => {
        // Use process.nextTick to avoid immediate console logging
        // This helps prevent EIO errors when the terminal is exiting
        process.nextTick(() => {
          try {
            // Only log if the process is still running
            if (process.stdout.writable) {
              console.log(`Terminal ${id} exited with code: ${exitCode}, signal: ${signal}`);
            }
          } catch (logError) {
            // Silently ignore logging errors
          }
        });

        // Mark terminal as inactive
        if (terminalStatus[id]) {
          terminalStatus[id].active = false;
        }

        try {
          if (!event.sender.isDestroyed()) {
            event.sender.send(`terminal:exit:${id}`, { code: exitCode, signal });
          }
        } catch (error) {
          // Silently ignore IPC errors during exit
        } finally {
          // Clean up resources
          delete terminals[id];

          // Wait for any pending writes to complete before removing status
          setTimeout(() => {
            delete terminalStatus[id];
          }, 1000);
        }
      });

      return { success: true, id, cwd: workingDirectory };
    } catch (error) {
      console.error('Failed to create terminal:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  // Write data to terminal
  ipcMain.handle('terminal:write', (_, { id, data }) => {
    try {
      // Check if terminal exists
      if (!terminals[id]) {
        console.error(`Terminal ${id} not found for writing`);
        return { success: false, error: 'Terminal not found' };
      }

      // Check if terminal is active
      if (terminalStatus[id] && !terminalStatus[id].active) {
        console.warn(`Attempted to write to inactive terminal: ${id}`);
        return { success: false, error: 'Terminal is no longer active' };
      }

      // Track pending writes
      if (terminalStatus[id]) {
        terminalStatus[id].pendingWrites++;
      }

      // Use a safer logging approach
      try {
        if (process.stdout.writable) {
          console.log(`Writing to terminal ${id}: ${data.length} bytes`);
        }
      } catch (logError) {
        // Silently ignore logging errors
      }

      try {
        // Wrap the write in a try-catch to handle EIO errors
        terminals[id].write(data);
      } catch (writeError) {
        // If we get an EIO error, mark the terminal as inactive
        if ((writeError as Error).message.includes('EIO')) {
          console.warn(`EIO error writing to terminal ${id}, marking as inactive`);
          if (terminalStatus[id]) {
            terminalStatus[id].active = false;
          }
        }
        throw writeError; // Re-throw to be caught by outer try-catch
      } finally {
        // Decrement pending writes counter
        if (terminalStatus[id]) {
          terminalStatus[id].pendingWrites--;
        }
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to write to terminal:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  // Handle terminal resize events
  ipcMain.handle('terminal:resize', (_, { id, cols, rows }) => {
    try {
      const term = terminals[id];
      if (!term) {
        return { success: false, error: 'Terminal not found' };
      }

      term.resize(cols, rows);
      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });

  // Close terminal
  ipcMain.handle('terminal:close', (_, { id }) => {
    try {
      // Check if terminal exists
      if (!terminals[id]) {
        console.warn(`Terminal ${id} not found for closing, may have already exited`);

        // Clean up any lingering status
        delete terminalStatus[id];

        // Not treating this as an error since the end result is the same
        return { success: true };
      }

      // Use a safer logging approach
      try {
        if (process.stdout.writable) {
          console.log(`Closing terminal ${id}`);
        }
      } catch (logError) {
        // Silently ignore logging errors
      }

      // Mark terminal as inactive before attempting to kill it
      if (terminalStatus[id]) {
        terminalStatus[id].active = false;
      }

      try {
        // Kill the terminal process
        terminals[id].kill();
      } catch (killError) {
        console.warn(`Error killing terminal ${id}:`, killError);
        // Continue with cleanup even if kill fails
      }

      // Clean up resources
      delete terminals[id];

      // Wait for any pending writes to complete before removing status
      setTimeout(() => {
        delete terminalStatus[id];
      }, 1000);

      return { success: true };
    } catch (error) {
      console.error('Failed to close terminal:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  // Change terminal working directory
  ipcMain.handle('terminal:changeCwd', (_, { id, cwd }) => {
    try {
      if (!terminals[id]) {
        console.error(`Terminal ${id} not found for changing directory`);
        return { success: false, error: 'Terminal not found' };
      }

      // Validate the directory
      try {
        const stats = fs.statSync(cwd);
        if (!stats.isDirectory()) {
          console.warn(`Not a valid directory: ${cwd}`);
          return { success: false, error: 'Not a valid directory' };
        }
      } catch (err) {
        console.warn(`Invalid directory: ${cwd}`, err);
        return { success: false, error: 'Invalid directory' };
      }

      // Format cd command based on OS
      const cdCommand = process.platform === 'win32'
        ? `cd /d "${cwd.replace(/\\/g, '\\\\')}"\r\n`
        : `cd "${cwd.replace(/"/g, '\\"')}"\r\n`;

      // Use a safer logging approach
      try {
        if (process.stdout.writable) {
          console.log(`Changing directory for terminal ${id} to: ${cwd}`);
        }
      } catch (logError) {
        // Silently ignore logging errors
      }
      terminals[id].write(cdCommand);
      return { success: true, cwd };
    } catch (error) {
      console.error('Failed to change terminal directory:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  // Clean up terminals when app is about to quit
  app.on('before-quit', () => {
    // Use a safer logging approach
    try {
      if (process.stdout.writable) {
        console.log('Cleaning up terminal processes...');
      }
    } catch (logError) {
      // Silently ignore logging errors
    }

    // Mark all terminals as inactive first
    Object.keys(terminalStatus).forEach(id => {
      if (terminalStatus[id]) {
        terminalStatus[id].active = false;
      }
    });

    // Then kill all terminal processes
    Object.keys(terminals).forEach(id => {
      try {
        if (terminals[id]) {
          // Use a safer logging approach
          try {
            if (process.stdout.writable) {
              console.log(`Killing terminal ${id}`);
            }
          } catch (logError) {
            // Silently ignore logging errors
          }
          try {
            terminals[id].kill();
          } catch (killError) {
            // Use a safer logging approach
            try {
              if (process.stderr.writable) {
                console.warn(`Error killing terminal ${id} during cleanup:`, killError);
              }
            } catch (logError) {
              // Silently ignore logging errors
            }
            // Continue with cleanup even if kill fails
          }
        }
        delete terminals[id];
      } catch (e) {
        // Use a safer logging approach
        try {
          if (process.stderr.writable) {
            console.error(`Error cleaning up terminal ${id}:`, e);
          }
        } catch (logError) {
          // Silently ignore logging errors
        }
      } finally {
        // Make sure to clean up status
        delete terminalStatus[id];
      }
    });

    // Final cleanup to ensure no references remain
    Object.keys(terminalStatus).forEach(id => {
      delete terminalStatus[id];
    });
  });
}