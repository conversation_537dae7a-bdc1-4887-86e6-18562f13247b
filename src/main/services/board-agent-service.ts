// src/main/services/board-agent-service.ts
import { ipc<PERSON><PERSON>, <PERSON><PERSON>erWindow } from 'electron';
import { BOARD_COMMANDS, BOARD_EVENTS } from '../../renderer/lib/board-ipc-bridge';
import * as fs from 'fs';
import * as path from 'path';

// Default board state
const defaultBoardState = {
  id: 'default-board',
  name: 'Default Board',
  columns: [
    { id: 'column-1', name: 'To Do', order: 0, color: '#e0e0e0' },
    { id: 'column-2', name: 'In Progress', order: 1, color: '#bbdefb' },
    { id: 'column-3', name: 'Done', order: 2, color: '#c8e6c9' }
  ],
  swimlanes: [
    { id: 'swimlane-1', name: 'High Priority', order: 0, expanded: true, color: '#ffcdd2' },
    { id: 'swimlane-2', name: 'Medium Priority', order: 1, expanded: true, color: '#fff9c4' },
    { id: 'swimlane-3', name: 'Low Priority', order: 2, expanded: true, color: '#dcedc8' }
  ],
  cards: [
    {
      id: 'card-sample-1',
      title: 'Sample Task: Implement Kanban Board',
      description: 'Implement the kanban board functionality according to the blueprint.',
      columnId: 'column-2',
      swimlaneId: 'swimlane-1',
      type: 'feature',
      priority: 'high',
      progress: 75,
      dueDate: new Date(Date.now() + 86400000 * 3).toISOString(), // 3 days from now
      agentAssignments: [
        {
          agentId: 'agent-1',
          agentType: 'code',
          assignmentTime: new Date().toISOString()
        }
      ],
      taskHistory: [
        {
          timestamp: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 days ago
          action: 'created',
          details: 'Task created'
        },
        {
          timestamp: new Date(Date.now() - 86400000 * 1).toISOString(), // 1 day ago
          action: 'update',
          details: 'Progress updated to 50%'
        },
        {
          timestamp: new Date().toISOString(),
          action: 'update',
          details: 'Progress updated to 75%'
        }
      ],
      createdAt: new Date(Date.now() - 86400000 * 2).toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'card-sample-2',
      title: 'Sample Task: Write Documentation',
      description: 'Create documentation for the kanban board functionality.',
      columnId: 'column-1',
      swimlaneId: 'swimlane-2',
      type: 'task',
      priority: 'medium',
      progress: 0,
      dueDate: new Date(Date.now() + 86400000 * 5).toISOString(), // 5 days from now
      agentAssignments: [
        {
          agentId: 'agent-2',
          agentType: 'docs',
          assignmentTime: new Date().toISOString()
        }
      ],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  cardTypes: [
    { id: 'feature', name: 'Feature', color: '#4caf50' },
    { id: 'bug', name: 'Bug', color: '#f44336' },
    { id: 'task', name: 'Task', color: '#2196f3' },
    { id: 'improvement', name: 'Improvement', color: '#ff9800' },
    { id: 'research', name: 'Research', color: '#9c27b0' }
  ],
  agents: [
    {
      id: 'agent-1',
      name: 'Code Assistant',
      type: 'code',
      status: 'idle',
      resourceUsage: {
        cpu: 0,
        memory: 0,
        tokens: 0
      }
    },
    {
      id: 'agent-2',
      name: 'Documentation Helper',
      type: 'docs',
      status: 'idle',
      resourceUsage: {
        cpu: 0,
        memory: 0,
        tokens: 0
      }
    },
    {
      id: 'agent-3',
      name: 'Test Generator',
      type: 'test',
      status: 'idle',
      resourceUsage: {
        cpu: 0,
        memory: 0,
        tokens: 0
      }
    }
  ]
};

export class BoardAgentService {
  private mainWindow: BrowserWindow;
  private boardState: any;
  private boardStoragePath: string;

  constructor(mainWindow: BrowserWindow, appDataPath: string) {
    this.mainWindow = mainWindow;
    this.boardStoragePath = path.join(appDataPath, 'board-state.json');
    this.boardState = this.loadBoardState();
    this.registerIPCHandlers();
  }

  private loadBoardState() {
    try {
      if (fs.existsSync(this.boardStoragePath)) {
        const data = fs.readFileSync(this.boardStoragePath, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.error('Failed to load board state:', error);
    }

    return defaultBoardState;
  }

  private saveBoardState() {
    try {
      fs.writeFileSync(this.boardStoragePath, JSON.stringify(this.boardState, null, 2), 'utf8');
    } catch (error) {
      console.error('Failed to save board state:', error);
    }
  }

  private registerIPCHandlers() {
    // Handle commands from renderer
    ipcMain.handle(BOARD_COMMANDS.GET_STATE, () => {
      return this.boardState;
    });

    ipcMain.handle(BOARD_COMMANDS.CREATE_CARD, (_, { cardData, agentId }) => {
      const newCard = {
        ...cardData,
        id: `card-${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Add agent assignment if provided
      if (agentId) {
        newCard.agentAssignments = [{
          agentId,
          assignmentTime: new Date().toISOString()
        }];
      }

      // Update local state
      this.boardState.cards.push(newCard);
      this.saveBoardState();

      // Send update to renderer
      this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);

      return newCard;
    });

    ipcMain.handle(BOARD_COMMANDS.UPDATE_CARD, (_, { cardId, updates, agentId }) => {
      const cardIndex = this.boardState.cards.findIndex((c: any) => c.id === cardId);

      if (cardIndex !== -1) {
        const card = this.boardState.cards[cardIndex];

        // Update the card
        this.boardState.cards[cardIndex] = {
          ...card,
          ...updates,
          updatedAt: new Date().toISOString()
        };

        // Add to task history if agent is involved
        if (agentId) {
          if (!this.boardState.cards[cardIndex].taskHistory) {
            this.boardState.cards[cardIndex].taskHistory = [];
          }

          this.boardState.cards[cardIndex].taskHistory.push({
            timestamp: new Date().toISOString(),
            action: 'update',
            agentId,
            details: 'Card updated by agent'
          });
        }

        this.saveBoardState();

        // Send update to renderer
        this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);

        return this.boardState.cards[cardIndex];
      }

      return null;
    });

    ipcMain.handle(BOARD_COMMANDS.MOVE_CARD, (_, { cardId, toColumnId, toSwimlaneId, agentId }) => {
      const cardIndex = this.boardState.cards.findIndex((c: any) => c.id === cardId);

      if (cardIndex !== -1) {
        // Update column and swimlane
        this.boardState.cards[cardIndex].columnId = toColumnId;
        this.boardState.cards[cardIndex].swimlaneId = toSwimlaneId;
        this.boardState.cards[cardIndex].updatedAt = new Date().toISOString();

        // Add to task history if agent is involved
        if (agentId) {
          if (!this.boardState.cards[cardIndex].taskHistory) {
            this.boardState.cards[cardIndex].taskHistory = [];
          }

          this.boardState.cards[cardIndex].taskHistory.push({
            timestamp: new Date().toISOString(),
            action: 'move',
            agentId,
            details: `Card moved by agent to column ${toColumnId} and swimlane ${toSwimlaneId}`
          });
        }

        this.saveBoardState();

        // Send update to renderer
        this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);

        return true;
      }

      return false;
    });

    ipcMain.handle(BOARD_COMMANDS.DELETE_CARD, (_, { cardId, agentId }) => {
      const cardIndex = this.boardState.cards.findIndex((c: any) => c.id === cardId);

      if (cardIndex !== -1) {
        // Remove the card
        this.boardState.cards.splice(cardIndex, 1);
        this.saveBoardState();

        // Send update to renderer
        this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);

        return true;
      }

      return false;
    });

    // Agent assignment
    ipcMain.handle(BOARD_COMMANDS.ASSIGN_AGENT, (_, { cardId, agentId, agentType }) => {
      const cardIndex = this.boardState.cards.findIndex((c: any) => c.id === cardId);

      if (cardIndex !== -1) {
        const card = this.boardState.cards[cardIndex];

        if (!card.agentAssignments) {
          card.agentAssignments = [];
        }

        // Check if agent is already assigned
        const existingAssignment = card.agentAssignments.find((a: any) => a.agentId === agentId);
        if (existingAssignment) return true;

        card.agentAssignments.push({
          agentId,
          agentType,
          assignmentTime: new Date().toISOString()
        });

        this.saveBoardState();

        // Send update to renderer
        this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);

        return true;
      }

      return false;
    });

    // Unassign agent from card
    ipcMain.handle(BOARD_COMMANDS.UNASSIGN_AGENT, (_, { cardId, agentId }) => {
      const cardIndex = this.boardState.cards.findIndex((c: any) => c.id === cardId);

      if (cardIndex !== -1) {
        const card = this.boardState.cards[cardIndex];

        if (!card.agentAssignments) return false;

        // Remove the assignment
        card.agentAssignments = card.agentAssignments.filter((a: any) => a.agentId !== agentId);

        this.saveBoardState();
        this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);

        return true;
      }

      return false;
    });

    // Get agent status
    ipcMain.handle(BOARD_COMMANDS.GET_AGENT_STATUS, (_, { agentId }) => {
      const agent = this.boardState.agents.find((a: any) => a.id === agentId);

      if (!agent) return null;

      return {
        id: agent.id,
        status: agent.status || 'idle',
        currentTaskId: agent.currentTaskId
      };
    });

    // Get agent resource usage
    ipcMain.handle(BOARD_COMMANDS.GET_AGENT_RESOURCE_USAGE, (_, { agentId }) => {
      const agent = this.boardState.agents.find((a: any) => a.id === agentId);

      if (!agent) return null;

      // In a real implementation, this would get actual resource usage
      // For now, we'll return mock data or existing data
      return agent.resourceUsage || {
        cpu: Math.floor(Math.random() * 30),
        memory: Math.floor(Math.random() * 200),
        tokens: Math.floor(Math.random() * 1000)
      };
    });

    // Column operations
    ipcMain.handle(BOARD_COMMANDS.CREATE_COLUMN, (_, { columnData }) => {
      const newColumn = {
        ...columnData,
        id: `column-${Date.now()}`
      };

      this.boardState.columns.push(newColumn);
      this.saveBoardState();

      this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);

      return newColumn;
    });

    ipcMain.handle(BOARD_COMMANDS.UPDATE_COLUMN, (_, { columnId, updates }) => {
      const columnIndex = this.boardState.columns.findIndex((c: any) => c.id === columnId);

      if (columnIndex !== -1) {
        this.boardState.columns[columnIndex] = {
          ...this.boardState.columns[columnIndex],
          ...updates
        };

        this.saveBoardState();
        this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);

        return this.boardState.columns[columnIndex];
      }

      return null;
    });

    ipcMain.handle(BOARD_COMMANDS.DELETE_COLUMN, (_, { columnId }) => {
      const columnIndex = this.boardState.columns.findIndex((c: any) => c.id === columnId);

      if (columnIndex !== -1) {
        this.boardState.columns.splice(columnIndex, 1);

        // Remove cards in this column or move them to another column
        this.boardState.cards = this.boardState.cards.filter((card: any) => card.columnId !== columnId);

        this.saveBoardState();
        this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);

        return true;
      }

      return false;
    });

    // Swimlane operations
    ipcMain.handle(BOARD_COMMANDS.CREATE_SWIMLANE, (_, { swimlaneData }) => {
      const newSwimlane = {
        ...swimlaneData,
        id: `swimlane-${Date.now()}`
      };

      this.boardState.swimlanes.push(newSwimlane);
      this.saveBoardState();

      this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);

      return newSwimlane;
    });

    ipcMain.handle(BOARD_COMMANDS.UPDATE_SWIMLANE, (_, { swimlaneId, updates }) => {
      const swimlaneIndex = this.boardState.swimlanes.findIndex((s: any) => s.id === swimlaneId);

      if (swimlaneIndex !== -1) {
        this.boardState.swimlanes[swimlaneIndex] = {
          ...this.boardState.swimlanes[swimlaneIndex],
          ...updates
        };

        this.saveBoardState();
        this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);

        return this.boardState.swimlanes[swimlaneIndex];
      }

      return null;
    });

    ipcMain.handle(BOARD_COMMANDS.DELETE_SWIMLANE, (_, { swimlaneId }) => {
      const swimlaneIndex = this.boardState.swimlanes.findIndex((s: any) => s.id === swimlaneId);

      if (swimlaneIndex !== -1) {
        this.boardState.swimlanes.splice(swimlaneIndex, 1);

        // Remove cards in this swimlane or move them to another swimlane
        this.boardState.cards = this.boardState.cards.filter((card: any) => card.swimlaneId !== swimlaneId);

        this.saveBoardState();
        this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);

        return true;
      }

      return false;
    });

    // Toggle swimlane expansion
    ipcMain.handle(BOARD_COMMANDS.TOGGLE_SWIMLANE_EXPANSION, (_, { swimlaneId }) => {
      const swimlaneIndex = this.boardState.swimlanes.findIndex((s: any) => s.id === swimlaneId);

      if (swimlaneIndex !== -1) {
        // Toggle the expanded state
        this.boardState.swimlanes[swimlaneIndex].expanded = !this.boardState.swimlanes[swimlaneIndex].expanded;

        this.saveBoardState();
        this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);

        return this.boardState.swimlanes[swimlaneIndex];
      }

      return null;
    });

    // Update card types
    ipcMain.handle(BOARD_COMMANDS.UPDATE_CARD_TYPES, (_, { boardId, cardTypes }) => {
      // In a multi-board setup, we would find the specific board
      // For now, we just update the card types in the current board
      this.boardState.cardTypes = cardTypes;
      this.saveBoardState();
      this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);
      return this.boardState.cardTypes;
    });

    // Update agents
    ipcMain.handle(BOARD_COMMANDS.UPDATE_AGENTS, (_, { boardId, agents }) => {
      // In a multi-board setup, we would find the specific board
      // For now, we just update the agents in the current board
      this.boardState.agents = agents;
      this.saveBoardState();
      this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);
      return this.boardState.agents;
    });

    // Save board state
    ipcMain.handle(BOARD_COMMANDS.SAVE_BOARD_STATE, () => {
      this.saveBoardState();
      return true;
    });

    // Load board state
    ipcMain.handle(BOARD_COMMANDS.LOAD_BOARD_STATE, () => {
      this.boardState = this.loadBoardState();
      this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);
      return this.boardState;
    });
  }

  // Methods for agents to interact with board
  createTaskFromAgent(task: any, agentId: string) {
    const newCard = {
      id: `card-${Date.now()}`,
      title: task.title,
      description: task.description || '',
      columnId: task.columnId || this.boardState.columns[0]?.id, // Default to first column
      swimlaneId: task.swimlaneId || this.boardState.swimlanes[0]?.id, // Default to first swimlane
      priority: task.priority || 'medium',
      agentAssignments: [{
        agentId,
        agentType: task.agentType || 'system',
        assignmentTime: new Date().toISOString(),
      }],
      dependencies: task.dependencies || [],
      resourceMetrics: {
        tokenUsage: 0,
        cpuTime: 0,
        memoryUsage: 0,
      },
      taskHistory: [{
        timestamp: new Date().toISOString(),
        action: 'created',
        agentId,
        details: 'Task created by agent',
      }],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Update local state
    this.boardState.cards.push(newCard);
    this.saveBoardState();

    // Send update to renderer
    this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);
    this.mainWindow.webContents.send(BOARD_EVENTS.AGENT_ACTION, {
      type: 'CREATE_CARD',
      agentId,
      cardId: newCard.id,
    });

    return newCard;
  }

  updateTaskProgress(cardId: string, progress: number, agentId: string) {
    const cardIndex = this.boardState.cards.findIndex((c: any) => c.id === cardId);

    if (cardIndex !== -1) {
      this.boardState.cards[cardIndex].progress = progress;
      this.boardState.cards[cardIndex].updatedAt = new Date().toISOString();

      if (!this.boardState.cards[cardIndex].taskHistory) {
        this.boardState.cards[cardIndex].taskHistory = [];
      }

      this.boardState.cards[cardIndex].taskHistory.push({
        timestamp: new Date().toISOString(),
        action: 'progress-update',
        agentId,
        details: `Updated progress to ${progress}%`,
      });

      this.saveBoardState();

      // Send update to renderer
      this.mainWindow.webContents.send(BOARD_EVENTS.STATE_UPDATE, this.boardState);
      this.mainWindow.webContents.send(BOARD_EVENTS.AGENT_ACTION, {
        type: 'UPDATE_PROGRESS',
        agentId,
        cardId,
        progress,
      });

      return true;
    }

    return false;
  }
}
