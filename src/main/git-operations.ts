// src/main/git-operations.ts
import { ipcMain } from 'electron';
import * as cp from 'child_process';
import * as path from 'path';
import { promisify } from 'util';

const exec = promisify(cp.exec);

interface GitStatusResult {
  staged: string[];
  modified: string[];
  untracked: string[];
  isRepo: boolean;
}

export function setupGitOperations() {
  // Check if a directory is a Git repository
  ipcMain.handle('git:isRepo', async (_, { dirPath }) => {
    try {
      await exec('git rev-parse --is-inside-work-tree', { cwd: dirPath });
      return { success: true, isRepo: true };
    } catch (error) {
      return { success: true, isRepo: false };
    }
  });

  // Get Git status for a repository
  ipcMain.handle('git:status', async (_, { dirPath }) => {
    try {
      const result: GitStatusResult = {
        staged: [],
        modified: [],
        untracked: [],
        isRepo: true
      };

      // Get status
      const { stdout } = await exec('git status --porcelain', { cwd: dirPath });
      if (!stdout) {
        return { success: true, result };
      }

      // Parse status output
      const lines = stdout.trim().split('\n');
      for (const line of lines) {
        if (!line) continue;

        const status = line.substring(0, 2);
        const filePath = line.substring(3);

        // Staged files (added, modified, deleted)
        if (status[0] !== ' ' && status[0] !== '?') {
          result.staged.push(filePath);
        }

        // Modified but not staged
        if (status[1] === 'M') {
          result.modified.push(filePath);
        }

        // Untracked files
        if (status === '??') {
          result.untracked.push(filePath);
        }
      }

      return { success: true, result };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });

  // Stage a file
  ipcMain.handle('git:stage', async (_, { filePath, dirPath }) => {
    try {
      const relativePath = path.relative(dirPath, filePath);
      await exec(`git add "${relativePath}"`, { cwd: dirPath });
      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });

  // Unstage a file
  ipcMain.handle('git:unstage', async (_, { filePath, dirPath }) => {
    try {
      const relativePath = path.relative(dirPath, filePath);
      await exec(`git reset HEAD "${relativePath}"`, { cwd: dirPath });
      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });

  // Get current branch
  ipcMain.handle('git:currentBranch', async (_, { dirPath }) => {
    try {
      const { stdout } = await exec('git branch --show-current', { cwd: dirPath });
      return { success: true, branch: stdout.trim() };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });

  // Commit changes
  ipcMain.handle('git:commit', async (_, { message, dirPath }) => {
    try {
      await exec(`git commit -m "${message.replace(/"/g, '\\"')}"`, { cwd: dirPath });
      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  });
}