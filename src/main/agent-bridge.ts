// src/main/agent-bridge.ts
import { ipcMain } from 'electron';
import { agentSystem } from '../agent/AgentSystem';
import type { AgentContext, AgentResponse } from '../agent/base/Agent';

export function setupAgentBridge() {
  // Initialize the agent system
  ipcMain.handle('agent:initialize', async (_, { apiKeys, selectedModels }) => {
    try {
      // Validate API keys before using
      if (!apiKeys || Object.keys(apiKeys).length === 0) {
        return {
          success: false,
          error: 'No API keys provided'
        };
      }

      // Check if at least one key is non-empty
      const hasValidKey = Object.values(apiKeys).some(key => typeof key === 'string' && key.trim() !== '');
      if (!hasValidKey) {
        return {
          success: false,
          error: 'At least one valid API key is required'
        };
      }

      await agentSystem.initialize(apiKeys, selectedModels);
      return { success: true };
    } catch (error) {
      console.error('Failed to initialize agent system:', error);
      return {
        success: false,
        error: `Failed to initialize agent system: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // Process a query with the agent system
  ipcMain.handle('agent:process', async (_, context: AgentContext) => {
    try {
      if (!agentSystem.isInitialized()) {
        return {
          success: false,
          error: 'Agent system not initialized. Please provide API keys.'
        };
      }

      // Validate context
      if (!context.query || context.query.trim() === '') {
        return {
          success: false,
          error: 'Query cannot be empty'
        };
      }

      const response = await agentSystem.processQuery(context);
      return response;
    } catch (error) {
      console.error('Agent system error:', error);
      return {
        success: false,
        error: `Agent system error: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // Get available agents
  ipcMain.handle('agent:getAgents', async () => {
    try {
      if (!agentSystem.isInitialized()) {
        return {
          success: false,
          error: 'Agent system not initialized'
        };
      }

      const agents = agentSystem.getAgentManager().getAllAgents();
      return {
        success: true,
        agents: agents.map(agent => ({
          id: agent.getId(),
          name: agent.getName(),
          description: agent.getDescription(),
          capabilities: agent.getCapabilities()
        }))
      };
    } catch (error) {
      console.error('Failed to get agents:', error);
      return {
        success: false,
        error: `Failed to get agents: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // New: Set agent provider and model
  ipcMain.handle('agent:setProvider', async (_, { provider, model }) => {
    try {
      if (!agentSystem.isInitialized()) {
        return {
          success: false,
          error: 'Agent system not initialized'
        };
      }

      agentSystem.getLLMService().setDefaultProvider(provider);
      if (model) {
        agentSystem.getLLMService().setDefaultModel(provider, model);
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to set provider:', error);
      return {
        success: false,
        error: `Failed to set provider: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  // Get context history for a project
  ipcMain.handle('agent:getHistory', async (_, { projectPath }) => {
    try {
      if (!agentSystem.isInitialized()) {
        return {
          success: false,
          error: 'Agent system not initialized'
        };
      }

      // This would require exposing the context management service through the agent system
      // For now, return a placeholder
      return {
        success: true,
        history: [] // This would be populated from contextManagementService
      };
    } catch (error) {
      console.error('Failed to get history:', error);
      return {
        success: false,
        error: `Failed to get history: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });
}