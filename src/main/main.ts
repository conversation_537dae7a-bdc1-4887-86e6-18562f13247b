// src/main/main.ts
import { app, BrowserWindow } from 'electron';
import * as path from 'path';
import * as os from 'os';
import { setupFileOperations } from './file-operations';
import { setupGitOperations } from './git-operations';
import { setupTerminalService } from './terminal-service';
import { setupAgentBridge } from './agent-bridge';
import { BoardAgentService } from './services/board-agent-service';
console.log('Imported setupGitOperations');

let mainWindow: BrowserWindow | null = null;
let boardAgentService: BoardAgentService | null = null;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true, // Enable context isolation for security
      preload: path.join(__dirname, '../preload/preload.js'), // Preload script path
      sandbox: false,
      webSecurity: true // Enable web security
    }
  });

  // Load the correct URL based on environment
  console.log('NODE_ENV:', process.env.NODE_ENV);

  // Always load from file in this case since we're having issues
  const rendererPath = path.join(__dirname, '../renderer/index.html');
  console.log('Loading renderer from:', rendererPath);
  mainWindow.loadFile(rendererPath);

  // Open DevTools
  mainWindow.webContents.openDevTools();

  // Emitted when the window is closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  createWindow();
  setupFileOperations();
  console.log('Setting up Git operations...');
  setupGitOperations();
  console.log('Git operations setup complete');
  console.log('Setting up Terminal service...');
  setupTerminalService();
  console.log('Terminal service setup complete');
  console.log('Setting up Agent system...');
  setupAgentBridge();
  console.log('Agent system setup complete');

  console.log('Setting up Board Agent service...');
  const appDataPath = path.join(app.getPath('userData'), 'board-data');
  if (mainWindow) {
    boardAgentService = new BoardAgentService(mainWindow, appDataPath);
    console.log('Board Agent service setup complete');
  } else {
    console.error('Failed to initialize Board Agent service: mainWindow is null');
  }
});

// Quit when all windows are closed
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Set up IPC handlers
import { ipcMain, shell } from 'electron';

ipcMain.handle('ping', () => {
  console.log('Received ping request');
  return 'pong';
});

ipcMain.handle('open-external', async (_, { url }) => {
  try {
    await shell.openExternal(url);
    return { success: true };
  } catch (error) {
    console.error('Failed to open external URL:', error);
    return { success: false, error: (error as Error).message };
  }
});

// Handle agent system toggle
ipcMain.on('agent-system:toggle', (_, isActive) => {
  console.log('Agent system toggled:', isActive);
  // You can implement logic here to activate/deactivate agent processes
});

// Handle agent pause
ipcMain.on('agent:pause', (_, agentId) => {
  console.log('Agent paused:', agentId);
  // Implement logic to pause the agent
});

// Handle agent resume
ipcMain.on('agent:resume', (_, agentId) => {
  console.log('Agent resumed:', agentId);
  // Implement logic to resume the agent
});