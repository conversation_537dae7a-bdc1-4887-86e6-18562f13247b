// src/renderer/components/layout/AppSidebar.tsx
import React from 'react';
import styled from 'styled-components';
import {
  FolderTree, Search, GitBranch, Bot, Terminal,
  Settings, HelpCircle, Trello
} from 'lucide-react';

interface AppSidebarProps {
  activeItem: string;
  onItemClick: (item: string) => void;
}

const AppSidebar: React.FC<AppSidebarProps> = ({
  activeItem,
  onItemClick
}) => {
  return (
    <SidebarContainer>
      <TopItems>
        <SidebarItem
          isActive={activeItem === 'explorer'}
          onClick={() => onItemClick('explorer')}
          title="File Explorer"
        >
          <FolderTree size={20} strokeWidth={1.5} />
          {activeItem === 'explorer' && <ActiveIndicator />}
        </SidebarItem>

        <SidebarItem
          isActive={activeItem === 'search'}
          onClick={() => onItemClick('search')}
          title="Search"
        >
          <Search size={20} strokeWidth={1.5} />
          {activeItem === 'search' && <ActiveIndicator />}
        </SidebarItem>

        <SidebarItem
          isActive={activeItem === 'source-control'}
          onClick={() => onItemClick('source-control')}
          title="Source Control"
        >
          <GitBranch size={20} strokeWidth={1.5} />
          {activeItem === 'source-control' && <ActiveIndicator />}
        </SidebarItem>

        <SidebarItem
          isActive={activeItem === 'agent'}
          onClick={() => onItemClick('agent')}
          title="AI Agent"
        >
          <Bot size={20} strokeWidth={1.5} />
          {activeItem === 'agent' && <ActiveIndicator />}
        </SidebarItem>

        <SidebarItem
          isActive={activeItem === 'terminal'}
          onClick={() => onItemClick('terminal')}
          title="Terminal"
        >
          <Terminal size={20} strokeWidth={1.5} />
          {activeItem === 'terminal' && <ActiveIndicator />}
        </SidebarItem>

        <SidebarItem
          isActive={activeItem === 'kanban'}
          onClick={() => onItemClick('kanban')}
          title="Kanban Board"
        >
          <Trello size={20} strokeWidth={1.5} />
          {activeItem === 'kanban' && <ActiveIndicator />}
        </SidebarItem>
      </TopItems>

      <BottomItems>
        <SidebarItem
          isActive={activeItem === 'settings'}
          onClick={() => onItemClick('settings')}
          title="Settings"
        >
          <Settings size={20} strokeWidth={1.5} />
          {activeItem === 'settings' && <ActiveIndicator />}
        </SidebarItem>
        <SidebarItem
          isActive={activeItem === 'help'}
          onClick={() => onItemClick('help')}
          title="Help"
        >
          <HelpCircle size={20} strokeWidth={1.5} />
          {activeItem === 'help' && <ActiveIndicator />}
        </SidebarItem>
      </BottomItems>
    </SidebarContainer>
  );
};

const SidebarContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 48px;
  height: 100%;
  background-color: ${({ theme }) => theme.mode === 'dark' ? '#333333' : '#2c2c2c'};
  border-left: 1px solid ${({ theme }) => theme.colors.border};
`;

const TopItems = styled.div`
  display: flex;
  flex-direction: column;
`;

const BottomItems = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
`;

const SidebarItem = styled.div<{ isActive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 48px;
  width: 48px;
  color: ${({ isActive, theme }) =>
    isActive ? '#ffffff' : 'rgba(255, 255, 255, 0.5)'};
  cursor: pointer;
  transition: color 0.1s ease;

  &:hover {
    color: #ffffff;
  }
`;

const ActiveIndicator = styled.div`
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 60%;
  background-color: #ffffff;
`;

export default AppSidebar;