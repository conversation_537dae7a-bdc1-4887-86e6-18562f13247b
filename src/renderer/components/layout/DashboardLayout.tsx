// src/renderer/components/layout/DashboardLayout.tsx
import React, { useState, useEffect } from 'react';
import styled, { ThemeProvider, createGlobalStyle } from 'styled-components';
import AppSidebar from './AppSidebar';
import SplitPane from 'react-split-pane';
import {
  GitBranch, Command as CommandIcon, Settings, Keyboard, Bot, Sun, Moon, Save
} from 'lucide-react';

// Custom split pane styles - VS Code style
const SplitPaneStyles = createGlobalStyle`
  .Resizer {
    background: ${({ theme }) => theme.colors.border};
    z-index: 1;
    box-sizing: border-box;
    background-clip: padding-box;
    opacity: 0.2;
  }

  .Resizer.horizontal {
    height: 1px;
    margin: 0;
    border-top: 0;
    border-bottom: 0;
    cursor: row-resize;
    width: 100%;
  }

  .Resizer.horizontal:hover,
  .Resizer.horizontal.resizing {
    background-color: ${({ theme }) => theme.colors.primary};
    opacity: 0.6;
  }

  .Resizer.vertical {
    width: 1px;
    margin: 0;
    border-left: 0;
    border-right: 0;
    cursor: col-resize;
  }

  .Resizer.vertical:hover,
  .Resizer.vertical.resizing {
    background-color: ${({ theme }) => theme.colors.primary};
    opacity: 0.6;
  }

  .DragLayer {
    z-index: 1;
  }

  .Pane {
    overflow: hidden;
  }
`;

import { FileExplorer } from '../file-explorer/FileExplorer';
import { MonacoEditorComponent } from '../editor/MonacoEditorComponent';
import ModernTerminalTabs from '../terminal/ModernTerminalTabs';
import { CommandPalette, Command } from '../command-palette/CommandPalette';
import { EditorPreferencesPanel } from '../editor/EditorPreferencesPanel';
import { EditorPreferences, defaultEditorPreferences } from '../../types/editor';
import { KeyboardShortcutsPanel } from '../shortcuts/KeyboardShortcutsPanel';
import { ShortcutsManager } from '../shortcuts/ShortcutsManager';
import { KeyboardShortcut } from '../../types/shortcuts';
import { gitService } from '../../services/GitService';
import AgentPanel from '../agent/AgentPanel';
import { lightTheme, darkTheme } from '../../styles/theme';
import { AgentResponse } from '../../../agent/base/Agent';
import KanbanIntegration from '../kanban/KanbanIntegration';

interface DashboardLayoutProps {
  theme: 'light' | 'dark';
  onThemeChange: (theme: 'light' | 'dark') => void;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  theme,
  onThemeChange
}) => {
  const [currentFilePath, setCurrentFilePath] = useState<string | null>(null);
  const [fileContent, setFileContent] = useState<string>('');
  const [language, setLanguage] = useState<string>('javascript');
  const [isModified, setIsModified] = useState<boolean>(false);
  const [isCommandPaletteOpen, setIsCommandPaletteOpen] = useState(false);
  const [isPreferencesPanelOpen, setIsPreferencesPanelOpen] = useState(false);
  const [isShortcutsPanelOpen, setIsShortcutsPanelOpen] = useState(false);
  const [editorPreferences, setEditorPreferences] = useState<EditorPreferences>(defaultEditorPreferences);
  const [commands, setCommands] = useState<Command[]>([]);
  const [shortcuts, setShortcuts] = useState<KeyboardShortcut[]>([]);
  const [gitBranch, setGitBranch] = useState<string | null>(null);
  const [isGitRepo, setIsGitRepo] = useState<boolean>(false);
  const [isAgentPanelVisible, setIsAgentPanelVisible] = useState<boolean>(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState<string>('explorer');
  const [isKanbanBoardActive, setIsKanbanBoardActive] = useState<boolean>(false);

  // State for panel sizes
  const [explorerSize, setExplorerSize] = useState(250);
  const [terminalSize, setTerminalSize] = useState(280);
  const [agentSize, setAgentSize] = useState(350);

  // Detect file language based on extension
  useEffect(() => {
    if (currentFilePath) {
      const extension = window.electron.path.extname(currentFilePath).toLowerCase();
      let detectedLanguage = 'plaintext';

      switch (extension) {
        case '.js':
          detectedLanguage = 'javascript';
          break;
        case '.ts':
          detectedLanguage = 'typescript';
          break;
        case '.jsx':
          detectedLanguage = 'javascript';
          break;
        case '.tsx':
          detectedLanguage = 'typescript';
          break;
        case '.html':
        case '.htm':
          detectedLanguage = 'html';
          break;
        case '.css':
          detectedLanguage = 'css';
          break;
        case '.json':
          detectedLanguage = 'json';
          break;
        case '.md':
          detectedLanguage = 'markdown';
          break;
        case '.php':
          detectedLanguage = 'php';
          break;
        case '.py':
          detectedLanguage = 'python';
          break;
        case '.rb':
          detectedLanguage = 'ruby';
          break;
        case '.java':
          detectedLanguage = 'java';
          break;
        case '.c':
        case '.cpp':
        case '.h':
        case '.hpp':
          detectedLanguage = 'cpp';
          break;
        case '.cs':
          detectedLanguage = 'csharp';
          break;
        case '.go':
          detectedLanguage = 'go';
          break;
        case '.rs':
          detectedLanguage = 'rust';
          break;
        case '.swift':
          detectedLanguage = 'swift';
          break;
        case '.kt':
        case '.kts':
          detectedLanguage = 'kotlin';
          break;
        case '.xml':
        case '.svg':
        case '.plist':
          detectedLanguage = 'xml';
          break;
        case '.yml':
        case '.yaml':
          detectedLanguage = 'yaml';
          break;
        case '.sql':
          detectedLanguage = 'sql';
          break;
        case '.sh':
        case '.bash':
          detectedLanguage = 'shell';
          break;
        case '.ps1':
          detectedLanguage = 'powershell';
          break;
        case '.dockerfile':
        case '.dockerignore':
          detectedLanguage = 'dockerfile';
          break;
        case '.dart':
          detectedLanguage = 'dart';
          break;
        case '.lua':
          detectedLanguage = 'lua';
          break;
        case '.r':
          detectedLanguage = 'r';
          break;
        case '.pl':
        case '.pm':
          detectedLanguage = 'perl';
          break;
        case '.scala':
          detectedLanguage = 'scala';
          break;
        case '.sol':
          detectedLanguage = 'solidity';
          break;
        case '.ini':
        case '.conf':
          detectedLanguage = 'ini';
          break;
        case '.graphql':
        case '.gql':
          detectedLanguage = 'graphql';
          break;
      }

      setLanguage(detectedLanguage);
    }
  }, [currentFilePath]);

  // Get basename from path
  const getBasename = (path: string) => {
    if (!window.electron || !window.electron.path) return path;
    return window.electron.path.basename(path);
  };

  // File selection handler
  const handleFileSelect = (filePath: string, content: string) => {
    setCurrentFilePath(filePath);
    setFileContent(content);
    setIsModified(false);

    // Check Git branch when opening a file
    const dirPath = window.electron.path.dirname(filePath);
    checkGitRepository(dirPath);
  };

  // Check if a directory is a Git repository
  const checkGitRepository = async (dirPath: string) => {
    try {
      const isRepo = await gitService.isGitRepository(dirPath);
      setIsGitRepo(isRepo);

      if (isRepo) {
        const branchResponse = await gitService.getCurrentBranch(dirPath);
        if (branchResponse.success && branchResponse.branch) {
          setGitBranch(branchResponse.branch);
        } else {
          setGitBranch(null);
        }
      } else {
        setGitBranch(null);
      }
    } catch (error) {
      console.error('Error checking Git repository:', error);
      setIsGitRepo(false);
      setGitBranch(null);
    }
  };

  // Content change handler
  const handleContentChange = (newContent: string) => {
    setFileContent(newContent);
    setIsModified(true);
  };

  // Save file handler
  const handleSaveFile = async () => {
    if (!currentFilePath) return;

    try {
      const response = await window.electron.ipcRenderer.invoke('fs:writeFile', {
        filePath: currentFilePath,
        content: fileContent
      });

      if (response.success) {
        setIsModified(false);
        console.log('File saved successfully');
      } else {
        console.error('Failed to save file:', response.error);
      }
    } catch (error) {
      console.error('Error saving file:', error);
    }
  };

  // Create a dedicated function for closing the current file
  const handleCloseFile = (e?: React.MouseEvent | KeyboardEvent) => {
    // Prevent default browser behavior for Cmd+W
    if (e) {
      e.preventDefault();
    }

    // Check if file is modified and show confirmation
    if (isModified) {
      const confirmClose = window.confirm('You have unsaved changes. Do you want to save before closing?');
      if (confirmClose) {
        handleSaveFile().then(() => {
          setCurrentFilePath(null);
          setFileContent('');
          setIsModified(false);
        });
      } else {
        setCurrentFilePath(null);
        setFileContent('');
        setIsModified(false);
      }
    } else {
      setCurrentFilePath(null);
      setFileContent('');
      setIsModified(false);
    }
  };

  // Create commands based on current state
  useEffect(() => {
    const commandList: Command[] = [
      {
        id: 'theme.toggle',
        title: 'Toggle Theme',
        category: 'Appearance',
        shortcut: 'Ctrl+Shift+T',
        execute: () => onThemeChange(theme === 'dark' ? 'light' : 'dark')
      },
      {
        id: 'file.save',
        title: 'Save File',
        category: 'File',
        shortcut: 'Ctrl+S',
        execute: handleSaveFile
      },
      {
        id: 'editor.preferences',
        title: 'Editor Preferences',
        category: 'Editor',
        execute: () => setIsPreferencesPanelOpen(true)
      },
      {
        id: 'editor.find',
        title: 'Find in File',
        category: 'Editor',
        shortcut: 'Ctrl+F',
        execute: () => {
          // We'll use a ref to the editor to trigger find
          const editorElement = document.querySelector('.monaco-editor');
          if (editorElement) {
            // Simulate Ctrl+F to open the find panel
            const event = new KeyboardEvent('keydown', {
              key: 'f',
              code: 'KeyF',
              ctrlKey: true,
              bubbles: true
            });
            editorElement.dispatchEvent(event);
          }
        }
      },
      {
        id: 'shortcuts.show',
        title: 'Keyboard Shortcuts',
        category: 'Help',
        shortcut: 'Ctrl+K S',
        execute: () => setIsShortcutsPanelOpen(true)
      },
      {
        id: 'view.toggleAgentPanel',
        title: 'Toggle Agent Panel',
        category: 'View',
        shortcut: 'Ctrl+Shift+A',
        execute: () => setIsAgentPanelVisible(prev => !prev)
      }
    ];

    // Only add this command if a file is open
    if (currentFilePath) {
      commandList.push({
        id: 'file.close',
        title: 'Close Current File',
        category: 'File',
        shortcut: 'Ctrl+W',
        execute: handleCloseFile
      });
    }

    setCommands(commandList);
  }, [theme, currentFilePath, onThemeChange, isModified]);

  // Handle agent response application
  const handleAgentResponseApply = (response: AgentResponse) => {
    if (!response.success || !currentFilePath) return;

    if (response.changes && response.changes.length > 0) {
      // Apply targeted changes
      let newContent = fileContent;

      // Sort changes in reverse order (to avoid offset issues)
      const sortedChanges = [...response.changes].sort((a, b) => {
        return b.start - a.start;
      });

      // Apply each change
      for (const change of sortedChanges) {
        const lines = newContent.split('\n');

        // Apply the change
        lines.splice(
          change.start - 1,
          change.end - change.start + 1,
          change.content
        );

        // Join the lines back together
        newContent = lines.join('\n');
      }

      setFileContent(newContent);
      setIsModified(true);
    } else if (response.result) {
      // Apply full replacement
      setFileContent(response.result);
      setIsModified(true);
    }
  };

  // Handle editor preferences changes
  const handleEditorPreferencesChange = (newPreferences: EditorPreferences) => {
    setEditorPreferences(newPreferences);

    // Save preferences to localStorage for persistence
    localStorage.setItem('editorPreferences', JSON.stringify(newPreferences));
  };

  // Handle sidebar actions
  const handleSidebarAction = (action: string) => {
    setActiveSidebarItem(action);

    switch(action) {
      case 'explorer':
        // Toggle file explorer
        setIsKanbanBoardActive(false);
        break;
      case 'search':
        // Open search panel
        setIsKanbanBoardActive(false);
        break;
      case 'source-control':
        // Open source control panel
        setIsKanbanBoardActive(false);
        break;
      case 'agent':
        // Toggle agent panel
        setIsAgentPanelVisible(prev => !prev);
        setIsKanbanBoardActive(false);
        break;
      case 'terminal':
        // Focus terminal
        setIsKanbanBoardActive(false);
        break;
      case 'kanban':
        // Show kanban board
        setIsKanbanBoardActive(true);
        break;
      case 'settings':
        // Open settings
        setIsKanbanBoardActive(false);
        break;
      default:
        break;
    }
  };



  // Load saved preferences on startup
  useEffect(() => {
    const savedPreferences = localStorage.getItem('editorPreferences');
    if (savedPreferences) {
      try {
        const parsedPreferences = JSON.parse(savedPreferences);
        setEditorPreferences(parsedPreferences);
      } catch (error) {
        console.error('Failed to parse saved editor preferences:', error);
      }
    }
  }, []);

  // Define keyboard shortcuts
  useEffect(() => {
    const shortcutsList: KeyboardShortcut[] = [
      {
        id: 'command-palette',
        description: 'Open Command Palette',
        keys: ['mod', 'p'],
        category: 'General',
        action: () => setIsCommandPaletteOpen(true)
      },
      {
        id: 'save-file',
        description: 'Save File',
        keys: ['mod', 's'],
        category: 'File',
        action: handleSaveFile
      },
      {
        id: 'toggle-theme',
        description: 'Toggle Theme',
        keys: ['mod', 'shift', 't'],
        category: 'Appearance',
        action: () => onThemeChange(theme === 'dark' ? 'light' : 'dark')
      },
      {
        id: 'find-in-file',
        description: 'Find in File',
        keys: ['mod', 'f'],
        category: 'Editor',
        action: () => {
          const editorElement = document.querySelector('.monaco-editor');
          if (editorElement) {
            const event = new KeyboardEvent('keydown', {
              key: 'f',
              code: 'KeyF',
              ctrlKey: true,
              bubbles: true
            });
            editorElement.dispatchEvent(event);
          }
        }
      },
      {
        id: 'editor-preferences',
        description: 'Open Editor Preferences',
        keys: ['mod', ','],
        category: 'Editor',
        action: () => setIsPreferencesPanelOpen(true)
      },
      {
        id: 'keyboard-shortcuts',
        description: 'Show Keyboard Shortcuts',
        keys: ['mod', 'k', 's'], // This is correct format, but our handler needs to be improved
        category: 'General',
        action: () => setIsShortcutsPanelOpen(true)
      },
      {
        id: 'toggle-agent-panel',
        description: 'Toggle Agent Panel',
        keys: ['mod', 'shift', 'a'],
        category: 'View',
        action: () => setIsAgentPanelVisible(prev => !prev)
      }
    ];

    // Additional editor-specific shortcuts when a file is open
    if (currentFilePath) {
      shortcutsList.push(
        {
          id: 'close-file',
          description: 'Close Current File',
          keys: ['mod', 'w'],
          category: 'File',
          action: handleCloseFile
        }
      );
    }

    setShortcuts(shortcutsList);
  }, [currentFilePath, theme, onThemeChange, isModified]);

  // Add keyboard shortcut for command palette
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Open command palette with Ctrl+P or Cmd+P (case insensitive)
      if ((event.ctrlKey || event.metaKey) && (event.key.toLowerCase() === 'p')) {
        event.preventDefault();
        setIsCommandPaletteOpen(true);
      }

      // Save file with Ctrl+S or Cmd+S (case insensitive)
      if ((event.ctrlKey || event.metaKey) && (event.key.toLowerCase() === 's') && currentFilePath) {
        event.preventDefault();
        handleSaveFile();
      }

      // Toggle theme with Ctrl+Shift+T or Cmd+Shift+T (case insensitive)
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && (event.key.toLowerCase() === 't')) {
        event.preventDefault();
        onThemeChange(theme === 'dark' ? 'light' : 'dark');
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentFilePath, theme, onThemeChange, fileContent]); // Add fileContent to dependencies

  return (
    <ThemeProvider theme={theme === 'dark' ? darkTheme : lightTheme}>
      <Container>
        <SplitPaneStyles />
        <ShortcutsManager shortcuts={shortcuts} enabled={true} />

        <SplitPane
          split="vertical"
          minSize={170}
          maxSize={500}
          defaultSize={explorerSize}
          onChange={(size: number) => setExplorerSize(size)}
          style={{ position: 'relative', height: 'calc(100vh - 22px)' }}
          pane1Style={{ minWidth: 0 }}
        >
          <FileExplorerContainer>
            <FileExplorer onFileSelect={handleFileSelect} />
          </FileExplorerContainer>

          <div style={{ position: 'relative', width: '100%', height: '100%' }}>
            {isAgentPanelVisible ? (
              <SplitPane
                split="vertical"
                minSize={400}
                defaultSize={`calc(100% - ${agentSize}px)`}
                primary="first"
                onChange={(size: number | string) => {
                  const containerWidth = document.getElementById('main-container')?.clientWidth || 0;
                  setAgentSize(containerWidth - (typeof size === 'number' ? size : parseInt(size as string, 10)));
                }}
                style={{ position: 'relative' }}
                pane1Style={{ minWidth: 0 }}
              >
                <SplitPane
                  split="horizontal"
                  minSize={100}
                  defaultSize={`calc(100% - ${terminalSize}px)`}
                  primary="first"
                  onChange={(size: number | string) => {
                    const containerHeight = document.getElementById('main-container')?.clientHeight || 0;
                    setTerminalSize(containerHeight - (typeof size === 'number' ? size : parseInt(size as string, 10)));
                  }}
                  style={{ position: 'relative' }}
                  pane1Style={{ minHeight: 0 }}
                  id="main-container"
                >
                  <MainWrapper>
                    {isKanbanBoardActive ? (
                      <KanbanIntegration />
                    ) : currentFilePath ? (
                      <MonacoEditorComponent
                        value={fileContent}
                        language={language}
                        onChange={handleContentChange}
                        theme={theme === 'dark' ? 'vs-dark' : 'light'}
                        preferences={editorPreferences}
                      />
                    ) : (
                      <WelcomeMessage>
                        <h2>Welcome to Middlware</h2>
                        <p>Open a file from the explorer to start editing</p>
                        <p>or create a new file with the right-click menu.</p>
                        <ShortcutList>
                          <ShortcutItem>
                            <kbd>Ctrl+P</kbd> or <kbd>⌘P</kbd> - Open Command Palette
                          </ShortcutItem>
                          <ShortcutItem>
                            <kbd>Ctrl+S</kbd> or <kbd>⌘S</kbd> - Save File
                          </ShortcutItem>
                          <ShortcutItem>
                            <kbd>Ctrl+Shift+T</kbd> or <kbd>⌘Shift+T</kbd> - Toggle Theme
                          </ShortcutItem>
                          <ShortcutItem>
                            <kbd>Ctrl+K S</kbd> or <kbd>⌘K S</kbd> - Show All Shortcuts
                          </ShortcutItem>
                        </ShortcutList>
                      </WelcomeMessage>
                    )}
                  </MainWrapper>
                  <TerminalWrapper>
                    <ModernTerminalTabs
                      projectPath={currentFilePath ? window.electron.path.dirname(currentFilePath) : undefined}
                    />
                  </TerminalWrapper>
                </SplitPane>
                <AgentPanelContainer>
                  <AgentPanel
                    currentFilePath={currentFilePath}
                    fileContent={fileContent}
                    onAgentResponseApply={handleAgentResponseApply}
                    theme={theme}
                  />
                </AgentPanelContainer>
              </SplitPane>
            ) : (
              <SplitPane
                split="horizontal"
                minSize={100}
                defaultSize={`calc(100% - ${terminalSize}px)`}
                primary="first"
                onChange={(size: number | string) => {
                  const containerHeight = document.getElementById('main-container')?.clientHeight || 0;
                  setTerminalSize(containerHeight - (typeof size === 'number' ? size : parseInt(size as string, 10)));
                }}
                style={{ position: 'relative' }}
                pane1Style={{ minHeight: 0 }}
                id="main-container"
              >
                <MainWrapper>
                  {isKanbanBoardActive ? (
                    <KanbanIntegration />
                  ) : currentFilePath ? (
                    <MonacoEditorComponent
                      value={fileContent}
                      language={language}
                      onChange={handleContentChange}
                      theme={theme === 'dark' ? 'vs-dark' : 'light'}
                      preferences={editorPreferences}
                    />
                  ) : (
                    <WelcomeMessage>
                      <h2>Welcome to Middlware</h2>
                      <p>Open a file from the explorer to start editing</p>
                      <p>or create a new file with the right-click menu.</p>
                      <ShortcutList>
                        <ShortcutItem>
                          <kbd>Ctrl+P</kbd> or <kbd>⌘P</kbd> - Open Command Palette
                        </ShortcutItem>
                        <ShortcutItem>
                          <kbd>Ctrl+S</kbd> or <kbd>⌘S</kbd> - Save File
                        </ShortcutItem>
                        <ShortcutItem>
                          <kbd>Ctrl+Shift+T</kbd> or <kbd>⌘Shift+T</kbd> - Toggle Theme
                        </ShortcutItem>
                        <ShortcutItem>
                          <kbd>Ctrl+K S</kbd> or <kbd>⌘K S</kbd> - Show All Shortcuts
                        </ShortcutItem>
                      </ShortcutList>
                    </WelcomeMessage>
                  )}
                </MainWrapper>
                <TerminalWrapper>
                  <ModernTerminalTabs
                    projectPath={currentFilePath ? window.electron.path.dirname(currentFilePath) : undefined}
                  />
                </TerminalWrapper>
              </SplitPane>
            )}
          </div>
        </SplitPane>

        <AppSidebarWrapper>
          <AppSidebar
            activeItem={activeSidebarItem}
            onItemClick={handleSidebarAction}
          />
        </AppSidebarWrapper>

        <StatusBar>
          <LeftItems>
            <LanguageSelector value={language} onChange={(e) => setLanguage(e.target.value)}>
              <option value="javascript">JavaScript</option>
              <option value="typescript">TypeScript</option>
              <option value="html">HTML</option>
              <option value="css">CSS</option>
              <option value="json">JSON</option>
              <option value="php">PHP</option>
              <option value="python">Python</option>
              <option value="java">Java</option>
              <option value="cpp">C/C++</option>
              <option value="csharp">C#</option>
              <option value="go">Go</option>
              <option value="ruby">Ruby</option>
              <option value="rust">Rust</option>
              <option value="swift">Swift</option>
              <option value="kotlin">Kotlin</option>
              <option value="xml">XML</option>
              <option value="yaml">YAML</option>
              <option value="sql">SQL</option>
              <option value="shell">Shell</option>
              <option value="powershell">PowerShell</option>
              <option value="dockerfile">Dockerfile</option>
              <option value="markdown">Markdown</option>
              <option value="plaintext">Plain Text</option>
            </LanguageSelector>

            {currentFilePath && (
              <CurrentFile>
                {getBasename(currentFilePath)}
                {isModified && ' *'}
              </CurrentFile>
            )}
            {isModified && currentFilePath && (
              <SaveButton onClick={handleSaveFile} title="Save File (Ctrl+S)">
                <Save size={14} />
              </SaveButton>
            )}
          </LeftItems>

          <RightItems>
            {isGitRepo && gitBranch && (
              <GitBranchInfo>
                <GitBranch size={14} /> {gitBranch}
              </GitBranchInfo>
            )}
            <CommandPaletteButton onClick={() => setIsCommandPaletteOpen(true)}>
              <CommandIcon size={14} />
            </CommandPaletteButton>
            <PreferencesButton onClick={() => setIsPreferencesPanelOpen(true)}>
              <Settings size={14} />
            </PreferencesButton>
            <ShortcutsButton onClick={() => setIsShortcutsPanelOpen(true)}>
              <Keyboard size={14} />
            </ShortcutsButton>
            <AgentButton
              onClick={() => setIsAgentPanelVisible(prev => !prev)}
              isActive={isAgentPanelVisible}
            >
              <Bot size={14} />
              <AgentButtonText>
                {isAgentPanelVisible ? 'Hide Agent' : 'Show Agent'}
              </AgentButtonText>
            </AgentButton>
            <ThemeToggle onClick={() => onThemeChange(theme === 'dark' ? 'light' : 'dark')}>
              {theme === 'dark' ? <Sun size={14} /> : <Moon size={14} />}
              <span>{theme === 'dark' ? 'Light' : 'Dark'}</span>
            </ThemeToggle>
            <StatusInfo>Middlware - Ready</StatusInfo>
          </RightItems>
        </StatusBar>

        <CommandPalette
          isOpen={isCommandPaletteOpen}
          onClose={() => setIsCommandPaletteOpen(false)}
          commands={commands}
        />

        <EditorPreferencesPanel
          isOpen={isPreferencesPanelOpen}
          onClose={() => setIsPreferencesPanelOpen(false)}
          preferences={editorPreferences}
          onPreferencesChange={handleEditorPreferencesChange}
        />

        <KeyboardShortcutsPanel
          isOpen={isShortcutsPanelOpen}
          onClose={() => setIsShortcutsPanelOpen(false)}
          shortcuts={shortcuts}
        />
      </Container>
    </ThemeProvider>
  );
};

const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text.primary};
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 13px;
  line-height: 1.4;
`;



const AppSidebarWrapper = styled.div`
  position: fixed;
  right: 0;
  top: 0;
  bottom: 22px;
  width: 48px;
  z-index: 10;
  background-color: ${({ theme }) => theme.mode === 'dark' ? '#333333' : '#2c2c2c'};
  border-left: 1px solid ${({ theme }) => theme.colors.border};
`;

const AgentPanelContainer = styled.div`
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.colors.background};
  border-left: 1px solid ${({ theme }) => theme.colors.border};
`;

const FileExplorerContainer = styled.div`
  height: 100%;
  overflow: hidden;
  background-color: ${({ theme }) => theme.mode === 'dark' ? '#252526' : '#f3f3f3'};
  border-right: 1px solid ${({ theme }) => theme.colors.border};
`;

const TerminalWrapper = styled.div`
  height: 100%;
  overflow: hidden;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
`;

const MainWrapper = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;



const StatusBar = styled.div`
  height: 22px;
  background-color: ${({ theme }) => theme.mode === 'dark' ? '#007acc' : '#007acc'};
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  font-size: 12px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  z-index: 10;
  box-shadow: 0 -1px 0 rgba(0, 0, 0, 0.2) inset;
`;

const LeftItems = styled.div`
  display: flex;
  align-items: center;
  height: 100%;
`;

const RightItems = styled.div`
  display: flex;
  align-items: center;
  height: 100%;
`;

const StatusBarItem = styled.div`
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 8px;
  font-size: 12px;

  &:hover {
    background-color: rgba(255, 255, 255, 0.12);
  }
`;

const LanguageSelector = styled.select`
  background-color: transparent;
  color: white;
  border: none;
  font-size: 12px;
  cursor: pointer;
  padding: 0 4px;
  height: 100%;
  appearance: none;

  &:hover {
    background-color: rgba(255, 255, 255, 0.12);
  }

  option {
    background-color: ${({ theme }) => theme.colors.background};
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const CurrentFile = styled(StatusBarItem)`
  font-size: 12px;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
`;

const ThemeToggle = styled(StatusBarItem)`
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
`;

const StatusInfo = styled(StatusBarItem)`
  font-size: 12px;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
`;

const WelcomeMessage = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
  text-align: center;

  h2 {
    margin-bottom: 16px;
    color: ${({ theme }) => theme.colors.text.primary};
    font-weight: 400;
    font-size: 24px;
  }

  p {
    margin: 4px 0;
    color: ${({ theme }) => theme.colors.text.secondary};
    font-size: 14px;
  }
`;

const ShortcutList = styled.ul`
  margin-top: 20px;
  list-style: none;
  padding: 0;
`;

const ShortcutItem = styled.li`
  margin: 8px 0;
  color: ${({ theme }) => theme.colors.text.secondary};

  kbd {
    background-color: ${({ theme }) => theme.colors.foreground};
    border: 1px solid ${({ theme }) => theme.colors.border};
    border-radius: 3px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    color: ${({ theme }) => theme.colors.text.primary};
    display: inline-block;
    font-size: 0.85em;
    font-family: 'Segoe UI', sans-serif;
    padding: 2px 5px;
    margin: 0 3px;
  }
`;

const CommandPaletteButton = styled(StatusBarItem)`
  cursor: pointer;
  display: flex;
  align-items: center;
`;

const PreferencesButton = styled(StatusBarItem)`
  cursor: pointer;
  display: flex;
  align-items: center;
`;

const ShortcutsButton = styled(StatusBarItem)`
  cursor: pointer;
  display: flex;
  align-items: center;
`;

const GitBranchInfo = styled(StatusBarItem)`
  display: flex;
  align-items: center;
  gap: 4px;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
`;





const AgentButton = styled(StatusBarItem)<{ isActive: boolean }>`
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  background: ${({ isActive }) => isActive ? 'rgba(255, 255, 255, 0.18)' : 'transparent'};

  &:hover {
    background-color: rgba(255, 255, 255, 0.12);
  }
`;



const AgentButtonText = styled.span`
  @media (max-width: 1200px) {
    display: none;
  }
`;



const SaveButton = styled(StatusBarItem)`
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
`;

