// src/renderer/components/kanban/dependency-graph.tsx
import React from 'react';
import { GitBranch } from 'lucide-react';

export function DependencyGraph() {
  return (
    <div className="p-4 text-center">
      <div className="flex flex-col items-center justify-center py-8">
        <GitBranch className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">Dependency Graph</h3>
        <p className="text-sm text-muted-foreground mt-2">
          Visualize task dependencies and relationships
        </p>
      </div>
    </div>
  );
}

export default DependencyGraph;
