// src/renderer/components/kanban/CreateCardDialog.tsx
import React, { useState } from 'react';
import styled from 'styled-components';
import { X } from 'lucide-react';
import { useBoard } from '../../contexts/board-context';
import { useAgentBoardController } from '../../contexts/agent-board-controller';
import { boardIPCBridge } from '../../lib/board-ipc-bridge';

interface CreateCardDialogProps {
  columnId: string;
  swimlaneId: string;
  onClose: () => void;
  onCreateCard?: (cardData: any) => void;
}

const CreateCardDialog: React.FC<CreateCardDialogProps> = ({
  columnId,
  swimlaneId,
  onClose,
  onCreateCard
}) => {
  const { activeBoard, addCardToColumn } = useBoard();
  const { agents } = useAgentBoardController();

  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [cardType, setCardType] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [dueDate, setDueDate] = useState('');
  const [selectedAgentId, setSelectedAgentId] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim() || !activeBoard) return;

    const newCard = {
      title: title.trim(),
      description: description.trim() || undefined,
      columnId,
      swimlaneId,
      type: cardType || undefined,
      priority,
      dueDate: dueDate || undefined,
      progress: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const cardId = await addCardToColumn(activeBoard.id, columnId, newCard);

    // If an agent is selected, assign it to the card
    if (selectedAgentId && cardId) {
      const agent = agents.find(a => a.id === selectedAgentId);
      if (agent) {
        try {
          await boardIPCBridge.assignAgent(cardId, selectedAgentId, agent.type);
          console.log(`Agent ${selectedAgentId} assigned to card ${cardId}`);
        } catch (error) {
          console.error('Failed to assign agent to card:', error);
        }
      }
    }

    // Call onCreateCard prop if provided
    if (onCreateCard && cardId) {
      onCreateCard({ ...newCard, id: cardId });
    }

    onClose();
  };

  return (
    <DialogOverlay onClick={onClose}>
      <DialogContent onClick={e => e.stopPropagation()}>
        <DialogHeader>
          <DialogTitle>Create New Card</DialogTitle>
          <CloseButton onClick={onClose}>
            <X size={18} />
          </CloseButton>
        </DialogHeader>

        <DialogForm onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="card-title">Title</Label>
            <Input
              id="card-title"
              value={title}
              onChange={e => setTitle(e.target.value)}
              placeholder="Enter card title"
              autoFocus
              required
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="card-description">Description (optional)</Label>
            <TextArea
              id="card-description"
              value={description}
              onChange={e => setDescription(e.target.value)}
              placeholder="Enter card description"
              rows={3}
            />
          </FormGroup>

          <FormRow>
            <FormGroup style={{ flex: 1 }}>
              <Label htmlFor="card-type">Card Type</Label>
              <Select
                id="card-type"
                value={cardType}
                onChange={e => setCardType(e.target.value)}
              >
                <option value="">Select a type</option>
                {activeBoard?.cardTypes.map(type => (
                  <option key={type.id} value={type.id}>
                    {type.name}
                  </option>
                ))}
              </Select>
            </FormGroup>

            <FormGroup style={{ flex: 1 }}>
              <Label htmlFor="card-priority">Priority</Label>
              <Select
                id="card-priority"
                value={priority}
                onChange={e => setPriority(e.target.value as 'low' | 'medium' | 'high')}
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </Select>
            </FormGroup>
          </FormRow>

          <FormRow>
            <FormGroup style={{ flex: 1 }}>
              <Label htmlFor="card-due-date">Due Date (optional)</Label>
              <Input
                id="card-due-date"
                type="date"
                value={dueDate}
                onChange={e => setDueDate(e.target.value)}
              />
            </FormGroup>

            <FormGroup style={{ flex: 1 }}>
              <Label htmlFor="card-agent">Assign Agent (optional)</Label>
              <Select
                id="card-agent"
                value={selectedAgentId}
                onChange={e => setSelectedAgentId(e.target.value)}
              >
                <option value="">None</option>
                {agents.map(agent => (
                  <option key={agent.id} value={agent.id}>
                    {agent.name}
                  </option>
                ))}
              </Select>
            </FormGroup>
          </FormRow>

          <DialogActions>
            <CancelButton type="button" onClick={onClose}>
              Cancel
            </CancelButton>
            <SubmitButton type="submit" disabled={!title.trim()}>
              Create Card
            </SubmitButton>
          </DialogActions>
        </DialogForm>
      </DialogContent>
    </DialogOverlay>
  );
};

const DialogOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const DialogContent = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
`;

const DialogHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  position: sticky;
  top: 0;
  background-color: ${({ theme }) => theme.colors.background};
  z-index: 1;
`;

const DialogTitle = styled.h3`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background-color: ${({ theme }) => theme.colors.foreground};
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const DialogForm = styled.form`
  padding: 16px;
`;

const FormGroup = styled.div`
  margin-bottom: 16px;
`;

const FormRow = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const DialogActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
`;

const Button = styled.button`
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
`;

const CancelButton = styled(Button)`
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.text.primary};

  &:hover {
    background-color: ${({ theme }) => theme.colors.foreground};
  }
`;

const SubmitButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.primary};
  border: 1px solid ${({ theme }) => theme.colors.primary};
  color: white;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.border};
    border-color: ${({ theme }) => theme.colors.border};
    color: ${({ theme }) => theme.colors.text.secondary};
    cursor: not-allowed;
  }
`;

export default CreateCardDialog;
