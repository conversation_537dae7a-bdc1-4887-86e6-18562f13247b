// src/renderer/components/kanban/KanbanIntegration.tsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { BoardProvider } from '../../contexts/board-context';
import { AgentBoardControllerProvider, useAgentBoardController } from '../../contexts/agent-board-controller';
import { useBoard } from '../../contexts/board-context';
import KanbanBoard from './KanbanBoard';
import AgentActivityPanel from './AgentActivityPanel';
import AgentIntegrationDialog from './AgentIntegrationDialog';
import CreateBoardDialog from './CreateBoardDialog';
import { Bot, Settings, Plus, Filter, SortAsc, Search } from 'lucide-react';
import { boardIPCBridge } from '../../lib/board-ipc-bridge';

export function KanbanIntegration() {
  const [showAgentPanel, setShowAgentPanel] = useState(false);
  const [showAgentSettings, setShowAgentSettings] = useState(false);
  const [showCreateBoardDialog, setShowCreateBoardDialog] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { isAgentSystemActive } = useAgentBoardController();
  const { activeBoard } = useBoard();

  // Load board state on component mount
  useEffect(() => {
    const loadBoardState = async () => {
      try {
        await boardIPCBridge.loadBoardState();
      } catch (error) {
        console.error('Failed to load board state:', error);
      }
    };

    loadBoardState();
  }, []);

  // Save board state when component unmounts
  useEffect(() => {
    return () => {
      const saveBoardState = async () => {
        try {
          await boardIPCBridge.saveBoardState();
        } catch (error) {
          console.error('Failed to save board state:', error);
        }
      };

      saveBoardState();
    };
  }, []);

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <HeaderTitle>
            {activeBoard?.name || 'Kanban Board'}
          </HeaderTitle>
          <CreateBoardButton
            title="Create New Board"
            onClick={() => setShowCreateBoardDialog(true)}
          >
            <Plus size={16} />
            <span>New Board</span>
          </CreateBoardButton>
        </HeaderLeft>
        <HeaderActions>
          <SearchContainer>
            <SearchIcon>
              <Search size={16} />
            </SearchIcon>
            <SearchInput
              placeholder="Search cards..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </SearchContainer>
          <HeaderButton title="Filter Cards">
            <Filter size={16} />
          </HeaderButton>
          <HeaderButton title="Sort Cards">
            <SortAsc size={16} />
          </HeaderButton>
          <HeaderButton
            title="Agent Settings"
            onClick={() => setShowAgentSettings(true)}
          >
            <Bot size={16} />
          </HeaderButton>
          <HeaderButton title="Board Settings">
            <Settings size={16} />
          </HeaderButton>
        </HeaderActions>
      </Header>

      <BoardContainer>
        <div className="flex-1 overflow-auto">
          <KanbanBoard />
        </div>
        {showAgentPanel && <AgentActivityPanel onClose={() => setShowAgentPanel(false)} />}
      </BoardContainer>

      <ToggleButton
        onClick={() => setShowAgentPanel(!showAgentPanel)}
        isActive={isAgentSystemActive}
      >
        <Bot size={16} />
        <span>{showAgentPanel ? 'Hide Agents' : 'Show Agents'}</span>
      </ToggleButton>

      <AgentIntegrationDialog
        isOpen={showAgentSettings}
        onClose={() => setShowAgentSettings(false)}
      />

      <CreateBoardDialog
        isOpen={showCreateBoardDialog}
        onClose={() => setShowCreateBoardDialog(false)}
      />
    </Container>
  );
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text.primary};
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.foreground};
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const HeaderTitle = styled.h2`
  margin: 0;
  font-size: 18px;
  font-weight: 600;
`;

const CreateBoardButton = styled.button`
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border: none;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }
`;

const HeaderActions = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const SearchContainer = styled.div`
  position: relative;
  width: 240px;
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 6px 12px 6px 32px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const HeaderButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const BoardContainer = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
`;

interface ToggleButtonProps {
  isActive?: boolean;
}

const ToggleButton = styled.button<ToggleButtonProps>`
  position: absolute;
  bottom: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: ${({ isActive, theme }) =>
    isActive ? theme.colors.primary : theme.colors.foreground};
  color: ${({ isActive, theme }) =>
    isActive ? 'white' : theme.colors.text.primary};
  border: 1px solid ${({ isActive, theme }) =>
    isActive ? theme.colors.primary : theme.colors.border};
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;

  &:hover {
    background-color: ${({ isActive, theme }) =>
      isActive ? theme.colors.primaryDark : theme.colors.background};
  }
`;

export default KanbanIntegration;
