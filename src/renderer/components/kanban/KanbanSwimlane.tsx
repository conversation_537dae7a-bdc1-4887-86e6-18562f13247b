// src/renderer/components/kanban/KanbanSwimlane.tsx
import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { Swimlane, Column, Card } from '../../contexts/board-context';
import KanbanColumn from './KanbanColumn';
import {
  ChevronRight, ChevronDown, Edit, Trash, MoreHorizontal, X,
  Users, Activity, AlertCircle, Clock, Calendar, Tag
} from 'lucide-react';
import { useDroppable } from '@dnd-kit/core';

interface KanbanSwimlaneProps {
  swimlane: Swimlane;
  columns: Column[];
  expanded: boolean;
  onToggle: () => void;
  onEdit: (name: string) => void;
  onDelete: () => void;
  getCardsForColumnAndSwimlane: (columnId: string, swimlaneId: string) => Card[];
  onAddCard: (columnId: string) => void;
  onCardUpdate?: (card: Card) => void;
  onDeleteCard?: (columnId: string, cardId: string) => void;
  isDragging?: boolean;
  activeDndColumnId?: string | null;
  activeDndSwimlaneId?: string | null;
}

const KanbanSwimlane: React.FC<KanbanSwimlaneProps> = ({
  swimlane,
  columns,
  expanded,
  onToggle,
  onEdit,
  onDelete,
  getCardsForColumnAndSwimlane,
  onAddCard,
  onCardUpdate,
  onDeleteCard,
  isDragging,
  activeDndColumnId,
  activeDndSwimlaneId
}) => {
  const [showMenu, setShowMenu] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [editName, setEditName] = useState(swimlane.name);
  const [contentHeight, setContentHeight] = useState<number | null>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Set up droppable for the swimlane
  const { setNodeRef, isOver } = useDroppable({
    id: `swimlane-${swimlane.id}`,
    data: {
      type: 'swimlane',
      swimlaneId: swimlane.id
    }
  });

  // Calculate the content height for smooth animation
  useEffect(() => {
    if (expanded && contentRef.current) {
      const height = contentRef.current.scrollHeight;
      setContentHeight(height);
    } else {
      setContentHeight(0);
    }
  }, [expanded, columns.length]);

  // Handle edit swimlane
  const handleEditSwimlane = () => {
    setShowMenu(false);
    setEditMode(true);
    setEditName(swimlane.name);
  };

  // Handle save edit
  const handleSaveEdit = () => {
    if (editName.trim()) {
      onEdit(editName.trim());
      setEditMode(false);
    }
  };

  // Handle key down in edit mode
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      setEditMode(false);
      setEditName(swimlane.name);
    }
  };

  // Handle delete swimlane
  const handleDeleteSwimlane = () => {
    setShowMenu(false);
    if (window.confirm(`Are you sure you want to delete the swimlane "${swimlane.name}"? This will also delete all cards in this swimlane.`)) {
      onDelete();
    }
  };

  // Determine if this swimlane is the active drop target
  const isDropTarget = activeDndSwimlaneId === swimlane.id;

  // Count cards in this swimlane
  const cardCount = columns.reduce((count, column) => {
    return count + getCardsForColumnAndSwimlane(column.id, swimlane.id).length;
  }, 0);

  // Get swimlane background based on name
  const getSwimlaneBackground = () => {
    const name = swimlane.name.toLowerCase();

    if (name.includes('high') || name.includes('urgent')) {
      return 'from-red-50 to-red-100';
    }
    if (name.includes('medium') || name.includes('normal')) {
      return 'from-amber-50 to-amber-100';
    }
    if (name.includes('low')) {
      return 'from-green-50 to-green-100';
    }
    if (name.includes('feature')) {
      return 'from-blue-50 to-blue-100';
    }
    if (name.includes('bug')) {
      return 'from-red-50 to-red-100';
    }
    if (name.includes('task')) {
      return 'from-purple-50 to-purple-100';
    }

    return 'from-gray-50 to-gray-100';
  };

  return (
    <SwimlaneContainer ref={setNodeRef}>
      <SwimlaneHeader
        swimlaneColor={swimlane.color}
        isDropTarget={isDropTarget && isOver}
        background={getSwimlaneBackground()}
      >
        <HeaderLeftSection>
          <ToggleButton onClick={onToggle}>
            {expanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </ToggleButton>

          {editMode ? (
            <SwimlaneEditInput
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onBlur={handleSaveEdit}
              onKeyDown={handleKeyDown}
              autoFocus
            />
          ) : (
            <HeaderTitleSection>
              <SwimlaneTitle>{swimlane.name}</SwimlaneTitle>
              <SwimlaneCardCount>{cardCount} cards</SwimlaneCardCount>
            </HeaderTitleSection>
          )}
        </HeaderLeftSection>

        <HeaderRightSection>
          <SwimlaneMetrics>
            <SwimlaneMetric title="Active cards">
              <Activity size={14} />
              <span>{cardCount}</span>
            </SwimlaneMetric>

            <SwimlaneMetric title="Assigned agents">
              <Users size={14} />
              <span>0</span>
            </SwimlaneMetric>
          </SwimlaneMetrics>

          <SwimlaneActions>
            <SwimlaneMenuContainer>
              <SwimlaneActionButton onClick={() => setShowMenu(!showMenu)} title="Swimlane Menu">
                <MoreHorizontal size={16} />
              </SwimlaneActionButton>

              {showMenu && (
                <MenuOverlay onClick={() => setShowMenu(false)}>
                  <SwimlaneMenu onClick={e => e.stopPropagation()}>
                    <SwimlaneMenuItem onClick={handleEditSwimlane}>
                      <Edit size={14} />
                      <span>Edit Swimlane</span>
                    </SwimlaneMenuItem>
                    <SwimlaneMenuItem onClick={handleDeleteSwimlane}>
                      <Trash size={14} />
                      <span>Delete Swimlane</span>
                    </SwimlaneMenuItem>
                    <SwimlaneMenuClose onClick={() => setShowMenu(false)}>
                      <X size={14} />
                    </SwimlaneMenuClose>
                  </SwimlaneMenu>
                </MenuOverlay>
              )}
            </SwimlaneMenuContainer>
          </SwimlaneActions>
        </HeaderRightSection>
      </SwimlaneHeader>

      <SwimlaneContent
        ref={contentRef}
        isExpanded={expanded}
        height={contentHeight}
      >
        <ColumnsContainer>
          {columns.map(column => (
            <KanbanColumn
              key={column.id}
              column={column}
              swimlaneId={swimlane.id}
              cards={getCardsForColumnAndSwimlane(column.id, swimlane.id)}
              onAddCard={() => onAddCard(column.id)}
              onCardUpdate={onCardUpdate}
              onDeleteCard={(cardId) => onDeleteCard && onDeleteCard(column.id, cardId)}
              isDragging={isDragging}
              isDropTarget={activeDndColumnId === column.id && activeDndSwimlaneId === swimlane.id}
            />
          ))}
        </ColumnsContainer>
      </SwimlaneContent>
    </SwimlaneContainer>
  );
};

const SwimlaneContainer = styled.div`
  display: flex;
  flex-direction: column;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 6px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s;

  &:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
`;

interface SwimlaneHeaderProps {
  swimlaneColor?: string;
  isDropTarget?: boolean;
  background?: string;
}

const SwimlaneHeader = styled.div<SwimlaneHeaderProps>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: ${({ theme }) => theme.colors.foreground};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  border-left: ${({ swimlaneColor }) => swimlaneColor ? `4px solid ${swimlaneColor}` : 'none'};
  transition: all 0.2s;

  ${({ isDropTarget, theme }) => isDropTarget && `
    box-shadow: 0 0 0 2px ${theme.colors.primary};
  `}
`;

const HeaderLeftSection = styled.div`
  display: flex;
  align-items: center;
`;

const HeaderRightSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const HeaderTitleSection = styled.div`
  display: flex;
  flex-direction: column;
`;

const ToggleButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  margin-right: 8px;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    color: ${({ theme }) => theme.colors.text.primary};
    background-color: rgba(0, 0, 0, 0.05);
  }
`;

const SwimlaneTitle = styled.h3`
  margin: 0;
  font-size: 14px;
  font-weight: 500;
`;

const SwimlaneCardCount = styled.span`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
  margin-top: 2px;
`;

const SwimlaneMetrics = styled.div`
  display: flex;
  gap: 12px;
`;

const SwimlaneMetric = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const SwimlaneEditInput = styled.input`
  flex: 1;
  padding: 6px 10px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.primary};
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;
  min-width: 200px;

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary}20;
  }
`;

const SwimlaneActions = styled.div`
  display: flex;
  gap: 4px;
`;

const SwimlaneMenuContainer = styled.div`
  position: relative;
`;

const MenuOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
`;

const SwimlaneActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const SwimlaneMenu = styled.div`
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 101;
  width: 160px;
  background-color: ${({ theme }) => theme.colors.foreground};
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

const SwimlaneMenuItem = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  color: ${({ theme }) => theme.colors.text.primary};
  cursor: pointer;
  font-size: 13px;

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;

const SwimlaneMenuClose = styled.button`
  position: absolute;
  top: 4px;
  right: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

interface SwimlaneContentProps {
  isExpanded: boolean;
  height: number | null;
}

const SwimlaneContent = styled.div<SwimlaneContentProps>`
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
  max-height: ${({ isExpanded, height }) => isExpanded ? `${height}px` : '0px'};
`;

const ColumnsContainer = styled.div`
  display: flex;
  min-height: 200px;
  padding: 12px;
  gap: 12px;
`;

export default KanbanSwimlane;
