// src/renderer/components/kanban/AgentActivityPanel.tsx
import React, { useState, useEffect } from 'react';
import { useAgentBoardController } from '../../contexts/agent-board-controller';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import {
  Cpu,
  FileText,
  Code,
  TestTube,
  X
} from 'lucide-react';

// Create simple Avatar components since we don't have the ui/avatar module
const Avatar = ({ className, children, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={`relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-muted ${className || ''}`}
    {...props}
  />
);

const AvatarFallback = ({ className, children, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={`flex h-full w-full items-center justify-center rounded-full bg-muted ${className || ''}`}
    {...props}
  >
    {children}
  </div>
);

interface AgentActivityPanelProps {
  onClose?: () => void;
}

const AgentActivityPanel: React.FC<AgentActivityPanelProps> = ({ onClose }) => {
  const [activeTab, setActiveTab] = useState("all");
  const {
    agents,
    isAgentSystemActive,
    toggleAgentSystem,
    pauseAgent,
    resumeAgent
  } = useAgentBoardController();

  const [activeAgents, setActiveAgents] = useState<Set<string>>(new Set());

  // Update active agents
  useEffect(() => {
    const interval = setInterval(() => {
      const workingAgents = agents.filter((agent) => agent.status === "working").map((agent) => agent.id);
      setActiveAgents(new Set(workingAgents));
    }, 1000);

    return () => clearInterval(interval);
  }, [agents]);

  // Filter agents based on active tab
  const filteredAgents = agents.filter((agent) => {
    if (activeTab === "all") return true;
    if (activeTab === "working") return agent.status === "working";
    if (activeTab === "idle") return agent.status === "idle";
    if (activeTab === "paused") return agent.status === "paused";
    return true;
  });

  // Get agent icon based on type
  const getAgentIcon = (type: string) => {
    switch (type) {
      case "task-manager":
        return <FileText className="h-6 w-6 text-blue-500" />;
      case "developer":
        return <Code className="h-6 w-6 text-green-500" />;
      case "tester":
        return <TestTube className="h-6 w-6 text-purple-500" />;
      case "documentation":
        return <FileText className="h-6 w-6 text-yellow-500" />;
      default:
        return <Cpu className="h-6 w-6 text-gray-500" />;
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "working":
        return "bg-green-500";
      case "idle":
        return "bg-blue-500";
      case "paused":
        return "bg-yellow-500";
      case "error":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="w-full max-w-xs border-l border-border h-full bg-background">
      <div className="p-4 flex justify-between items-center border-b border-border">
        <h2 className="text-lg font-semibold">Agent Activity</h2>
        {onClose && (
          <Button variant="ghost" size="icon" onClick={onClose} className="ml-auto mr-2">
            <X className="h-4 w-4" />
          </Button>
        )}
        <Button
          variant={isAgentSystemActive ? "destructive" : "default"}
          onClick={toggleAgentSystem}
          size="sm"
        >
          {isAgentSystemActive ? "Deactivate Agents" : "Activate Agents"}
        </Button>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <div className="px-2 border-b border-border">
          <TabsList className="w-full">
            <TabsTrigger value="all" className="flex-1">
              All
            </TabsTrigger>
            <TabsTrigger value="working" className="flex-1">
              Working
            </TabsTrigger>
            <TabsTrigger value="idle" className="flex-1">
              Idle
            </TabsTrigger>
            <TabsTrigger value="paused" className="flex-1">
              Paused
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value={activeTab} className="mt-0 p-2 overflow-auto max-h-[calc(100vh-12rem)]">
          {filteredAgents.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">No agents in this category</div>
          ) : (
            filteredAgents.map((agent) => (
              <Card
                key={agent.id}
                className={`mb-3 ${activeAgents.has(agent.id) ? "shadow-md border-primary/50 animate-pulse" : ""}`}
              >
                <CardContent className="p-3">
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarFallback>{getAgentIcon(agent.type)}</AvatarFallback>
                    </Avatar>

                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div className="font-medium flex items-center gap-2">
                          {agent.name}
                          <span className={`h-2 w-2 rounded-full ${getStatusColor(agent.status || 'idle')}`}></span>
                        </div>

                        {agent.status === "working" && (
                          <Button variant="outline" size="sm" onClick={() => pauseAgent(agent.id)}>
                            Pause
                          </Button>
                        )}

                        {agent.status === "paused" && (
                          <Button variant="outline" size="sm" onClick={() => resumeAgent(agent.id)}>
                            Resume
                          </Button>
                        )}
                      </div>

                      <div className="text-sm text-muted-foreground">{agent.type}</div>

                      {agent.resourceUsage && (
                        <div className="mt-2 text-xs grid grid-cols-3 gap-1">
                          <div>CPU: {agent.resourceUsage.cpu}%</div>
                          <div>Memory: {agent.resourceUsage.memory} MB</div>
                          <div>Tokens: {agent.resourceUsage.tokens}</div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};



export default AgentActivityPanel;
