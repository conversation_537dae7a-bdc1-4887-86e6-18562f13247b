// src/renderer/components/kanban/CardDetailsDialog.tsx
import React, { useState } from 'react';
import styled from 'styled-components';
import { X, Edit, Trash, Clock, Tag, Bot, MessageSquare } from 'lucide-react';
import { Card, useBoard } from '../../contexts/board-context';
import { useAgentBoardController } from '../../contexts/agent-board-controller';

interface CardDetailsDialogProps {
  card: Card;
  onClose: () => void;
  onUpdate?: (updatedCard: Card) => void;
  onDelete?: () => void;
}

const CardDetailsDialog: React.FC<CardDetailsDialogProps> = ({
  card,
  onClose,
  onUpdate,
  onDelete: onDeleteProp
}) => {
  const { activeBoard, updateCardInColumn, deleteCardFromColumn } = useBoard();
  const { agents, assignTaskToAgent, unassignTaskFromAgent } = useAgentBoardController();

  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState(card.title);
  const [description, setDescription] = useState(card.description || '');
  const [cardType, setCardType] = useState(card.type || '');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>(card.priority || 'medium');
  const [dueDate, setDueDate] = useState(card.dueDate || '');
  const [progress, setProgress] = useState(card.progress || 0);

  // Get card type
  const cardTypeObj = activeBoard?.cardTypes.find(type => type.id === card.type);

  // Get assigned agents
  const assignedAgents = agents.filter(agent =>
    card.agentAssignments?.some(assignment => assignment.agentId === agent.id)
  );

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  // Handle save changes
  const handleSaveChanges = () => {
    if (!title.trim() || !activeBoard) return;

    const updatedCard = {
      ...card,
      title: title.trim(),
      description: description.trim() || undefined,
      type: cardType || undefined,
      priority,
      dueDate: dueDate || undefined,
      progress: progress || undefined
    };

    // Update card in context
    updateCardInColumn(activeBoard.id, card.id, updatedCard);

    // Call onUpdate prop if provided
    if (onUpdate) {
      onUpdate(updatedCard);
    }

    setIsEditing(false);
  };

  // Handle delete card
  const handleDeleteCard = () => {
    if (!activeBoard) return;

    if (window.confirm('Are you sure you want to delete this card?')) {
      // Delete card from context
      deleteCardFromColumn(activeBoard.id, card.id);

      // Call onDelete prop if provided
      if (onDeleteProp) {
        onDeleteProp();
      }

      onClose();
    }
  };

  // Handle assign agent
  const handleAssignAgent = async (agentId: string) => {
    await assignTaskToAgent(card.id, agentId);
  };

  // Handle unassign agent
  const handleUnassignAgent = async (agentId: string) => {
    await unassignTaskFromAgent(card.id, agentId);
  };

  return (
    <DialogOverlay onClick={onClose}>
      <DialogContent onClick={e => e.stopPropagation()}>
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Card' : 'Card Details'}
          </DialogTitle>
          <DialogActions>
            {!isEditing && (
              <>
                <ActionButton onClick={() => setIsEditing(true)} title="Edit Card">
                  <Edit size={16} />
                </ActionButton>
                <ActionButton onClick={handleDeleteCard} title="Delete Card">
                  <Trash size={16} />
                </ActionButton>
              </>
            )}
            <CloseButton onClick={onClose}>
              <X size={18} />
            </CloseButton>
          </DialogActions>
        </DialogHeader>

        <DialogBody>
          {isEditing ? (
            <EditForm>
              <FormGroup>
                <Label htmlFor="card-title">Title</Label>
                <Input
                  id="card-title"
                  value={title}
                  onChange={e => setTitle(e.target.value)}
                  placeholder="Enter card title"
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="card-description">Description (optional)</Label>
                <TextArea
                  id="card-description"
                  value={description}
                  onChange={e => setDescription(e.target.value)}
                  placeholder="Enter card description"
                  rows={4}
                />
              </FormGroup>

              <FormRow>
                <FormGroup style={{ flex: 1 }}>
                  <Label htmlFor="card-type">Card Type</Label>
                  <Select
                    id="card-type"
                    value={cardType}
                    onChange={e => setCardType(e.target.value)}
                  >
                    <option value="">Select a type</option>
                    {activeBoard?.cardTypes.map(type => (
                      <option key={type.id} value={type.id}>
                        {type.name}
                      </option>
                    ))}
                  </Select>
                </FormGroup>

                <FormGroup style={{ flex: 1 }}>
                  <Label htmlFor="card-priority">Priority</Label>
                  <Select
                    id="card-priority"
                    value={priority}
                    onChange={e => setPriority(e.target.value as 'low' | 'medium' | 'high')}
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </Select>
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup style={{ flex: 1 }}>
                  <Label htmlFor="card-due-date">Due Date (optional)</Label>
                  <Input
                    id="card-due-date"
                    type="date"
                    value={dueDate}
                    onChange={e => setDueDate(e.target.value)}
                  />
                </FormGroup>

                <FormGroup style={{ flex: 1 }}>
                  <Label htmlFor="card-progress">Progress (%)</Label>
                  <Input
                    id="card-progress"
                    type="number"
                    min="0"
                    max="100"
                    value={progress}
                    onChange={e => setProgress(Number(e.target.value))}
                  />
                </FormGroup>
              </FormRow>

              <FormActions>
                <CancelButton type="button" onClick={() => setIsEditing(false)}>
                  Cancel
                </CancelButton>
                <SaveButton
                  type="button"
                  onClick={handleSaveChanges}
                  disabled={!title.trim()}
                >
                  Save Changes
                </SaveButton>
              </FormActions>
            </EditForm>
          ) : (
            <CardDetails>
              <CardTitle>{card.title}</CardTitle>

              {card.description && (
                <CardDescription>{card.description}</CardDescription>
              )}

              <CardMetadata>
                {cardTypeObj && (
                  <MetadataItem>
                    <MetadataLabel>
                      <Tag size={14} />
                      <span>Type:</span>
                    </MetadataLabel>
                    <MetadataValue style={{ color: cardTypeObj.color }}>
                      {cardTypeObj.name}
                    </MetadataValue>
                  </MetadataItem>
                )}

                <MetadataItem>
                  <MetadataLabel>
                    <span>Priority:</span>
                  </MetadataLabel>
                  <MetadataValue>
                    {card.priority || 'Medium'}
                  </MetadataValue>
                </MetadataItem>

                {card.dueDate && (
                  <MetadataItem>
                    <MetadataLabel>
                      <Clock size={14} />
                      <span>Due Date:</span>
                    </MetadataLabel>
                    <MetadataValue>
                      {formatDate(card.dueDate)}
                    </MetadataValue>
                  </MetadataItem>
                )}

                {card.progress !== undefined && (
                  <MetadataItem>
                    <MetadataLabel>
                      <span>Progress:</span>
                    </MetadataLabel>
                    <ProgressBar>
                      <ProgressFill progress={card.progress} />
                      <ProgressText>{card.progress}%</ProgressText>
                    </ProgressBar>
                  </MetadataItem>
                )}
              </CardMetadata>

              <SectionTitle>
                <Bot size={16} />
                <span>Assigned Agents</span>
              </SectionTitle>

              {assignedAgents.length > 0 ? (
                <AgentsList>
                  {assignedAgents.map(agent => (
                    <AgentItem key={agent.id}>
                      <AgentName>{agent.name}</AgentName>
                      <AgentType>{agent.type}</AgentType>
                      <AgentStatus status={agent.status || 'idle'}>
                        {agent.status || 'idle'}
                      </AgentStatus>
                      <UnassignButton
                        onClick={() => handleUnassignAgent(agent.id)}
                        title="Unassign agent"
                      >
                        <X size={14} />
                      </UnassignButton>
                    </AgentItem>
                  ))}
                </AgentsList>
              ) : (
                <EmptyMessage>No agents assigned</EmptyMessage>
              )}

              <SectionTitle>
                <span>Assign New Agent</span>
              </SectionTitle>

              <AgentSelector>
                {agents
                  .filter(agent => !assignedAgents.some(a => a.id === agent.id))
                  .map(agent => (
                    <AgentOption
                      key={agent.id}
                      onClick={() => handleAssignAgent(agent.id)}
                    >
                      <AgentName>{agent.name}</AgentName>
                      <AgentType>{agent.type}</AgentType>
                    </AgentOption>
                  ))}
              </AgentSelector>

              {card.taskHistory && card.taskHistory.length > 0 && (
                <>
                  <SectionTitle>
                    <MessageSquare size={16} />
                    <span>Activity History</span>
                  </SectionTitle>

                  <ActivityList>
                    {card.taskHistory.map((activity, index) => (
                      <ActivityItem key={index}>
                        <ActivityTime>
                          {formatTimestamp(activity.timestamp)}
                        </ActivityTime>
                        <ActivityDetails>
                          <strong>{activity.action}</strong>
                          {activity.details && `: ${activity.details}`}
                          {activity.agentId && (
                            <AgentTag>
                              {agents.find(a => a.id === activity.agentId)?.name || activity.agentId}
                            </AgentTag>
                          )}
                        </ActivityDetails>
                      </ActivityItem>
                    ))}
                  </ActivityList>
                </>
              )}
            </CardDetails>
          )}
        </DialogBody>
      </DialogContent>
    </DialogOverlay>
  );
};

const DialogOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const DialogContent = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 8px;
  width: 600px;
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
`;

const DialogHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const DialogTitle = styled.h3`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
`;

const DialogActions = styled.div`
  display: flex;
  gap: 8px;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;

  &:hover {
    background-color: ${({ theme }) => theme.colors.foreground};
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const CloseButton = styled(ActionButton)``;

const DialogBody = styled.div`
  padding: 16px;
  overflow-y: auto;
  flex: 1;
`;

const EditForm = styled.div``;

const FormGroup = styled.div`
  margin-bottom: 16px;
`;

const FormRow = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const FormActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
`;

const Button = styled.button`
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
`;

const CancelButton = styled(Button)`
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.text.primary};

  &:hover {
    background-color: ${({ theme }) => theme.colors.foreground};
  }
`;

const SaveButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.primary};
  border: 1px solid ${({ theme }) => theme.colors.primary};
  color: white;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.border};
    border-color: ${({ theme }) => theme.colors.border};
    color: ${({ theme }) => theme.colors.text.secondary};
    cursor: not-allowed;
  }
`;

const CardDetails = styled.div``;

const CardTitle = styled.h2`
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
`;

const CardDescription = styled.p`
  margin: 0 0 24px 0;
  font-size: 14px;
  line-height: 1.5;
  color: ${({ theme }) => theme.colors.text.secondary};
  white-space: pre-wrap;
`;

const CardMetadata = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.foreground};
  border-radius: 8px;
`;

const MetadataItem = styled.div`
  display: flex;
  align-items: center;
`;

const MetadataLabel = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100px;
  font-size: 14px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const MetadataValue = styled.div`
  font-size: 14px;
  flex: 1;
`;

interface ProgressFillProps {
  progress: number;
}

const ProgressBar = styled.div`
  flex: 1;
  height: 8px;
  background-color: ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  position: relative;
  overflow: hidden;
`;

const ProgressFill = styled.div<ProgressFillProps>`
  height: 100%;
  width: ${({ progress }) => `${progress}%`};
  background-color: ${({ theme, progress }) =>
    progress < 30 ? theme.colors.error :
    progress < 70 ? theme.colors.warning :
    theme.colors.success};
`;

const ProgressText = styled.span`
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const SectionTitle = styled.h4`
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 24px 0 16px;
  font-size: 15px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const AgentsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const AgentItem = styled.div`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: ${({ theme }) => theme.colors.foreground};
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

const AgentName = styled.span`
  font-size: 14px;
  font-weight: 500;
  flex: 1;
`;

const AgentType = styled.span`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
  margin-right: 12px;
`;

interface AgentStatusProps {
  status: string;
}

const AgentStatus = styled.span<AgentStatusProps>`
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  background-color: ${({ theme, status }) =>
    status === 'working' ? `${theme.colors.success}20` :
    status === 'paused' ? `${theme.colors.warning}20` :
    status === 'error' ? `${theme.colors.error}20` :
    `${theme.colors.text.secondary}20`
  };
  color: ${({ theme, status }) =>
    status === 'working' ? theme.colors.success :
    status === 'paused' ? theme.colors.warning :
    status === 'error' ? theme.colors.error :
    theme.colors.text.secondary
  };
  margin-right: 12px;
`;

const UnassignButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
    color: ${({ theme }) => theme.colors.error};
  }
`;

const EmptyMessage = styled.p`
  margin: 0;
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text.secondary};
  font-style: italic;
`;

const AgentSelector = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
`;

const AgentOption = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background-color: ${({ theme }) => theme.colors.foreground};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary};
    background-color: ${({ theme }) => theme.colors.primary}10;
  }
`;

const ActivityList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const ActivityItem = styled.div`
  padding: 8px 12px;
  background-color: ${({ theme }) => theme.colors.foreground};
  border-radius: 4px;
  font-size: 13px;
`;

const ActivityTime = styled.div`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
  margin-bottom: 4px;
`;

const ActivityDetails = styled.div`
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
`;

const AgentTag = styled.span`
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.primary}20;
  color: ${({ theme }) => theme.colors.primary};
  margin-left: 4px;
`;

export default CardDetailsDialog;
