// src/renderer/components/kanban/analytics-dashboard.tsx
import React, { useState, useContext, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "../ui/tabs";
import { useAgentBoardController } from "../../contexts/agent-board-controller";
import styled from 'styled-components';

// Define types for data structures
type TimeRangeKey = "7d" | "30d" | "90d";

interface TaskCompletionData {
  "7d": number[];
  "30d": number[];
  "90d": number[];
}

interface AgentPerformance {
  tasksCompleted: number;
  successRate: number;
  avgCompletionTime: number; // in minutes
}

interface AgentPerformanceData {
  [agentName: string]: AgentPerformance;
}

interface ResourceMetrics {
  cpu: number[];
  memory: number[];
  tokens: number[];
}

interface ResourceUtilizationData {
  "7d": ResourceMetrics;
  "30d": ResourceMetrics;
  "90d": ResourceMetrics;
}

export function AnalyticsDashboard() {
  const [timeRange, setTimeRange] = useState<TimeRangeKey>("7d");

  // Get agents from the context
  const agentContext = useAgentBoardController();
  const agents = agentContext?.agents || [];

  // In a real Electron application, this data would likely be fetched from
  // the main process (if it requires system access or aggregation of data from agent processes)
  // or from a backend service.
  // You might use useEffect to fetch this data when the component mounts or timeRange changes.

  // For example:
  // const [analyticsData, setAnalyticsData] = useState<any>(null);
  // const [loading, setLoading] = useState(true);
  //
  // useEffect(() => {
  //   const fetchAnalytics = async () => {
  //     setLoading(true);
  //     try {
  //       // Example: IPC call to main process
  //       // const data = await window.electron.ipcRenderer.invoke('fetch-analytics-data', timeRange, agents.map(a => a.id));
  //       // setAnalyticsData(data);
  //
  //       // For now, we'll just simulate a delay for mock data
  //       await new Promise(resolve => setTimeout(resolve, 500));
  //       // In a real scenario, you'd update your state with fetched data here.
  //     } catch (error) {
  //       console.error("Failed to fetch analytics data:", error);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };
  //
  //   fetchAnalytics();
  // }, [timeRange, agents]);

  // Mock data for demonstration
  const taskCompletionData: TaskCompletionData = {
    "7d": [4, 6, 8, 7, 9, 10, 12],
    "30d": [
      4, 6, 8, 7, 9, 10, 12, 14, 13, 15, 17, 16, 18, 20, 19, 21, 22, 24, 23, 25, 27, 26, 28, 30, 29, 31, 32, 34, 33, 35,
    ],
    "90d": Array.from({ length: 90 }, (_, i) => Math.floor(Math.random() * 30) + 10),
  };

  // This could be dynamically generated based on the `agents` from context
  const agentPerformanceData: AgentPerformanceData = agents.reduce((acc, agent) => {
    // Mock performance for each agent from context for a more dynamic feel
    acc[agent.name] = {
      tasksCompleted: Math.floor(Math.random() * 50) + 10,
      successRate: Math.random() * 0.2 + 0.8, // 80% - 100%
      avgCompletionTime: Math.floor(Math.random() * 120) + 60, // 60 - 180
    };
    return acc;
  }, {} as AgentPerformanceData);

  // If there are no agents from context, fall back to some default mock data
  if (Object.keys(agentPerformanceData).length === 0) {
    Object.assign(agentPerformanceData, {
      TaskBot: { tasksCompleted: 45, successRate: 0.92, avgCompletionTime: 120 },
      CodeAssistant: { tasksCompleted: 38, successRate: 0.89, avgCompletionTime: 180 },
      QATester: { tasksCompleted: 52, successRate: 0.95, avgCompletionTime: 90 },
      DocWriter: { tasksCompleted: 29, successRate: 0.97, avgCompletionTime: 150 },
    });
  }

  const resourceUtilizationData: ResourceUtilizationData = {
    "7d": {
      cpu: [25, 30, 28, 35, 40, 38, 42],
      memory: [128, 156, 142, 168, 180, 175, 190],
      tokens: [5000, 7500, 6800, 8200, 9500, 9000, 10200],
    },
    "30d": {
      cpu: Array.from({ length: 30 }, (_, i) => Math.floor(Math.random() * 30) + 20),
      memory: Array.from({ length: 30 }, (_, i) => Math.floor(Math.random() * 100) + 100),
      tokens: Array.from({ length: 30 }, (_, i) => Math.floor(Math.random() * 5000) + 5000),
    },
    "90d": {
      cpu: Array.from({ length: 90 }, (_, i) => Math.floor(Math.random() * 30) + 20),
      memory: Array.from({ length: 90 }, (_, i) => Math.floor(Math.random() * 100) + 100),
      tokens: Array.from({ length: 90 }, (_, i) => Math.floor(Math.random() * 5000) + 5000),
    },
  };

  // Render a simple bar chart
  const renderBarChart = (data: number[] | undefined, height = 150, color = "#2563eb") => {
    if (!data || data.length === 0) {
      return <div className="text-center text-muted-foreground p-4">No data available for this period.</div>;
    }
    const max = Math.max(...data, 1); // Ensure max is at least 1 to avoid division by zero

    return (
      <ChartContainer>
        {data.map((value, index) => (
          <Bar
            key={index}
            height={`${Math.max(0, (value / max) * (height - 10))}px`}
            color={color}
            title={`${value}`}
          />
        ))}
      </ChartContainer>
    );
  };

  return (
    <DashboardContainer>
      <DashboardHeader>
        <h2>Analytics Dashboard</h2>
        <TimeRangeSelect value={timeRange} onChange={(e) => setTimeRange(e.target.value as TimeRangeKey)}>
          <option value="7d">Last 7 days</option>
          <option value="30d">Last 30 days</option>
          <option value="90d">Last 90 days</option>
        </TimeRangeSelect>
      </DashboardHeader>

      <Tabs defaultValue="task-completion" className="w-full">
        <TabsList className="grid grid-cols-3 w-full">
          <TabsTrigger value="task-completion">Task Completion</TabsTrigger>
          <TabsTrigger value="agent-performance">Agent Performance</TabsTrigger>
          <TabsTrigger value="resource-utilization">Resource Utilization</TabsTrigger>
        </TabsList>

        <TabsContent value="task-completion" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Task Completion Velocity</CardTitle>
              <CardDescription>Number of tasks completed over time</CardDescription>
            </CardHeader>
            <CardContent>
              {renderBarChart(taskCompletionData[timeRange], 150, "#4CAF50")}
              <AxisLabels>
                {timeRange === "7d" && ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map(day => <AxisLabel key={day}>{day}</AxisLabel>)}
                {timeRange === "30d" && ["W1", "W2", "W3", "W4", "W5+"].map(week => <AxisLabel key={week}>{week}</AxisLabel>)}
                {timeRange === "90d" && ["M1", "M2", "M3"].map(month => <AxisLabel key={month}>{month}</AxisLabel>)}
              </AxisLabels>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="agent-performance" className="mt-4">
          {Object.keys(agentPerformanceData).length === 0 ? (
            <Card><CardContent className="p-4 text-center text-muted-foreground">No agent performance data available.</CardContent></Card>
          ) : (
            <AgentGrid>
              {Object.entries(agentPerformanceData).map(([agentName, data]) => (
                <Card key={agentName}>
                  <CardHeader>
                    <CardTitle>{agentName}</CardTitle>
                    <CardDescription>Performance metrics</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <MetricsList>
                      <MetricItem>
                        <MetricLabel>Tasks Completed</MetricLabel>
                        <MetricValue>{data.tasksCompleted}</MetricValue>
                      </MetricItem>
                      <MetricItem>
                        <MetricLabel>Success Rate</MetricLabel>
                        <MetricValue>{(data.successRate * 100).toFixed(1)}%</MetricValue>
                      </MetricItem>
                      <MetricItem>
                        <MetricLabel>Avg. Completion Time</MetricLabel>
                        <MetricValue>{data.avgCompletionTime} min</MetricValue>
                      </MetricItem>
                    </MetricsList>
                  </CardContent>
                </Card>
              ))}
            </AgentGrid>
          )}
        </TabsContent>

        <TabsContent value="resource-utilization" className="mt-4">
          <ResourceGrid>
            <Card>
              <CardHeader>
                <CardTitle>CPU Usage</CardTitle>
                <CardDescription>Average CPU usage percentage</CardDescription>
              </CardHeader>
              <CardContent>
                {renderBarChart(resourceUtilizationData[timeRange].cpu, 150, "#F44336")}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Memory Usage</CardTitle>
                <CardDescription>Memory consumption in MB</CardDescription>
              </CardHeader>
              <CardContent>
                {renderBarChart(resourceUtilizationData[timeRange].memory, 150, "#2196F3")}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Token Usage</CardTitle>
                <CardDescription>API tokens consumed</CardDescription>
              </CardHeader>
              <CardContent>
                {renderBarChart(resourceUtilizationData[timeRange].tokens, 150, "#9C27B0")}
              </CardContent>
            </Card>
          </ResourceGrid>
        </TabsContent>
      </Tabs>
    </DashboardContainer>
  );
}

// Styled components
const DashboardContainer = styled.div`
  padding: 16px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const DashboardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    font-size: 20px;
    font-weight: 600;
  }
`;

const TimeRangeSelect = styled.select`
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.background};
  min-width: 150px;
`;

const ChartContainer = styled.div`
  display: flex;
  align-items: flex-end;
  height: 150px;
  gap: 2px;
  padding: 8px 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.foreground};
`;

const Bar = styled.div<{ height: string; color: string }>`
  flex: 1;
  height: ${props => props.height};
  background-color: ${props => props.color};
  border-radius: 2px 2px 0 0;
  min-height: 1px;
  transition: height 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
`;

const AxisLabels = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
`;

const AxisLabel = styled.div`
  flex: 1;
  text-align: center;
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const AgentGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
`;

const ResourceGrid = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const MetricsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const MetricItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const MetricLabel = styled.span`
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const MetricValue = styled.span`
  font-weight: 500;
`;

export default AnalyticsDashboard;
