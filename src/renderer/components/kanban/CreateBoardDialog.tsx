// src/renderer/components/kanban/CreateBoardDialog.tsx
import React, { useState } from 'react';
import styled from 'styled-components';
import { X } from 'lucide-react';
import { useBoard } from '../../contexts/board-context';

interface CreateBoardDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const CreateBoardDialog: React.FC<CreateBoardDialogProps> = ({
  isOpen,
  onClose
}) => {
  const [boardName, setBoardName] = useState('');
  const [boardDescription, setBoardDescription] = useState('');
  const { addBoard } = useBoard();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!boardName.trim()) return;
    
    // Create a new board with default columns and swimlanes
    const newBoard = {
      name: boardName.trim(),
      description: boardDescription.trim(),
      columns: [
        { id: `column-${Date.now()}-1`, name: 'To Do', order: 0 },
        { id: `column-${Date.now()}-2`, name: 'In Progress', order: 1 },
        { id: `column-${Date.now()}-3`, name: 'Done', order: 2 }
      ],
      swimlanes: [
        { id: `swimlane-${Date.now()}-1`, name: 'Default', order: 0, expanded: true }
      ],
      cards: [],
      cardTypes: [
        { id: 'feature', name: 'Feature', color: '#4caf50' },
        { id: 'bug', name: 'Bug', color: '#f44336' },
        { id: 'task', name: 'Task', color: '#2196f3' },
        { id: 'improvement', name: 'Improvement', color: '#ff9800' }
      ],
      agents: []
    };
    
    addBoard(newBoard);
    
    // Reset form and close dialog
    setBoardName('');
    setBoardDescription('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <DialogOverlay onClick={onClose}>
      <DialogContent onClick={e => e.stopPropagation()}>
        <DialogHeader>
          <DialogTitle>Create New Board</DialogTitle>
          <CloseButton onClick={onClose}>
            <X size={18} />
          </CloseButton>
        </DialogHeader>

        <DialogForm onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="board-name">Board Name</Label>
            <Input
              id="board-name"
              value={boardName}
              onChange={e => setBoardName(e.target.value)}
              placeholder="Enter board name"
              autoFocus
              required
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="board-description">Description (optional)</Label>
            <TextArea
              id="board-description"
              value={boardDescription}
              onChange={e => setBoardDescription(e.target.value)}
              placeholder="Enter board description"
              rows={3}
            />
          </FormGroup>

          <FormGroup>
            <Label>Template</Label>
            <TemplateGrid>
              <TemplateOption selected>
                <TemplateTitle>Basic Kanban</TemplateTitle>
                <TemplateDescription>To Do, In Progress, Done</TemplateDescription>
              </TemplateOption>
              <TemplateOption>
                <TemplateTitle>Scrum Board</TemplateTitle>
                <TemplateDescription>Backlog, Sprint, Review, Done</TemplateDescription>
              </TemplateOption>
              <TemplateOption>
                <TemplateTitle>Project Management</TemplateTitle>
                <TemplateDescription>Planning, Development, Testing, Deployment</TemplateDescription>
              </TemplateOption>
              <TemplateOption>
                <TemplateTitle>Empty Board</TemplateTitle>
                <TemplateDescription>Start from scratch</TemplateDescription>
              </TemplateOption>
            </TemplateGrid>
          </FormGroup>

          <DialogFooter>
            <CancelButton type="button" onClick={onClose}>
              Cancel
            </CancelButton>
            <SubmitButton type="submit" disabled={!boardName.trim()}>
              Create Board
            </SubmitButton>
          </DialogFooter>
        </DialogForm>
      </DialogContent>
    </DialogOverlay>
  );
};

// Styled components
const DialogOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const DialogContent = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: fadeIn 0.3s ease-out;

  @keyframes fadeIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
  }
`;

const DialogHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const DialogTitle = styled.h3`
  margin: 0;
  font-size: 18px;
  font-weight: 600;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background-color: ${({ theme }) => theme.colors.foreground};
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const DialogForm = styled.form`
  padding: 16px;
`;

const FormGroup = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const TemplateGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
`;

interface TemplateOptionProps {
  selected?: boolean;
}

const TemplateOption = styled.div<TemplateOptionProps>`
  padding: 12px;
  border-radius: 4px;
  border: 1px solid ${({ selected, theme }) => 
    selected ? theme.colors.primary : theme.colors.border};
  background-color: ${({ selected, theme }) => 
    selected ? `${theme.colors.primary}10` : theme.colors.foreground};
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary};
    background-color: ${({ theme }) => `${theme.colors.primary}10`};
  }
`;

const TemplateTitle = styled.h4`
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
`;

const TemplateDescription = styled.p`
  margin: 0;
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const DialogFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
`;

const Button = styled.button`
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
`;

const CancelButton = styled(Button)`
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.text.primary};

  &:hover {
    background-color: ${({ theme }) => theme.colors.foreground};
  }
`;

const SubmitButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.primary};
  border: 1px solid ${({ theme }) => theme.colors.primary};
  color: white;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.border};
    border-color: ${({ theme }) => theme.colors.border};
    color: ${({ theme }) => theme.colors.text.secondary};
    cursor: not-allowed;
  }
`;

export default CreateBoardDialog;
