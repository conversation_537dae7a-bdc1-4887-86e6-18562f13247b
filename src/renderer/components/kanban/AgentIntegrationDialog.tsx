// src/renderer/components/kanban/AgentIntegrationDialog.tsx
import React, { useState } from 'react';
import styled from 'styled-components';
import { <PERSON>, <PERSON><PERSON>, Cpu, GitBranch, Settings } from 'lucide-react';
import { useAgentBoardController } from '../../contexts/agent-board-controller';
import { useToast } from '../../hooks/use-toast';

// Define a type for the settings being managed
export interface AgentSettings {
  agentType: string;
  autoAssign: boolean;
  resourceLimit: number;
  enableAIAgents: boolean;
  trackTokens: boolean;
  trackCpu: boolean;
  trackMemory: boolean;
  trackApi: boolean;
  autoDetectDependencies: boolean;
  dependencyVisualization: string;
  blockProgressOnUnresolvedDependencies: boolean;
  // Advanced settings
  agentTimeout?: number;
  retryAttempts?: number;
  aiModel?: string;
  debugMode?: boolean;
  logAgentActions?: boolean;
}

interface AgentIntegrationDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const AgentIntegrationDialog: React.FC<AgentIntegrationDialogProps> = ({
  isOpen,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState<'agents' | 'resources' | 'dependencies'>('agents');
  const [agentType, setAgentType] = useState<string>('task-manager');
  const [autoAssign, setAutoAssign] = useState<boolean>(true);
  const [resourceLimit, setResourceLimit] = useState<number>(50);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState<boolean>(false);
  const [enableAgents, setEnableAgents] = useState<boolean>(true);

  // Additional state variables from the user's version
  const [trackTokens, setTrackTokens] = useState<boolean>(true);
  const [trackCpu, setTrackCpu] = useState<boolean>(true);
  const [trackMemory, setTrackMemory] = useState<boolean>(true);
  const [trackApi, setTrackApi] = useState<boolean>(false);
  const [autoDetectDependencies, setAutoDetectDependencies] = useState<boolean>(true);
  const [dependencyVisualization, setDependencyVisualization] = useState<string>('graph');
  const [blockProgress, setBlockProgress] = useState<boolean>(true);
  const [agentTimeout, setAgentTimeout] = useState<number>(30000);
  const [retryAttempts, setRetryAttempts] = useState<number>(3);
  const [aiModel, setAiModel] = useState<string>('gpt-4');
  const [debugMode, setDebugMode] = useState<boolean>(false);
  const [logAgentActions, setLogAgentActions] = useState<boolean>(true);

  const { agents, isAgentSystemActive, toggleAgentSystem } = useAgentBoardController();
  const { toast } = useToast();

  const handleSave = () => {
    // Toggle agent system if needed
    if (enableAgents !== isAgentSystemActive) {
      toggleAgentSystem();
    }

    // Gather all settings
    const currentSettings: AgentSettings = {
      agentType,
      autoAssign,
      resourceLimit,
      enableAIAgents: enableAgents,
      trackTokens,
      trackCpu,
      trackMemory,
      trackApi,
      autoDetectDependencies,
      dependencyVisualization,
      blockProgressOnUnresolvedDependencies: blockProgress,
      agentTimeout,
      retryAttempts,
      aiModel,
      debugMode,
      logAgentActions
    };

    // In an Electron app, persist these settings:
    // Option 1: Send to main process via IPC to save
    // window.electron.ipcRenderer.send('save-agent-settings', currentSettings);

    toast({
      title: "Agent settings saved",
      description: "Your AI agent integration settings have been updated."
    });

    onClose();
  };

  if (!isOpen) return null;

  return (
    <DialogOverlay onClick={onClose}>
      <DialogContent onClick={e => e.stopPropagation()}>
        <DialogHeader>
          <DialogTitle>AI Agent Integration</DialogTitle>
          <CloseButton onClick={onClose}>
            <X size={18} />
          </CloseButton>
        </DialogHeader>

        <TabsContainer>
          <TabsList>
            <TabButton
              isActive={activeTab === 'agents'}
              onClick={() => setActiveTab('agents')}
            >
              <Bot size={16} />
              <span>Agents</span>
            </TabButton>
            <TabButton
              isActive={activeTab === 'resources'}
              onClick={() => setActiveTab('resources')}
            >
              <Cpu size={16} />
              <span>Resources</span>
            </TabButton>
            <TabButton
              isActive={activeTab === 'dependencies'}
              onClick={() => setActiveTab('dependencies')}
            >
              <GitBranch size={16} />
              <span>Dependencies</span>
            </TabButton>
          </TabsList>

          <TabContent>
            {activeTab === 'agents' && (
              <div>
                <FormGroup>
                  <FormGroupHeader>
                    <Label>Enable AI Agents</Label>
                    <Description>Allow AI agents to monitor and update your board</Description>
                  </FormGroupHeader>
                  <Switch
                    checked={enableAgents}
                    onChange={() => setEnableAgents(!enableAgents)}
                  />
                </FormGroup>

                <Separator />

                <FormGroup>
                  <Label>Default Agent Type</Label>
                  <Select
                    value={agentType}
                    onChange={e => setAgentType(e.target.value)}
                  >
                    <option value="task-manager">Task Manager</option>
                    <option value="resource-optimizer">Resource Optimizer</option>
                    <option value="dependency-resolver">Dependency Resolver</option>
                    <option value="progress-tracker">Progress Tracker</option>
                  </Select>
                </FormGroup>

                <FormGroup>
                  <FormGroupHeader>
                    <Label>Auto-assign Agents to New Cards</Label>
                    <Description>Automatically assign agents to new cards based on card type</Description>
                  </FormGroupHeader>
                  <Switch
                    checked={autoAssign}
                    onChange={() => setAutoAssign(!autoAssign)}
                  />
                </FormGroup>

                <Separator />

                <FormGroup>
                  <ExpandableHeader onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}>
                    <Label>Advanced Settings</Label>
                    <ExpandButton>{showAdvancedSettings ? '−' : '+'}</ExpandButton>
                  </ExpandableHeader>

                  {showAdvancedSettings && (
                    <AdvancedSettings>
                      <FormGroup>
                        <Label>AI Model</Label>
                        <Select defaultValue="gpt-4">
                          <option value="gpt-4">GPT-4</option>
                          <option value="gpt-3.5">GPT-3.5</option>
                          <option value="claude-3">Claude 3</option>
                          <option value="custom">Custom Model</option>
                        </Select>
                      </FormGroup>

                      <FormGroup>
                        <FormGroupHeader>
                          <Label>Enable Debug Mode</Label>
                          <Description>Show detailed agent activity logs</Description>
                        </FormGroupHeader>
                        <Switch defaultChecked={false} />
                      </FormGroup>
                    </AdvancedSettings>
                  )}
                </FormGroup>
              </div>
            )}

            {activeTab === 'resources' && (
              <div>
                <FormGroup>
                  <Label>Resource Limit ({resourceLimit}%)</Label>
                  <RangeSlider
                    type="range"
                    min="10"
                    max="100"
                    value={resourceLimit}
                    onChange={e => setResourceLimit(Math.max(10, Math.min(100, parseInt(e.target.value))))}
                  />
                  <Description>Maximum resources agents can use</Description>
                </FormGroup>

                <Separator />

                <FormGroup>
                  <Label>Resource Metrics to Track</Label>
                  <CheckboxGrid>
                    <CheckboxItem>
                      <Checkbox
                        id="track-tokens"
                        checked={trackTokens}
                        onChange={() => setTrackTokens(!trackTokens)}
                      />
                      <CheckboxLabel htmlFor="track-tokens">Token Usage</CheckboxLabel>
                    </CheckboxItem>
                    <CheckboxItem>
                      <Checkbox
                        id="track-cpu"
                        checked={trackCpu}
                        onChange={() => setTrackCpu(!trackCpu)}
                      />
                      <CheckboxLabel htmlFor="track-cpu">CPU Time</CheckboxLabel>
                    </CheckboxItem>
                    <CheckboxItem>
                      <Checkbox
                        id="track-memory"
                        checked={trackMemory}
                        onChange={() => setTrackMemory(!trackMemory)}
                      />
                      <CheckboxLabel htmlFor="track-memory">Memory Usage</CheckboxLabel>
                    </CheckboxItem>
                    <CheckboxItem>
                      <Checkbox
                        id="track-api"
                        checked={trackApi}
                        onChange={() => setTrackApi(!trackApi)}
                      />
                      <CheckboxLabel htmlFor="track-api">API Calls</CheckboxLabel>
                    </CheckboxItem>
                  </CheckboxGrid>
                </FormGroup>
              </div>
            )}

            {activeTab === 'dependencies' && (
              <div>
                <FormGroup>
                  <FormGroupHeader>
                    <Label>Auto-detect Dependencies</Label>
                    <Description>Let AI suggest task dependencies</Description>
                  </FormGroupHeader>
                  <Switch
                    checked={autoDetectDependencies}
                    onChange={() => setAutoDetectDependencies(!autoDetectDependencies)}
                  />
                </FormGroup>

                <Separator />

                <FormGroup>
                  <Label>Dependency Visualization</Label>
                  <Select
                    value={dependencyVisualization}
                    onChange={e => setDependencyVisualization(e.target.value)}
                  >
                    <option value="graph">Network Graph</option>
                    <option value="tree">Tree View</option>
                    <option value="matrix">Dependency Matrix</option>
                  </Select>
                </FormGroup>

                <FormGroup>
                  <FormGroupHeader>
                    <Label>Block progress on unresolved dependencies</Label>
                    <Description>Prevent cards from moving to next column if dependencies aren't resolved</Description>
                  </FormGroupHeader>
                  <Switch
                    checked={blockProgress}
                    onChange={() => setBlockProgress(!blockProgress)}
                  />
                </FormGroup>
              </div>
            )}
          </TabContent>
        </TabsContainer>

        {showAdvancedSettings && (
          <AdvancedSettingsContainer>
            <AdvancedSettings>
              <FormGroup>
                <Label>Agent Timeout (ms)</Label>
                <Input
                  type="number"
                  value={agentTimeout}
                  onChange={e => setAgentTimeout(Number(e.target.value))}
                  min={1000}
                  max={60000}
                />
              </FormGroup>

              <FormGroup>
                <Label>Retry Attempts</Label>
                <Input
                  type="number"
                  value={retryAttempts}
                  onChange={e => setRetryAttempts(Number(e.target.value))}
                  min={0}
                  max={10}
                />
              </FormGroup>

              <FormGroup>
                <Label>AI Model</Label>
                <Select
                  value={aiModel}
                  onChange={e => setAiModel(e.target.value)}
                >
                  <option value="gpt-4">GPT-4</option>
                  <option value="gpt-3.5">GPT-3.5</option>
                  <option value="claude-3">Claude 3</option>
                  <option value="custom">Custom Model</option>
                </Select>
              </FormGroup>

              <FormGroup>
                <FormGroupHeader>
                  <Label>Enable Debug Mode</Label>
                  <Description>Show detailed agent activity logs</Description>
                </FormGroupHeader>
                <Switch
                  checked={debugMode}
                  onChange={() => setDebugMode(!debugMode)}
                />
              </FormGroup>

              <FormGroup>
                <FormGroupHeader>
                  <Label>Log All Agent Actions</Label>
                  <Description>Keep a record of all agent activities</Description>
                </FormGroupHeader>
                <Switch
                  checked={logAgentActions}
                  onChange={() => setLogAgentActions(!logAgentActions)}
                />
              </FormGroup>
            </AdvancedSettings>
          </AdvancedSettingsContainer>
        )}

        <DialogFooter>
          <AdvancedButton onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}>
            <Settings size={16} />
            {showAdvancedSettings ? "Hide" : "Show"} Advanced Settings
          </AdvancedButton>
          <ButtonGroup>
            <CancelButton onClick={onClose}>Cancel</CancelButton>
            <SaveButton onClick={handleSave}>Save Changes</SaveButton>
          </ButtonGroup>
        </DialogFooter>
      </DialogContent>
    </DialogOverlay>
  );
};

// Styled components
const DialogOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const DialogContent = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 8px;
  width: 600px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: fadeIn 0.3s ease-out;

  @keyframes fadeIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
  }
`;

const DialogHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const DialogTitle = styled.h3`
  margin: 0;
  font-size: 18px;
  font-weight: 600;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background-color: ${({ theme }) => theme.colors.foreground};
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const TabsContainer = styled.div`
  padding: 16px;
`;

const TabsList = styled.div`
  display: flex;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  margin-bottom: 16px;
`;

interface TabButtonProps {
  isActive: boolean;
}

const TabButton = styled.button<TabButtonProps>`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: none;
  border: none;
  border-bottom: 2px solid ${({ isActive, theme }) =>
    isActive ? theme.colors.primary : 'transparent'};
  color: ${({ isActive, theme }) =>
    isActive ? theme.colors.primary : theme.colors.text.secondary};
  font-weight: ${({ isActive }) => isActive ? '600' : '400'};
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const TabContent = styled.div`
  margin-top: 16px;
`;

const FormGroup = styled.div`
  margin-bottom: 16px;
`;

const FormGroupHeader = styled.div`
  margin-bottom: 8px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
`;

const Description = styled.p`
  margin: 4px 0 0;
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const Select = styled.select`
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const Switch = styled.input.attrs({ type: 'checkbox' })`
  position: relative;
  width: 40px;
  height: 20px;
  appearance: none;
  background-color: ${({ theme }) => theme.colors.border};
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s;

  &:checked {
    background-color: ${({ theme }) => theme.colors.primary};
  }

  &:before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: white;
    transition: transform 0.3s;
  }

  &:checked:before {
    transform: translateX(20px);
  }
`;

const Separator = styled.div`
  height: 1px;
  background-color: ${({ theme }) => theme.colors.border};
  margin: 16px 0;
`;

const ExpandableHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
`;

const ExpandButton = styled.span`
  font-size: 18px;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const AdvancedSettings = styled.div`
  margin-top: 12px;
  padding: 12px;
  background-color: ${({ theme }) => theme.colors.foreground};
  border-radius: 4px;
`;

const RangeSlider = styled.input`
  width: 100%;
  margin: 8px 0;
`;

const CheckboxGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-top: 8px;
`;

const CheckboxItem = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const Checkbox = styled.input.attrs({ type: 'checkbox' })`
  width: 16px;
  height: 16px;
`;

const CheckboxLabel = styled.label`
  font-size: 14px;
`;

const DialogFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
`;

const Button = styled.button`
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
`;

const CancelButton = styled(Button)`
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.text.primary};

  &:hover {
    background-color: ${({ theme }) => theme.colors.foreground};
  }
`;

const SaveButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.primary};
  border: 1px solid ${({ theme }) => theme.colors.primary};
  color: white;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }
`;

const AdvancedSettingsContainer = styled.div`
  padding: 0 16px;
  margin-bottom: 16px;
`;

const Input = styled.input`
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const AdvancedButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.text.primary};

  &:hover {
    background-color: ${({ theme }) => theme.colors.foreground};
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 8px;
`;

export default AgentIntegrationDialog;
