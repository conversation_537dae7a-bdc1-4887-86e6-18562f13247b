// src/renderer/components/kanban/KanbanBoard.tsx
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useBoard, Column, Swimlane, Card } from '../../contexts/board-context';
import { useAgentBoardController } from '../../contexts/agent-board-controller';
import KanbanColumn from './KanbanColumn';
import KanbanSwimlane from './KanbanSwimlane';
import { cn } from '../../lib/utils';
import {
  Plus,
  Settings,
  Filter,
  SortAsc,
  MoreHorizontal,
  Columns,
  LayoutGrid,
  Edit,
  ChevronDown,
  Bot
} from 'lucide-react';
import CreateColumnDialog from './CreateColumnDialog';
import CreateSwimlaneDialog from './CreateSwimlaneDialog';
import CreateCardDialog from './CreateCardDialog';
import BoardSettingsDialog from './BoardSettingsDialog';
import AgentIntegrationDialog from './AgentIntegrationDialog';
import {
  DndContext,
  DragOverlay,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
  type DragStartEvent,
  type DragOverEvent,
  type DragEndEvent,
} from '@dnd-kit/core';

interface KanbanBoardProps {
  className?: string;
}

const KanbanBoard: React.FC<KanbanBoardProps> = ({ className }) => {
  const {
    activeBoard,
    addColumn,
    updateColumn,
    deleteColumn,
    addSwimlane,
    updateSwimlane,
    deleteSwimlane,
    toggleSwimlaneExpansion,
    addCardToColumn,
    updateCardInColumn,
    deleteCardFromColumn,
    moveCard
  } = useBoard();
  const { agents } = useAgentBoardController();

  // State for dialogs
  const [isCreateColumnDialogOpen, setIsCreateColumnDialogOpen] = useState(false);
  const [isCreateSwimlaneDialogOpen, setIsCreateSwimlaneDialogOpen] = useState(false);
  const [isBoardSettingsDialogOpen, setIsBoardSettingsDialogOpen] = useState(false);
  const [isAgentSettingsDialogOpen, setIsAgentSettingsDialogOpen] = useState(false);
  const [expandedSwimlanes, setExpandedSwimlanes] = useState<Record<string, boolean>>({});

  // State for drag and drop
  const [isDragging, setIsDragging] = useState(false);
  const [activeCardId, setActiveCardId] = useState<string | null>(null);
  const [activeDndColumnId, setActiveDndColumnId] = useState<string | null>(null);
  const [activeDndSwimlaneId, setActiveDndSwimlaneId] = useState<string | null>(null);

  // State for view mode
  const [viewMode, setViewMode] = useState<'swimlanes' | 'columns'>('swimlanes');
  const [activeSwimlaneForColumnView, setActiveSwimlaneForColumnView] = useState<string | null>(null);

  // State for card creation
  const [targetColumnId, setTargetColumnId] = useState<string | null>(null);
  const [targetSwimlaneId, setTargetSwimlaneId] = useState<string | null>(null);
  const [isCreateCardOpen, setIsCreateCardOpen] = useState(false);

  const boardRef = useRef<HTMLDivElement>(null);
  const dragOverlayRef = useRef(null);

  // Initialize expanded swimlanes from board state
  useEffect(() => {
    if (activeBoard?.swimlanes) {
      const initialExpandedState: Record<string, boolean> = {};
      activeBoard.swimlanes.forEach(swimlane => {
        initialExpandedState[swimlane.id] = swimlane.expanded;
      });
      setExpandedSwimlanes(initialExpandedState);
    }
  }, [activeBoard?.swimlanes]);

  // Set default active swimlane for column view when swimlanes change
  useEffect(() => {
    if (activeBoard?.swimlanes && activeBoard.swimlanes.length > 0 && !activeSwimlaneForColumnView) {
      setActiveSwimlaneForColumnView(activeBoard.swimlanes[0]?.id || null);
    }
  }, [activeBoard?.swimlanes, activeSwimlaneForColumnView]);

  // Handle swimlane toggle
  const handleSwimlaneToggle = (swimlaneId: string) => {
    toggleSwimlaneExpansion(activeBoard?.id || '', swimlaneId);
    setExpandedSwimlanes(prev => ({
      ...prev,
      [swimlaneId]: !prev[swimlaneId]
    }));
  };

  // Helper function to get cards for a column and swimlane
  const getCardsForColumnAndSwimlane = (columnId: string, swimlaneId: string): Card[] => {
    if (!activeBoard) return [];

    return activeBoard.cards.filter(
      card => card.columnId === columnId && card.swimlaneId === swimlaneId
    );
  };

  // Sort columns by order
  const sortedColumns = useMemo(() => {
    return activeBoard?.columns.slice().sort((a, b) => a.order - b.order) || [];
  }, [activeBoard?.columns]);

  // Sort swimlanes by order
  const sortedSwimlanes = useMemo(() => {
    return activeBoard?.swimlanes.slice().sort((a, b) => a.order - b.order) || [];
  }, [activeBoard?.swimlanes]);



  // Handle column creation
  const handleCreateColumn = async (name: string, color?: string) => {
    if (!activeBoard) return;

    const order = activeBoard.columns.length;
    await addColumn(activeBoard.id, { name, order, color });
    setIsCreateColumnDialogOpen(false);
  };

  // Handle column update
  const handleEditColumn = (columnId: string, name: string, color?: string) => {
    if (!activeBoard) return;

    const column = activeBoard.columns.find(col => col.id === columnId);
    if (column) {
      updateColumn(activeBoard.id, column.id, { ...column, name, color });
    }
  };

  // Handle column deletion
  const handleDeleteColumn = (columnId: string) => {
    if (!activeBoard) return;

    deleteColumn(activeBoard.id, columnId);
  };

  // Handle swimlane creation
  const handleCreateSwimlane = async (name: string, color?: string) => {
    if (!activeBoard) return;

    const order = activeBoard.swimlanes.length;
    await addSwimlane(activeBoard.id, { name, order, expanded: true, color });
    setIsCreateSwimlaneDialogOpen(false);
  };

  // Handle swimlane update
  const handleEditSwimlane = (swimlaneId: string, name: string, color?: string) => {
    if (!activeBoard) return;

    const swimlane = activeBoard.swimlanes.find(s => s.id === swimlaneId);
    if (swimlane) {
      updateSwimlane(activeBoard.id, swimlane.id, { ...swimlane, name, color });
    }
  };

  // Handle swimlane deletion
  const handleDeleteSwimlane = (swimlaneId: string) => {
    if (!activeBoard) return;

    deleteSwimlane(activeBoard.id, swimlaneId);
  };

  // Set up DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor)
  );

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    setIsDragging(true);

    const { active } = event;
    const { id } = active;

    if (typeof id === 'string') {
      setActiveCardId(id);

      // Get column and swimlane IDs from the data
      const data = active.data.current;
      if (data) {
        setActiveDndColumnId(data.columnId);
        setActiveDndSwimlaneId(data.swimlaneId);
      }
    }
  };

  // Handle drag over
  const handleDragOver = (event: DragOverEvent) => {
    // This would be used for more complex drag operations
    // like sorting within a column
  };

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    setIsDragging(false);
    setActiveCardId(null);

    const { active, over } = event;

    if (!over) return;

    const activeId = active.id;
    const overId = over.id;

    if (activeId === overId) return;

    const activeData = active.data.current;
    const overData = over.data.current;

    if (!activeData || !overData) return;

    const sourceColumnId = activeData.columnId;
    const sourceSwimlaneId = activeData.swimlaneId;

    // Extract destination column and swimlane IDs
    let destinationColumnId = overData.columnId;
    let destinationSwimlaneId = overData.swimlaneId;

    // If we dropped on a card, use its column and swimlane
    if (overData.type === 'card') {
      destinationColumnId = overData.columnId;
      destinationSwimlaneId = overData.swimlaneId;
    }

    // If no change in position, do nothing
    if (sourceColumnId === destinationColumnId && sourceSwimlaneId === destinationSwimlaneId) {
      return;
    }

    // Move the card
    if (activeBoard && typeof activeId === 'string') {
      moveCard(
        activeBoard.id,
        activeId,
        destinationColumnId,
        destinationSwimlaneId
      );
    }
  };

  // Handle drag cancel
  const handleDragCancel = () => {
    setIsDragging(false);
    setActiveCardId(null);
    setActiveDndColumnId(null);
    setActiveDndSwimlaneId(null);
  };

  // Open card creation dialog
  const openCreateCardDialog = (columnId: string, swimlaneId: string) => {
    setTargetColumnId(columnId);
    setTargetSwimlaneId(swimlaneId);
    setIsCreateCardOpen(true);
  };

  // Handle card creation
  const handleAddCard = (cardData: any) => {
    if (!activeBoard || !targetColumnId || !targetSwimlaneId) return;

    addCardToColumn(
      activeBoard.id,
      targetColumnId,
      { ...cardData, swimlaneId: targetSwimlaneId }
    );

    setIsCreateCardOpen(false);
    setTargetColumnId(null);
    setTargetSwimlaneId(null);
  };

  // Handle card update
  const handleCardUpdate = (card: Card) => {
    if (!activeBoard) return;

    updateCardInColumn(activeBoard.id, card.id, card);
  };

  // Handle card deletion
  const handleDeleteCard = (columnId: string, cardId: string) => {
    if (!activeBoard) return;

    deleteCardFromColumn(activeBoard.id, cardId);
  };

  if (!activeBoard) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
        <h3 className="mb-2">No active board</h3>
        <p className="m-0">Create a board to get started</p>
      </div>
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
      onDragCancel={handleDragCancel}
    >
      <div className={cn("flex flex-col h-full w-full bg-background text-foreground overflow-hidden", className)} ref={boardRef}>
        <div className="flex justify-between items-center p-3 border-b bg-card">
          <div className="flex items-center gap-4">
            <h2 className="text-lg font-semibold m-0">{activeBoard?.name || 'Untitled Board'}</h2>

            <div className="flex border rounded-md overflow-hidden">
              <button
                className={cn(
                  "flex items-center gap-2 px-2.5 py-1.5 text-xs border-0",
                  viewMode === 'swimlanes'
                    ? "bg-secondary text-secondary-foreground"
                    : "bg-transparent text-muted-foreground hover:bg-muted"
                )}
                onClick={() => setViewMode('swimlanes')}
              >
                <LayoutGrid size={14} />
                <span>Swimlanes</span>
              </button>
              <button
                className={cn(
                  "flex items-center gap-2 px-2.5 py-1.5 text-xs border-0",
                  viewMode === 'columns'
                    ? "bg-secondary text-secondary-foreground"
                    : "bg-transparent text-muted-foreground hover:bg-muted"
                )}
                onClick={() => setViewMode('columns')}
              >
                <Columns size={14} />
                <span>Columns</span>
              </button>
            </div>

            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>Key:</span>
              <span className="flex items-center gap-1">
                <span className="inline-block w-2 h-2 rounded-sm bg-emerald-500"></span>
                Low
              </span>
              <span className="flex items-center gap-1">
                <span className="inline-block w-2 h-2 rounded-sm bg-amber-500"></span>
                Medium
              </span>
              <span className="flex items-center gap-1">
                <span className="inline-block w-2 h-2 rounded-sm bg-destructive"></span>
                High
              </span>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <button
              className="flex items-center gap-1.5 px-2.5 py-1.5 rounded-md border text-xs hover:bg-muted transition-colors"
              onClick={() => setIsBoardSettingsDialogOpen(true)}
            >
              <Edit size={14} />
              <span>Edit</span>
            </button>

            <button
              className="flex items-center gap-1.5 px-2.5 py-1.5 rounded-md border border-primary/40 text-primary text-xs hover:bg-primary/10 transition-colors"
              onClick={() => setIsAgentSettingsDialogOpen(true)}
            >
              <Bot size={14} />
              <span>AI Agent Settings</span>
            </button>

            <button
              className="flex items-center gap-1.5 px-2.5 py-1.5 rounded-md border text-xs hover:bg-muted transition-colors"
              onClick={() => setIsBoardSettingsDialogOpen(true)}
            >
              <span>Board Settings</span>
            </button>

            <button
              className="flex items-center gap-1.5 px-2.5 py-1.5 rounded-md border text-xs hover:bg-muted transition-colors"
              onClick={() => setIsCreateColumnDialogOpen(true)}
            >
              <span>Add Column</span>
            </button>

            <button
              className="flex items-center gap-1.5 px-2.5 py-1.5 rounded-md border text-xs hover:bg-muted transition-colors"
              onClick={() => setIsCreateSwimlaneDialogOpen(true)}
            >
              <span>Add Swimlane</span>
            </button>
          </div>
        </div>

        <div className="flex justify-between items-center px-4 py-2 border-b">
          <div className="flex gap-2">
            <button className="flex items-center gap-1.5 px-3 py-1.5 rounded-md border text-xs bg-card hover:bg-muted transition-colors">
              <Filter size={14} />
              <span>Filter</span>
            </button>

            <button className="flex items-center gap-1.5 px-3 py-1.5 rounded-md border text-xs bg-card hover:bg-muted transition-colors">
              <SortAsc size={14} />
              <span>Sort</span>
            </button>
          </div>

          <div className="flex gap-2">
            <button className="flex items-center gap-1.5 px-3 py-1.5 rounded-md border-0 text-xs bg-primary text-primary-foreground hover:bg-primary/90 transition-colors">
              <Bot size={14} />
              <span>Activate Agents</span>
            </button>
          </div>
        </div>

        <div className="flex flex-col flex-1 overflow-hidden">
          {viewMode === 'swimlanes' ? (
            <>
              <div className="flex px-4 border-b">
                <div className="w-[200px] min-w-[200px] border-r"></div>
                {sortedColumns.map(column => (
                  <div key={column.id} className="flex-1 min-w-[250px] py-3 px-4 font-semibold text-center border-r">
                    {column.name}
                  </div>
                ))}
                <button
                  className="flex items-center justify-center w-10 min-w-10 text-muted-foreground hover:text-foreground hover:bg-card"
                  onClick={() => setIsCreateColumnDialogOpen(true)}
                >
                  <Plus size={16} />
                </button>
              </div>

              <div className="flex-1 overflow-y-auto overflow-x-auto">
                {sortedSwimlanes.map(swimlane => (
                  <KanbanSwimlane
                    key={swimlane.id}
                    swimlane={swimlane}
                    columns={sortedColumns}
                    expanded={expandedSwimlanes[swimlane.id]}
                    onToggle={() => handleSwimlaneToggle(swimlane.id)}
                    onEdit={(name) => handleEditSwimlane(swimlane.id, name)}
                    onDelete={() => handleDeleteSwimlane(swimlane.id)}
                    getCardsForColumnAndSwimlane={getCardsForColumnAndSwimlane}
                    onAddCard={(columnId) => openCreateCardDialog(columnId, swimlane.id)}
                    onCardUpdate={handleCardUpdate}
                    onDeleteCard={handleDeleteCard}
                    isDragging={isDragging}
                    activeDndColumnId={activeDndColumnId}
                    activeDndSwimlaneId={activeDndSwimlaneId}
                  />
                ))}
              </div>
            </>
          ) : (
            <div className="flex flex-col h-full">
              <div className="flex items-center gap-2 px-4 py-3 border-b">
                <span>Active Swimlane:</span>
                <select
                  value={activeSwimlaneForColumnView || ''}
                  onChange={(e) => setActiveSwimlaneForColumnView(e.target.value)}
                  className="px-2 py-1.5 rounded-md border bg-card text-foreground"
                >
                  {sortedSwimlanes.map(swimlane => (
                    <option key={swimlane.id} value={swimlane.id}>
                      {swimlane.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex gap-4 p-4 h-[calc(100%-48px)] overflow-x-auto">
                {sortedColumns.map(column => (
                  <KanbanColumn
                    key={column.id}
                    column={column}
                    swimlaneId={activeSwimlaneForColumnView || ''}
                    cards={getCardsForColumnAndSwimlane(column.id, activeSwimlaneForColumnView || '')}
                    onAddCard={() => openCreateCardDialog(column.id, activeSwimlaneForColumnView || '')}
                    onEditColumn={(name) => handleEditColumn(column.id, name)}
                    onDeleteColumn={() => handleDeleteColumn(column.id)}
                    onCardUpdate={handleCardUpdate}
                    onDeleteCard={(cardId) => handleDeleteCard(column.id, cardId)}
                    isDragging={isDragging}
                    isDropTarget={activeDndColumnId === column.id}
                  />
                ))}
              </div>
            </div>
          )}
        </div>

        <DragOverlay>
          {activeCardId && activeBoard?.cards && (
            <div style={{ position: "relative", width: "100%" }}>
              {/* Drag overlay card - rendered by DnD library */}
            </div>
          )}
        </DragOverlay>
      </div>

      {isCreateColumnDialogOpen && (
        <CreateColumnDialog
          onClose={() => setIsCreateColumnDialogOpen(false)}
          onCreateColumn={handleCreateColumn}
        />
      )}

      {isCreateSwimlaneDialogOpen && (
        <CreateSwimlaneDialog
          onClose={() => setIsCreateSwimlaneDialogOpen(false)}
          onCreateSwimlane={handleCreateSwimlane}
        />
      )}

      {isBoardSettingsDialogOpen && (
        <BoardSettingsDialog
          board={activeBoard}
          onClose={() => setIsBoardSettingsDialogOpen(false)}
        />
      )}

      {isAgentSettingsDialogOpen && (
        <AgentIntegrationDialog
          isOpen={isAgentSettingsDialogOpen}
          onClose={() => setIsAgentSettingsDialogOpen(false)}
        />
      )}

      {isCreateCardOpen && targetColumnId && targetSwimlaneId && (
        <CreateCardDialog
          columnId={targetColumnId}
          swimlaneId={targetSwimlaneId}
          onClose={() => setIsCreateCardOpen(false)}
          onCreateCard={handleAddCard}
        />
      )}
    </DndContext>
  );
};



export default KanbanBoard;
