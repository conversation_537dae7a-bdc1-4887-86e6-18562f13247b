// src/renderer/components/kanban/AgentSidebar.tsx
import React, { useState, useEffect } from "react";
import { Activity, Bot, BrainCircuit, Database, GitBranch, LineChart, Settings, Users } from "lucide-react";
import styled from 'styled-components';
import { But<PERSON> } from "../ui/button";
import { Separator } from "../ui/separator";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "../ui/tabs";
import AgentActivityPanel from "./AgentActivityPanel";
import DependencyGraph from "./dependency-graph";
import AnalyticsDashboard from "./analytics-dashboard";
import { useToast } from "../../hooks/use-toast";
import { useAgentBoardController } from "../../contexts/agent-board-controller";

export function AgentSidebar() {
  const [activeTab, setActiveTab] = useState<string>("activity");
  const { toast } = useToast();

  // This agentStatus might reflect a global state, perhaps from useAgentBoardController
  const [agentSystemOverallStatus, setAgentSystemOverallStatus] = useState<"active" | "paused">("paused");
  const { isAgentSystemActive, toggleAgentSystem: globalToggleAgentSystem } = useAgentBoardController();

  // Effect to sync with the global agent system state
  useEffect(() => {
    setAgentSystemOverallStatus(isAgentSystemActive ? "active" : "paused");
  }, [isAgentSystemActive]);

  const handleToggleAgentSystem = () => {
    const newStatus = agentSystemOverallStatus === "active" ? "paused" : "active";
    setAgentSystemOverallStatus(newStatus); // Update local state for immediate UI feedback

    // Call the global toggle function from the agent board controller
    globalToggleAgentSystem();

    toast({
      title: `AI Agents ${newStatus === "active" ? "Activated" : "Paused"}`,
      description: `AI agents are now ${newStatus === "active" ? "actively" : "no longer"} monitoring and updating the board.`,
      variant: newStatus === "active" ? "default" : "destructive",
    });
  };

  // State for the "Last sync" time
  const [lastSyncTime, setLastSyncTime] = useState<string>(new Date().toLocaleTimeString());

  // Update the sync time periodically (in a real app, this would be triggered by actual sync events)
  useEffect(() => {
    const intervalId = setInterval(() => {
      // This is just a placeholder. A real sync update would come from
      // an IPC message or a data fetching completion.
      setLastSyncTime(new Date().toLocaleTimeString());
    }, 60000); // Update every minute for demo
    return () => clearInterval(intervalId);
  }, []);

  // Placeholder functions for menu items
  const handleAgentAssignmentsClick = () => {
    toast({
      title: "Navigate",
      description: "Agent Assignments clicked (implement navigation or dialog)",
      variant: "default"
    });
  };

  const handleResourceAllocationClick = () => {
    toast({
      title: "Navigate",
      description: "Resource Allocation clicked (implement navigation or dialog)",
      variant: "default"
    });
  };

  const handleAgentSettingsClick = () => {
    toast({
      title: "Navigate",
      description: "Agent Settings clicked (implement navigation or dialog)",
      variant: "default"
    });
    // Example: openAgentIntegrationDialog(true)
  };

  return (
    <SidebarContainer>
      <SidebarHeader>
        <HeaderTop>
          <HeaderTitle>
            <BrainCircuit size={20} />
            <h2>AI Agent Hub</h2>
          </HeaderTitle>
          <TriggerButton>
            <GitBranch size={16} />
          </TriggerButton>
        </HeaderTop>
        <ActivateButton
          isActive={agentSystemOverallStatus === "active"}
          onClick={handleToggleAgentSystem}
        >
          <Bot size={16} />
          {agentSystemOverallStatus === "active" ? "Agents Active" : "Activate Agents"}
        </ActivateButton>
      </SidebarHeader>

      <SidebarContentArea>
        <Tabs defaultValue="activity" value={activeTab} onValueChange={setActiveTab}>
          <TabsListContainer>
            <TabsTrigger value="activity">
              <Activity size={16} />
              <TabLabel>Activity</TabLabel>
            </TabsTrigger>
            <TabsTrigger value="dependencies">
              <GitBranch size={16} />
              <TabLabel>Dependencies</TabLabel>
            </TabsTrigger>
            <TabsTrigger value="analytics">
              <LineChart size={16} />
              <TabLabel>Analytics</TabLabel>
            </TabsTrigger>
          </TabsListContainer>

          <TabsContentContainer>
            <TabsContent value="activity">
              <AgentActivityPanel />
            </TabsContent>

            <TabsContent value="dependencies">
              <DependencyGraph />
            </TabsContent>

            <TabsContent value="analytics">
              <AnalyticsDashboard />
            </TabsContent>
          </TabsContentContainer>
        </Tabs>

        <Separator />

        <ManagementSection>
          <SectionTitle>Agent Management</SectionTitle>
          <MenuList>
            <MenuItem onClick={handleAgentAssignmentsClick}>
              <Users size={16} />
              <span>Agent Assignments</span>
            </MenuItem>
            <MenuItem onClick={handleResourceAllocationClick}>
              <Database size={16} />
              <span>Resource Allocation</span>
            </MenuItem>
            <MenuItem onClick={handleAgentSettingsClick}>
              <Settings size={16} />
              <span>Agent Settings</span>
            </MenuItem>
          </MenuList>
        </ManagementSection>
      </SidebarContentArea>

      <SidebarFooterArea>
        <FooterText>
          <p>AI Agent System v1.0</p>
          <p>Last sync: {lastSyncTime}</p>
        </FooterText>
      </SidebarFooterArea>
    </SidebarContainer>
  );
}

// Styled Components
const SidebarContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 300px;
  background-color: ${({ theme }) => theme.colors.background};
  border-left: 1px solid ${({ theme }) => theme.colors.border};
`;

const SidebarHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const HeaderTop = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const HeaderTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;

  h2 {
    font-size: 18px;
    font-weight: 600;
  }

  svg {
    color: ${({ theme }) => theme.colors.primary};
  }
`;

const TriggerButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 4px;

  &:hover {
    background-color: ${({ theme }) => theme.colors.foreground};
  }
`;

const ActivateButton = styled.button<{ isActive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  ${({ isActive, theme }) => isActive ? `
    background-color: ${theme.colors.primary};
    color: white;
    border: 1px solid ${theme.colors.primary};

    &:hover {
      background-color: ${theme.colors.primaryDark};
    }
  ` : `
    background-color: transparent;
    color: ${theme.colors.text.primary};
    border: 1px dashed ${theme.colors.border};

    &:hover {
      background-color: ${theme.colors.foreground};
    }
  `}
`;

const SidebarContentArea = styled.div`
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0 0 16px 0;
`;

const TabsListContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
  margin: 16px;
  position: sticky;
  top: 0;
  background-color: ${({ theme }) => theme.colors.background};
  z-index: 10;
`;

const TabLabel = styled.span`
  @media (max-width: 400px) {
    display: none;
  }
`;

const TabsContentContainer = styled.div`
  padding: 0;
`;

const ManagementSection = styled.div`
  padding: 0 16px;
`;

const SectionTitle = styled.h3`
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const MenuList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const MenuItem = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background-color: ${({ theme }) => theme.colors.foreground};
  }

  svg {
    color: ${({ theme }) => theme.colors.text.secondary};
  }
`;

const SidebarFooterArea = styled.div`
  padding: 16px;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
`;

const FooterText = styled.div`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};

  p {
    margin: 2px 0;
  }
`;

export default AgentSidebar;
