// src/renderer/components/kanban/BoardSettingsDialog.tsx
import React, { useState } from 'react';
import styled from 'styled-components';
import { X, Plus, Trash } from 'lucide-react';
import { useBoard, BoardFull, CardType } from '../../contexts/board-context';

interface BoardSettingsDialogProps {
  board: BoardFull;
  onClose: () => void;
}

const BoardSettingsDialog: React.FC<BoardSettingsDialogProps> = ({
  board,
  onClose
}) => {
  const { updateBoard, updateCardTypes } = useBoard();
  const [boardName, setBoardName] = useState(board.name);
  const [cardTypes, setCardTypes] = useState<CardType[]>(board.cardTypes);
  const [newTypeName, setNewTypeName] = useState('');
  const [newTypeColor, setNewTypeColor] = useState('#4caf50');
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (boardName.trim()) {
      // Update board name
      updateBoard(board.id, { name: boardName.trim() });
      
      // Update card types
      updateCardTypes(board.id, cardTypes);
      
      onClose();
    }
  };
  
  const handleAddCardType = () => {
    if (!newTypeName.trim()) return;
    
    const newType: CardType = {
      id: `type-${Date.now()}`,
      name: newTypeName.trim(),
      color: newTypeColor
    };
    
    setCardTypes([...cardTypes, newType]);
    setNewTypeName('');
    setNewTypeColor('#4caf50');
  };
  
  const handleRemoveCardType = (typeId: string) => {
    setCardTypes(cardTypes.filter(type => type.id !== typeId));
  };
  
  return (
    <DialogOverlay onClick={onClose}>
      <DialogContent onClick={e => e.stopPropagation()}>
        <DialogHeader>
          <DialogTitle>Board Settings</DialogTitle>
          <CloseButton onClick={onClose}>
            <X size={18} />
          </CloseButton>
        </DialogHeader>
        
        <DialogForm onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="board-name">Board Name</Label>
            <Input
              id="board-name"
              value={boardName}
              onChange={e => setBoardName(e.target.value)}
              placeholder="Enter board name"
              required
            />
          </FormGroup>
          
          <SectionTitle>Card Types</SectionTitle>
          
          <CardTypesList>
            {cardTypes.map(type => (
              <CardTypeItem key={type.id}>
                <CardTypeColor style={{ backgroundColor: type.color }} />
                <CardTypeName>{type.name}</CardTypeName>
                <RemoveButton
                  onClick={() => handleRemoveCardType(type.id)}
                  title="Remove card type"
                >
                  <Trash size={14} />
                </RemoveButton>
              </CardTypeItem>
            ))}
          </CardTypesList>
          
          <AddCardTypeForm>
            <FormRow>
              <FormGroup style={{ flex: 1 }}>
                <Label htmlFor="new-type-name">New Type Name</Label>
                <Input
                  id="new-type-name"
                  value={newTypeName}
                  onChange={e => setNewTypeName(e.target.value)}
                  placeholder="Enter type name"
                />
              </FormGroup>
              
              <FormGroup style={{ width: '100px' }}>
                <Label htmlFor="new-type-color">Color</Label>
                <ColorInput
                  id="new-type-color"
                  type="color"
                  value={newTypeColor}
                  onChange={e => setNewTypeColor(e.target.value)}
                />
              </FormGroup>
            </FormRow>
            
            <AddButton
              type="button"
              onClick={handleAddCardType}
              disabled={!newTypeName.trim()}
            >
              <Plus size={14} />
              <span>Add Type</span>
            </AddButton>
          </AddCardTypeForm>
          
          <DialogActions>
            <CancelButton type="button" onClick={onClose}>
              Cancel
            </CancelButton>
            <SubmitButton type="submit" disabled={!boardName.trim()}>
              Save Changes
            </SubmitButton>
          </DialogActions>
        </DialogForm>
      </DialogContent>
    </DialogOverlay>
  );
};

const DialogOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const DialogContent = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
`;

const DialogHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  position: sticky;
  top: 0;
  background-color: ${({ theme }) => theme.colors.background};
  z-index: 1;
`;

const DialogTitle = styled.h3`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.foreground};
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const DialogForm = styled.form`
  padding: 16px;
`;

const FormGroup = styled.div`
  margin-bottom: 16px;
`;

const FormRow = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const ColorInput = styled.input`
  width: 100%;
  height: 40px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.foreground};
  cursor: pointer;
`;

const SectionTitle = styled.h4`
  margin: 24px 0 16px;
  font-size: 15px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const CardTypesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
`;

const CardTypeItem = styled.div`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.foreground};
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

const CardTypeColor = styled.div`
  width: 16px;
  height: 16px;
  border-radius: 4px;
  margin-right: 12px;
`;

const CardTypeName = styled.span`
  flex: 1;
  font-size: 14px;
`;

const RemoveButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
    color: ${({ theme }) => theme.colors.error};
  }
`;

const AddCardTypeForm = styled.div`
  margin-bottom: 24px;
`;

const AddButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.foreground};
  border: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;
  cursor: pointer;
  
  &:hover:not(:disabled) {
    background-color: ${({ theme }) => theme.colors.background};
    border-color: ${({ theme }) => theme.colors.primary};
    color: ${({ theme }) => theme.colors.primary};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const DialogActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
`;

const Button = styled.button`
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
`;

const CancelButton = styled(Button)`
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.text.primary};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.foreground};
  }
`;

const SubmitButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.primary};
  border: 1px solid ${({ theme }) => theme.colors.primary};
  color: white;
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }
  
  &:disabled {
    background-color: ${({ theme }) => theme.colors.border};
    border-color: ${({ theme }) => theme.colors.border};
    color: ${({ theme }) => theme.colors.text.secondary};
    cursor: not-allowed;
  }
`;

export default BoardSettingsDialog;
