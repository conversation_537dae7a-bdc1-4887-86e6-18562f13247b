// src/renderer/components/kanban/KanbanCard.tsx
import React, { useState } from 'react';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { Card as UICard, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import {
  Clock, Tag, MessageSquare, Bot, MoreHorizontal, Edit, Trash, X,
  GripVertical, Activity, Link, Network, CalendarIcon
} from 'lucide-react';
import { Card, useBoard } from '../../contexts/board-context';
import { useAgentBoardController } from '../../contexts/agent-board-controller';
import CardDetailsDialog from './CardDetailsDialog';

interface KanbanCardProps {
  card: Card;
  columnId: string;
  swimlaneId: string;
  index?: number;
  onUpdate?: (card: Card) => void;
  onDelete?: () => void;
  isDragOverlay?: boolean;
}

const KanbanCard: React.FC<KanbanCardProps> = ({
  card,
  columnId,
  swimlaneId,
  index,
  onUpdate,
  onDelete,
  isDragOverlay = false
}) => {
  const { activeBoard } = useBoard();
  const { getAgentsAssignedToCard } = useAgentBoardController();
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [showMenu, setShowMenu] = useState(false);

  // Get card type
  const cardType = activeBoard?.cardTypes.find(type => type.id === card.type);

  // Get assigned agents
  const assignedAgents = getAgentsAssignedToCard(card.id);

  // Check if card has dependencies
  const hasDependencies = card.dependencies && card.dependencies.length > 0;

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);

    // Check if the date is today
    const today = new Date();
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    }

    // Check if the date is tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    }

    // Check if the date is in the past
    if (date < today) {
      return `Overdue: ${date.toLocaleDateString()}`;
    }

    return date.toLocaleDateString();
  };

  // Open card details
  const handleOpenDetailView = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDetailsOpen(true);
  };

  // Handle card update
  const handleCardUpdate = (updatedCard: Card) => {
    if (onUpdate) {
      onUpdate(updatedCard);
    }
  };

  // Handle card menu
  const handleCardMenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMenu(!showMenu);
  };

  // Handle edit card
  const handleEditCard = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMenu(false);
    setIsDetailsOpen(true);
  };

  // Handle delete card
  const handleDeleteCard = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMenu(false);
    if (window.confirm(`Are you sure you want to delete the card "${card.title}"?`)) {
      if (onDelete) {
        onDelete();
      }
    }
  };

  // Set up draggable
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: card.id,
    data: {
      type: 'card',
      card: card,
      columnId: columnId,
      swimlaneId: swimlaneId,
      index: index
    },
    disabled: isDragOverlay,
  });

  const style = !isDragOverlay
    ? {
        transform: transform ? CSS.Transform.toString(transform) : undefined,
        opacity: isDragging ? 0.8 : 1,
        zIndex: isDragging ? 1000 : undefined,
        position: isDragging ? ("relative" as const) : undefined,
      }
    : {};

  // Card type color styling
  const cardTypeColorStyle = cardType
    ? { borderLeftColor: cardType.color, borderLeftWidth: '4px' }
    : {};

  // Get color for priority if no card type
  const getPriorityColor = () => {
    if (cardType) return cardType.color;

    if (card.priority === 'high') return '#f44336';
    if (card.priority === 'medium') return '#ff9800';
    return '#4caf50';
  };

  const priorityColor = getPriorityColor();

  return (
    <>
      <div
        ref={!isDragOverlay ? setNodeRef : undefined}
        style={{ ...style, ...cardTypeColorStyle }}
        className={`mb-2 transition-shadow duration-200 rounded-md max-w-full ${
          isDragging || isDragOverlay ? "shadow-xl" : "hover:shadow-lg hover:-translate-y-0.5 cursor-pointer"
        }`}
        onClick={handleOpenDetailView}
      >
        <UICard
          className={`shadow-sm transition-all duration-200 w-full overflow-hidden ${
            isDragging || isDragOverlay ? "opacity-90 border-primary/50" : ""
          }`}
        >
          <CardContent className="p-3 relative overflow-hidden">
            {/* Drag Handle - Only show if not a drag overlay */}
            {!isDragOverlay && (
              <div
                {...attributes}
                {...listeners}
                className="absolute top-1 right-1 p-1.5 rounded-full text-muted-foreground hover:bg-muted hover:text-foreground cursor-grab active:cursor-grabbing"
                onClick={(e) => e.stopPropagation()}
              >
                <GripVertical className="h-4 w-4" />
              </div>
            )}

            {/* Card Header */}
            <div className="flex justify-between items-start mb-1 pr-7">
              <h3 className="text-sm font-medium leading-tight line-clamp-2">{card.title}</h3>
              <button
                onClick={handleCardMenu}
                className="p-0.5 rounded hover:bg-muted text-muted-foreground hover:text-foreground"
              >
                <MoreHorizontal className="h-4 w-4" />
              </button>
            </div>

            {/* Card ID for debugging */}
            {process.env.NODE_ENV === 'development' && (
              <div className="text-xs text-muted-foreground mb-1.5">ID: {card.id.substring(0, 8)}</div>
            )}

            {/* Card Description */}
            {card.description && (
              <div className="text-xs text-muted-foreground mb-2 line-clamp-2">{card.description}</div>
            )}

            {/* Dependencies indicator */}
            {hasDependencies && (
              <div className="mb-2 flex items-center gap-1 text-xs text-muted-foreground bg-muted/40 p-1 rounded">
                <Link className="h-3 w-3" />
                <span>
                  {card.dependencies?.length} {card.dependencies?.length === 1 ? "dependency" : "dependencies"}
                </span>
              </div>
            )}

            {/* Resource usage mini chart */}
            {card.resourceMetrics && (
              <div className="mb-2 bg-muted/40 p-1.5 rounded-sm text-xs">
                <div className="flex justify-between items-center gap-2">
                  <span className="flex items-center">
                    <Activity className="h-3 w-3 mr-0.5 text-blue-500" />
                    {card.resourceMetrics.cpuTime || 0}%
                  </span>
                  <span className="flex items-center">
                    <Network className="h-3 w-3 mr-0.5 text-green-500" />
                    {card.resourceMetrics.memoryUsage || 0}MB
                  </span>
                </div>
              </div>
            )}

            {/* Progress bar */}
            {card.progress !== undefined && (
              <div className="relative w-full mb-3">
                <div className="w-full h-1 bg-muted rounded-sm overflow-hidden">
                  <div
                    className={`h-full ${
                      card.progress < 30 ? 'bg-destructive' :
                      card.progress < 70 ? 'bg-amber-500' :
                      'bg-emerald-500'
                    }`}
                    style={{ width: `${card.progress}%` }}
                  />
                </div>
                <span className="absolute right-0 top-1.5 text-[10px] text-muted-foreground">
                  {card.progress}%
                </span>
              </div>
            )}

            {/* Tags and Priority */}
            <div className="flex flex-wrap gap-1 mb-2">
              {/* Priority Badge */}
              {cardType && (
                <Badge
                  variant="outline"
                  className="text-[10px] px-1.5 py-0.5 h-5 font-normal"
                  style={{
                    backgroundColor: `${priorityColor}20`,
                    color: priorityColor,
                    borderColor: `${priorityColor}80`,
                  }}
                >
                  {cardType.name}
                </Badge>
              )}

              {/* Priority as tag if no card type */}
              {!cardType && card.priority && (
                <Badge
                  variant="outline"
                  className="text-[10px] px-1.5 py-0.5 h-5 font-normal"
                  style={{
                    backgroundColor: `${priorityColor}20`,
                    color: priorityColor,
                    borderColor: `${priorityColor}80`,
                  }}
                >
                  {card.priority.charAt(0).toUpperCase() + card.priority.slice(1)} Priority
                </Badge>
              )}

              {/* Tags */}
              {card.tags?.map((tag: string) => (
                <Badge key={tag} variant="secondary" className="text-[10px] px-1.5 py-0.5 h-5 font-normal">
                  {tag}
                </Badge>
              ))}
            </div>

            <div className="flex justify-between items-center mt-2">
              {/* Agent Assignments */}
              <div className="flex -space-x-1">
                {assignedAgents.slice(0, 3).map(agent => {
                  const statusColor =
                    agent.status === 'working' ? 'bg-emerald-500' :
                    agent.status === 'paused' ? 'bg-amber-500' :
                    agent.status === 'error' ? 'bg-destructive' :
                    'bg-muted-foreground';

                  return (
                    <div
                      key={agent.id}
                      className="relative"
                      title={agent.name}
                    >
                      <div className="h-5 w-5 rounded-full flex items-center justify-center text-[10px] font-semibold bg-muted-foreground text-white border-2 border-background">
                        {agent.name.charAt(0)}
                        <span className={`absolute -top-0.5 -right-0.5 w-1.5 h-1.5 rounded-full ${statusColor} border border-background`}></span>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Due Date */}
              {card.dueDate && (
                <div className={`flex items-center text-xs ${
                  new Date(card.dueDate) < new Date() && card.progress !== 100
                    ? 'text-destructive'
                    : 'text-muted-foreground'
                }`}>
                  <Clock className="h-3 w-3 mr-1" />
                  <span>{formatDate(card.dueDate)}</span>
                </div>
              )}
            </div>

            {/* Last Updated */}
            {card.updatedAt && (
              <div className="flex justify-end mt-2">
                <div className="flex items-center text-xs text-muted-foreground">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatDate(card.updatedAt)}
                </div>
              </div>
            )}
          </CardContent>
        </UICard>
      </div>

      {/* Card Menu Dropdown */}
      {showMenu && (
        <div
          className="fixed inset-0 z-50"
          onClick={() => setShowMenu(false)}
        >
          <div
            className="absolute z-[51] w-40 bg-card rounded-md shadow-md border overflow-hidden"
            onClick={e => e.stopPropagation()}
          >
            <button
              className="flex items-center gap-2 w-full px-3 py-2 text-sm text-left hover:bg-muted"
              onClick={handleEditCard}
            >
              <Edit className="h-4 w-4" />
              <span>Edit Card</span>
            </button>
            <button
              className="flex items-center gap-2 w-full px-3 py-2 text-sm text-left hover:bg-muted text-destructive"
              onClick={handleDeleteCard}
            >
              <Trash className="h-4 w-4" />
              <span>Delete Card</span>
            </button>
            <button
              className="absolute top-1 right-1 flex items-center justify-center w-5 h-5 rounded-full hover:bg-muted text-muted-foreground"
              onClick={() => setShowMenu(false)}
            >
              <X className="h-3 w-3" />
            </button>
          </div>
        </div>
      )}

      {/* Card Detail View for editing */}
      {!isDragOverlay && isDetailsOpen && (
        <CardDetailsDialog
          card={card}
          onClose={() => setIsDetailsOpen(false)}
          onUpdate={handleCardUpdate}
          onDelete={onDelete}
        />
      )}
    </>
  );
};

export default KanbanCard;
