// src/renderer/components/kanban/KanbanColumn.tsx
import React, { useState, useMemo } from 'react';
import { useDroppable } from '@dnd-kit/core';
import { Card as UICard, CardHeader, CardTitle } from '../ui/card';
import KanbanCard from './KanbanCard';
import { Column, Card } from '../../contexts/board-context';
import { MoreHorizontal, PlusCircle, Trash, Edit, Plus } from 'lucide-react';

interface KanbanColumnProps {
  column: Column;
  swimlaneId: string;
  cards: Card[];
  onAddCard?: () => void;
  onEditColumn?: (name: string) => void;
  onDeleteColumn?: () => void;
  onCardUpdate?: (card: Card) => void;
  onDeleteCard?: (cardId: string) => void;
  isDragging?: boolean;
  isDropTarget?: boolean;
}

const KanbanColumn: React.FC<KanbanColumnProps> = ({
  column,
  swimlaneId,
  cards,
  onAddCard,
  onEditColumn,
  onDeleteColumn,
  onCardUpdate,
  onDeleteCard,
  isDragging,
  isDropTarget
}) => {
  const [showMenu, setShowMenu] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [editName, setEditName] = useState(column.name);

  // Set up droppable for the column
  const { setNodeRef, isOver } = useDroppable({
    id: `column-${column.id}-swimlane-${swimlaneId}`,
    data: {
      type: 'column',
      columnId: column.id,
      swimlaneId: swimlaneId
    }
  });

  // Handle edit column
  const handleEditColumn = () => {
    setShowMenu(false);
    setEditMode(true);
    setEditName(column.name);
  };

  // Handle save edit
  const handleSaveEdit = () => {
    if (editName.trim() && onEditColumn) {
      onEditColumn(editName.trim());
      setEditMode(false);
    }
  };

  // Handle key down in edit mode
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      setEditMode(false);
      setEditName(column.name);
    }
  };

  // Handle delete column
  const handleDeleteColumn = () => {
    setShowMenu(false);
    if (window.confirm(`Are you sure you want to delete the column "${column.name}"? This will also delete all cards in this column.`)) {
      if (onDeleteColumn) {
        onDeleteColumn();
      }
    }
  };

  // Determine if this column is the active drop target
  const showAsDropTarget = isDropTarget && isOver;

  // Determine column background color based on name
  const getColumnBackground = () => {
    const name = column.name.toLowerCase();

    if (name.includes('backlog')) {
      return 'bg-gradient-to-b from-gray-50/80 to-gray-100/50 dark:from-gray-900/30 dark:to-gray-800/20';
    }
    if (name.includes('ready')) {
      return 'bg-gradient-to-b from-blue-50/80 to-blue-100/50 dark:from-blue-950/30 dark:to-blue-900/20';
    }
    if (name.includes('development') || name.includes('in dev')) {
      return 'bg-gradient-to-b from-amber-50/80 to-amber-100/50 dark:from-amber-950/30 dark:to-amber-900/20';
    }
    if (name.includes('review')) {
      return 'bg-gradient-to-b from-purple-50/80 to-purple-100/50 dark:from-purple-950/30 dark:to-purple-900/20';
    }
    if (name.includes('testing') || name.includes('qa')) {
      return 'bg-gradient-to-b from-indigo-50/80 to-indigo-100/50 dark:from-indigo-950/30 dark:to-indigo-900/20';
    }
    if (name.includes('done') || name.includes('complete')) {
      return 'bg-gradient-to-b from-green-50/80 to-green-100/50 dark:from-green-950/30 dark:to-green-900/20';
    }

    return 'bg-gradient-to-b from-gray-50/80 to-gray-100/50 dark:from-gray-900/30 dark:to-gray-800/20';
  };

  const handleCardUpdate = (updatedCard: Card) => {
    if (onCardUpdate) {
      onCardUpdate(updatedCard);
    }
  };

  return (
    <div
      ref={setNodeRef}
      className={`
        flex flex-col w-[280px] min-w-[280px] max-w-[280px] h-full rounded-md
        border border-[#e0e0e0] dark:border-[#3e3e42]
        ${getColumnBackground()}
        backdrop-blur-[2px]
        transition-all duration-150 ease-in-out
        ${showAsDropTarget ? "ring-2 ring-primary shadow-lg scale-[1.01]" : ""}
        ${isDragging && !showAsDropTarget ? "opacity-70" : ""}
      `}
    >
      <CardHeader className={`p-3 border-b border-[#e0e0e0] dark:border-[#3e3e42] flex flex-row items-center justify-between ${column.color ? `border-t-[3px] border-t-[${column.color}]` : ''}`}>
        {editMode ? (
          <div className="flex items-center gap-1 w-full">
            <input
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onBlur={handleSaveEdit}
              onKeyDown={handleKeyDown}
              className="h-7 text-sm px-2 py-1 rounded border border-primary bg-background focus:outline-none focus:ring-1 focus:ring-primary/20 w-full"
              autoFocus
            />
          </div>
        ) : (
          <div className="flex items-center justify-between w-full">
            <CardTitle className="text-sm font-medium flex items-center">
              {column.name}
              <span className="ml-2 text-xs text-muted-foreground">{cards.length}</span>
            </CardTitle>
            <div className="flex items-center">
              <button
                className="h-7 w-7 flex items-center justify-center rounded-md hover:bg-muted text-muted-foreground hover:text-foreground"
                onClick={onAddCard}
                title="Add Card"
              >
                <PlusCircle className="h-4 w-4" />
                <span className="sr-only">Add card</span>
              </button>

              <div className="relative">
                <button
                  className="h-7 w-7 flex items-center justify-center rounded-md hover:bg-muted text-muted-foreground hover:text-foreground"
                  onClick={() => setShowMenu(!showMenu)}
                  title="Column Menu"
                >
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">More options</span>
                </button>

                {showMenu && (
                  <>
                    <div
                      className="fixed inset-0 z-40"
                      onClick={() => setShowMenu(false)}
                    />
                    <div className="absolute top-full right-0 z-50 w-40 bg-card rounded-md shadow-md border overflow-hidden">
                      <button
                        className="flex items-center gap-2 w-full px-3 py-2 text-sm text-left hover:bg-muted"
                        onClick={handleEditColumn}
                      >
                        <Edit className="h-4 w-4" />
                        <span>Edit Column</span>
                      </button>
                      <button
                        className="flex items-center gap-2 w-full px-3 py-2 text-sm text-left hover:bg-muted text-destructive"
                        onClick={handleDeleteColumn}
                      >
                        <Trash className="h-4 w-4" />
                        <span>Delete Column</span>
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </CardHeader>

      <div className="flex flex-col gap-2 p-2 overflow-y-auto overflow-x-hidden flex-1 min-h-[60px]">
        {cards.length > 0 ? (
          cards.map((card, index) => (
            <KanbanCard
              key={card.id}
              card={card}
              columnId={column.id}
              swimlaneId={swimlaneId}
              index={index}
              onUpdate={handleCardUpdate}
              onDelete={onDeleteCard ? () => onDeleteCard(card.id) : undefined}
            />
          ))
        ) : (
          <div
            className={`
              flex items-center justify-center h-20 rounded-md
              border-2 border-dashed
              transition-all duration-200
              ${showAsDropTarget ? "border-primary/70 bg-primary/10 scale-105" : "border-muted-foreground/20"}
              ${isDragging && !showAsDropTarget ? "border-muted-foreground/10 bg-muted/5" : ""}
            `}
          >
            <p className="text-xs text-muted-foreground">{showAsDropTarget ? "Drop here" : "No cards"}</p>
          </div>
        )}

        <button
          className="flex items-center gap-1.5 px-3 py-2 rounded-md border border-dashed border-muted-foreground/30 text-muted-foreground text-sm hover:bg-muted hover:text-foreground hover:border-primary/40 transition-colors"
          onClick={onAddCard}
        >
          <Plus className="h-3.5 w-3.5" />
          <span>Add Card</span>
        </button>
      </div>
    </div>
  );
};

export default KanbanColumn;
