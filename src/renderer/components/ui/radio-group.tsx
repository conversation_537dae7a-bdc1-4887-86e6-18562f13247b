import * as React from 'react';
import { cn } from '../../lib/utils';

export interface RadioGroupProps {
  value?: string;
  onChange?: (value: string) => void;
  defaultValue?: string;
  className?: string;
  children?: React.ReactNode;
  role?: string;
  id?: string;
}

const RadioGroupContext = React.createContext<{
  value?: string;
  onChange?: (value: string) => void;
} | null>(null);

const RadioGroup = React.forwardRef<HTMLDivElement, RadioGroupProps>(
  ({ className, value, onChange, defaultValue, ...props }, ref) => {
    const [selectedValue, setSelectedValue] = React.useState<string | undefined>(
      value || defaultValue
    );

    React.useEffect(() => {
      if (value !== undefined) {
        setSelectedValue(value);
      }
    }, [value]);

    const handleChange = React.useCallback(
      (newValue: string) => {
        if (value === undefined) {
          setSelectedValue(newValue);
        }
        onChange?.(newValue);
      },
      [onChange, value]
    );

    return (
      <RadioGroupContext.Provider
        value={{ value: selectedValue, onChange: handleChange }}
      >
        <div
          ref={ref}
          className={cn('grid gap-2', className)}
          role="radiogroup"
          {...props}
        />
      </RadioGroupContext.Provider>
    );
  }
);
RadioGroup.displayName = 'RadioGroup';

export interface RadioGroupItemProps extends React.InputHTMLAttributes<HTMLInputElement> {
  value: string;
  label?: string;
}

const RadioGroupItem = React.forwardRef<HTMLInputElement, RadioGroupItemProps>(
  ({ className, value, label, ...props }, ref) => {
    const context = React.useContext(RadioGroupContext);
    if (!context) {
      throw new Error('RadioGroupItem must be used within a RadioGroup');
    }

    const { value: selectedValue, onChange } = context;
    const checked = selectedValue === value;

    return (
      <div className="flex items-center space-x-2">
        <input
          type="radio"
          ref={ref}
          className={cn(
            'h-4 w-4 rounded-full border border-input bg-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
            className
          )}
          value={value}
          checked={checked}
          onChange={() => onChange?.(value)}
          {...props}
        />
        {label && (
          <label
            htmlFor={props.id}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
          </label>
        )}
      </div>
    );
  }
);
RadioGroupItem.displayName = 'RadioGroupItem';

export { RadioGroup, RadioGroupItem };
