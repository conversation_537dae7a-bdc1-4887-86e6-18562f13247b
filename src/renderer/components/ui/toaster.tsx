import React, { useState, useEffect } from 'react';
import { Toast, ToastClose, ToastTitle, ToastDescription } from './toast';
import { useToast } from '../../hooks/use-toast';

interface ToasterProps {
  className?: string;
}

export function Toaster({ className }: ToasterProps) {
  const { toasts } = useToast();
  
  return (
    <div className="fixed top-0 right-0 z-50 flex flex-col p-4 space-y-4 max-w-md w-full">
      {toasts.map(({ id, title, description, variant, onClose }) => (
        <div key={id} className="flex flex-col items-end">
          <Toast variant={variant} className={className}>
            <div className="flex flex-col gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && <ToastDescription>{description}</ToastDescription>}
            </div>
            {onClose && <ToastClose onClick={onClose} />}
          </Toast>
        </div>
      ))}
    </div>
  );
}
