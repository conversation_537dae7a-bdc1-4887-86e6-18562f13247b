import * as React from 'react';
import { cn } from '../../lib/utils';

interface AccordionContextValue {
  expanded: Record<string, boolean>;
  toggle: (value: string) => void;
}

const AccordionContext = React.createContext<AccordionContextValue | undefined>(undefined);

function useAccordion() {
  const context = React.useContext(AccordionContext);
  if (!context) {
    throw new Error('useAccordion must be used within an Accordion component');
  }
  return context;
}

interface AccordionProps extends React.HTMLAttributes<HTMLDivElement> {
  type?: 'single' | 'multiple';
  defaultValue?: string | string[];
  value?: string | string[];
  onValueChange?: (value: string | string[]) => void;
  collapsible?: boolean;
}

const Accordion = React.forwardRef<HTMLDivElement, AccordionProps>(
  ({ className, type = 'single', defaultValue, value, onValueChange, collapsible = false, ...props }, ref) => {
    const [expanded, setExpanded] = React.useState<Record<string, boolean>>(() => {
      const initialState: Record<string, boolean> = {};
      
      if (type === 'single') {
        const singleValue = value || defaultValue;
        if (typeof singleValue === 'string') {
          initialState[singleValue] = true;
        }
      } else if (type === 'multiple') {
        const multipleValue = value || defaultValue;
        if (Array.isArray(multipleValue)) {
          multipleValue.forEach((val) => {
            initialState[val] = true;
          });
        }
      }
      
      return initialState;
    });

    React.useEffect(() => {
      if (value !== undefined) {
        const newExpanded: Record<string, boolean> = {};
        
        if (type === 'single' && typeof value === 'string') {
          newExpanded[value] = true;
        } else if (type === 'multiple' && Array.isArray(value)) {
          value.forEach((val) => {
            newExpanded[val] = true;
          });
        }
        
        setExpanded(newExpanded);
      }
    }, [value, type]);

    const toggle = React.useCallback(
      (itemValue: string) => {
        setExpanded((prev) => {
          const newExpanded = { ...prev };
          
          if (type === 'single') {
            // If the item is already expanded and collapsible is true, collapse it
            if (newExpanded[itemValue] && collapsible) {
              newExpanded[itemValue] = false;
              onValueChange?.('');
            } else {
              // Otherwise, expand this item and collapse all others
              Object.keys(newExpanded).forEach((key) => {
                newExpanded[key] = false;
              });
              newExpanded[itemValue] = true;
              onValueChange?.(itemValue);
            }
          } else if (type === 'multiple') {
            // Toggle the expanded state of this item
            newExpanded[itemValue] = !newExpanded[itemValue];
            
            // Notify about the change
            const expandedValues = Object.entries(newExpanded)
              .filter(([, isExpanded]) => isExpanded)
              .map(([key]) => key);
            
            onValueChange?.(expandedValues);
          }
          
          return newExpanded;
        });
      },
      [type, collapsible, onValueChange]
    );

    return (
      <AccordionContext.Provider value={{ expanded, toggle }}>
        <div ref={ref} className={cn('', className)} {...props} />
      </AccordionContext.Provider>
    );
  }
);
Accordion.displayName = 'Accordion';

interface AccordionItemProps extends React.HTMLAttributes<HTMLDivElement> {
  value: string;
}

const AccordionItem = React.forwardRef<HTMLDivElement, AccordionItemProps>(
  ({ className, value, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('border-b', className)}
        data-value={value}
        {...props}
      />
    );
  }
);
AccordionItem.displayName = 'AccordionItem';

interface AccordionTriggerProps extends React.HTMLAttributes<HTMLButtonElement> {
  value: string;
}

const AccordionTrigger = React.forwardRef<HTMLButtonElement, AccordionTriggerProps>(
  ({ className, children, value, ...props }, ref) => {
    const { expanded, toggle } = useAccordion();
    const isExpanded = expanded[value] || false;

    return (
      <button
        ref={ref}
        type="button"
        aria-expanded={isExpanded}
        className={cn(
          'flex w-full items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180',
          className
        )}
        onClick={() => toggle(value)}
        {...props}
      >
        {children}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className={cn(
            'h-4 w-4 shrink-0 transition-transform duration-200',
            isExpanded ? 'rotate-180' : 'rotate-0'
          )}
        >
          <polyline points="6 9 12 15 18 9"></polyline>
        </svg>
      </button>
    );
  }
);
AccordionTrigger.displayName = 'AccordionTrigger';

interface AccordionContentProps extends React.HTMLAttributes<HTMLDivElement> {
  value: string;
}

const AccordionContent = React.forwardRef<HTMLDivElement, AccordionContentProps>(
  ({ className, children, value, ...props }, ref) => {
    const { expanded } = useAccordion();
    const isExpanded = expanded[value] || false;

    if (!isExpanded) {
      return null;
    }

    return (
      <div
        ref={ref}
        className={cn(
          'overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down',
          className
        )}
        {...props}
      >
        <div className="pb-4 pt-0">{children}</div>
      </div>
    );
  }
);
AccordionContent.displayName = 'AccordionContent';

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent };
