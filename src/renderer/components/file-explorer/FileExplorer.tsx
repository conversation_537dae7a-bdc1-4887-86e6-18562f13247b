// src/renderer/components/file-explorer/FileExplorer.tsx
import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { FileExplorerItem } from './FileExplorerItem';
import { FileContextMenu } from './FileContextMenu';
import { gitService, GitStatusResult } from '../../services/GitService';
import { ModernGitCommitDialog } from '../git/ModernGitCommitDialog';

// Define FileInfo interface
interface FileInfo {
  name: string;
  path: string;
  type: 'file' | 'directory';
  children?: FileInfo[];
  gitStatus?: 'modified' | 'staged' | 'untracked';
}

interface FileExplorerProps {
  onFileSelect: (filePath: string, content: string) => void;
}

export const FileExplorer: React.FC<FileExplorerProps> = ({ onFileSelect }) => {
  const [rootPath, setRootPath] = useState<string | null>(null);
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [selectedPath, setSelectedPath] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isGitRepo, setIsGitRepo] = useState<boolean>(false);
  const [currentBranch, setCurrentBranch] = useState<string>('');
  const [gitStatus, setGitStatus] = useState<GitStatusResult | null>(null);
  const [isCommitDialogOpen, setIsCommitDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [contextMenu, setContextMenu] = useState<{ x: number; y: number; visible: boolean }>({
    x: 0,
    y: 0,
    visible: false
  });
  const explorerContainerRef = useRef<HTMLDivElement>(null);

  const loadDirectory = async (dirPath: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await window.electron.ipcRenderer.invoke('fs:readDirectory', { dirPath });

      if (response && response.success && response.files) {
        setFiles(response.files);
        setRootPath(dirPath);

        // Check if it's a Git repository
        const isRepo = await gitService.isGitRepository(dirPath);
        setIsGitRepo(isRepo);

        if (isRepo) {
          // Get Git status
          const statusResponse = await gitService.getStatus(dirPath);
          if (statusResponse && statusResponse.success && statusResponse.result) {
            setGitStatus(statusResponse.result);

            // Apply Git status to files
            const filesWithGitStatus = response.files.map((file: FileInfo) => {
              const filePath = file.path;

              if (statusResponse.result!.staged.includes(file.name)) {
                return { ...file, gitStatus: 'staged' as const };
              }

              if (statusResponse.result!.modified.includes(file.name)) {
                return { ...file, gitStatus: 'modified' as const };
              }

              if (statusResponse.result!.untracked.includes(file.name)) {
                return { ...file, gitStatus: 'untracked' as const };
              }

              return file;
            });

            setFiles(filesWithGitStatus);
          }

          // Get current branch
          const branchResponse = await gitService.getCurrentBranch(dirPath);
          if (branchResponse && branchResponse.success && branchResponse.branch) {
            setCurrentBranch(branchResponse.branch);
          }
        }
      } else {
        setError(response ? response.error || 'Failed to load directory' : 'Invalid response from filesystem');
      }
    } catch (error) {
      setError((error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenFolder = async () => {
    try {
      // Close any open context menu first
      closeContextMenu();

      const response = await window.electron.ipcRenderer.invoke('fs:openDirectory', null);

      if (response && response.success && response.path) {
        await loadDirectory(response.path);
      }
    } catch (error) {
      setError((error as Error).message);
    }
  };

  const handleItemClick = async (item: FileInfo) => {
    if (item.type === 'file') {
      setSelectedPath(item.path);

      try {
        const response = await window.electron.ipcRenderer.invoke('fs:readFile', { filePath: item.path });

        if (response && response.success && response.content !== undefined) {
          onFileSelect(item.path, response.content);
        } else {
          setError(response ? response.error || 'Failed to read file' : 'Invalid response from filesystem');
        }
      } catch (error) {
        setError((error as Error).message);
      }
    }
  };

  const closeContextMenu = () => {
    setContextMenu({ ...contextMenu, visible: false });
  };

  const handleExplorerContextMenu = (e: React.MouseEvent) => {
    // Only show context menu if a directory is open
    if (!rootPath) return;

    e.preventDefault();
    e.stopPropagation();

    // Close any existing context menu first
    closeContextMenu();

    // After a small delay, open the new menu
    setTimeout(() => {
      setContextMenu({
        x: e.clientX,
        y: e.clientY,
        visible: true
      });
    }, 10);
  };

  const handleCreateFile = async () => {
    closeContextMenu();

    setTimeout(async () => {
      if (!rootPath) return;

      const fileName = prompt('Enter file name:');
      if (!fileName) return;

      const filePath = window.electron.path.join(rootPath, fileName);
      try {
        const response = await window.electron.ipcRenderer.invoke('fs:createFile', { filePath });
        if (response.success) {
          // Refresh the directory
          await loadDirectory(rootPath);
        } else {
          alert(`Failed to create file: ${response.error}`);
        }
      } catch (error) {
        console.error('Error creating file:', error);
      }
    }, 50);
  };

  const handleCreateDirectory = async () => {
    closeContextMenu();

    setTimeout(async () => {
      if (!rootPath) return;

      const dirName = prompt('Enter folder name:');
      if (!dirName) return;

      const dirPath = window.electron.path.join(rootPath, dirName);
      try {
        const response = await window.electron.ipcRenderer.invoke('fs:createDirectory', { dirPath });
        if (response.success) {
          // Refresh the directory
          await loadDirectory(rootPath);
        } else {
          alert(`Failed to create directory: ${response.error}`);
        }
      } catch (error) {
        console.error('Error creating directory:', error);
      }
    }, 50);
  };

  const handleCommitSuccess = () => {
    if (rootPath) {
      loadDirectory(rootPath);
    }
  };

  // Filter files based on search term
  const filteredFiles = files.filter(file => {
    if (!searchTerm) return true;
    return file.name.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Count total files
  const countFiles = (fileList: FileInfo[]): number => {
    let count = 0;
    for (const file of fileList) {
      if (file.type === 'file') count++;
    }
    return count;
  };

  const totalFiles = countFiles(files);

  // Global event handlers to ensure context menus close properly
  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      // Don't close if clicking within the context menu itself
      const contextMenuElement = document.querySelector('.context-menu');
      if (contextMenuElement && contextMenuElement.contains(e.target as Node)) {
        return;
      }

      // Otherwise close any open context menu
      closeContextMenu();
    };

    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closeContextMenu();
      }
    };

    // Add these handlers to document to catch all events
    document.addEventListener('mousedown', handleGlobalClick);
    document.addEventListener('keydown', handleEscapeKey);

    return () => {
      document.removeEventListener('mousedown', handleGlobalClick);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, []);

  return (
    <ExplorerContainer
      ref={explorerContainerRef}
      onContextMenu={handleExplorerContextMenu}
    >
      <ExplorerHeader>
        <ExplorerTitle>Explorer</ExplorerTitle>
        <SettingsButton>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </SettingsButton>
      </ExplorerHeader>

      <SearchBar>
        <SearchIcon>
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </SearchIcon>
        <SearchInput
          type="text"
          placeholder="Search files..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchBar>

      <ProjectsSection>
        <SectionHeader>
          <SectionTitle>PROJECTS</SectionTitle>
          <AddButton onClick={handleOpenFolder}>+</AddButton>
        </SectionHeader>

        <FilesContainer>
          {loading && <LoadingIndicator>Loading...</LoadingIndicator>}

          {error && <ErrorMessage>{error}</ErrorMessage>}

          {!rootPath && !loading && (
            <EmptyState>
              <EmptyIcon>
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M3 7V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V9C21 7.89543 20.1046 7 19 7H13L11 5H5C3.89543 5 3 5.89543 3 7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </EmptyIcon>
              <EmptyText>No projects open</EmptyText>
              <OpenFolderButton onClick={handleOpenFolder}>Open Folder</OpenFolderButton>
            </EmptyState>
          )}

          {rootPath && filteredFiles.length === 0 && !loading && (
            <EmptyState>
              <EmptyIcon>
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M3 7V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V9C21 7.89543 20.1046 7 19 7H13L11 5H5C3.89543 5 3 5.89543 3 7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </EmptyIcon>
              <EmptyText>Folder is empty</EmptyText>
              <EmptySubtext>Right-click to create files</EmptySubtext>
            </EmptyState>
          )}

          {filteredFiles.map((file) => (
            <FileExplorerItem
              key={file.path}
              item={file}
              level={0}
              onFileSelect={handleItemClick}
              selectedPath={selectedPath}
              onRefresh={() => rootPath && loadDirectory(rootPath)}
              isGitRepo={isGitRepo}
              rootPath={rootPath || ''}
            />
          ))}
        </FilesContainer>
      </ProjectsSection>

      {rootPath && (
        <StatusBar>
          <StatusText>{isGitRepo ? `${currentBranch}` : ''}</StatusText>
          <FileCount>{rootPath ? `${totalFiles} files` : ''}</FileCount>
        </StatusBar>
      )}

      <FileContextMenu
        x={contextMenu.x}
        y={contextMenu.y}
        isVisible={contextMenu.visible}
        isDirectory={true}  // Root is always a directory
        onCreateFile={handleCreateFile}
        onCreateDirectory={handleCreateDirectory}
        onDelete={() => {}} // Not applicable for root
        onRename={() => {}} // Not applicable for root
        onClose={closeContextMenu}
        isGitRepo={isGitRepo}
        item={null}
        rootPath={rootPath || ''}
        onStage={() => {}}
        onUnstage={() => {}}
        onRefresh={() => rootPath && loadDirectory(rootPath)}
      />

      {isGitRepo && rootPath && (
        <ModernGitCommitDialog
          isOpen={isCommitDialogOpen}
          onClose={() => setIsCommitDialogOpen(false)}
          rootPath={rootPath}
          onSuccess={handleCommitSuccess}
        />
      )}
    </ExplorerContainer>
  );
};

const ExplorerContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background-color: ${({ theme }) => theme.mode === 'dark' ? '#252526' : '#f3f3f3'};
  border-right: 1px solid ${({ theme }) => theme.colors.border};
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
`;

const ExplorerHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  color: ${({ theme }) => theme.colors.text.primary};
  user-select: none;
  text-transform: uppercase;
  font-size: 11px;
  font-weight: 600;
  letter-spacing: 0.5px;
`;

const ExplorerTitle = styled.h2`
  font-size: 11px;
  font-weight: 600;
  margin: 0;
  color: ${({ theme }) => theme.mode === 'dark' ? '#bbbbbb' : '#6f6f6f'};
`;

const SettingsButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  padding: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;

  &:hover {
    opacity: 1;
  }
`;

const SearchBar = styled.div`
  margin: 0 12px 8px;
  padding: 4px 6px;
  background-color: ${({ theme }) => theme.mode === 'dark' ? '#3c3c3c' : '#eaeaea'};
  display: flex;
  align-items: center;
  height: 24px;
`;

const SearchIcon = styled.span`
  color: ${({ theme }) => theme.mode === 'dark' ? '#cccccc' : '#616161'};
  margin-right: 6px;
  display: flex;
  align-items: center;
`;

const SearchInput = styled.input`
  background: none;
  border: none;
  color: ${({ theme }) => theme.mode === 'dark' ? '#cccccc' : '#333333'};
  font-size: 13px;
  width: 100%;
  outline: none;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

  &::placeholder {
    color: ${({ theme }) => theme.mode === 'dark' ? '#cccccc80' : '#61616180'};
  }
`;

const ProjectsSection = styled.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 16px 4px 10px;
  user-select: none;
  height: 22px;
`;

const SectionTitle = styled.h3`
  font-size: 11px;
  font-weight: 600;
  margin: 0;
  color: ${({ theme }) => theme.mode === 'dark' ? '#bbbbbb' : '#6f6f6f'};
  letter-spacing: 0.5px;
`;

const AddButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 16px;
  line-height: 1;
  padding: 0;
  cursor: pointer;
  opacity: 0.6;

  &:hover {
    opacity: 1;
  }
`;

const OpenFolderButton = styled.button`
  background-color: ${({ theme }) => theme.mode === 'dark' ? '#2d2f3a' : theme.colors.secondary};
  border: none;
  color: ${({ theme }) => theme.colors.text.primary};
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  margin-top: 8px;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${({ theme }) => theme.mode === 'dark' ? '#3d3f4a' : theme.colors.border};
  }
`;

const FilesContainer = styled.div`
  flex: 1;
  overflow: auto;
  padding: 0;

  /* VS Code-like scrollbar */
  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(121, 121, 121, 0.4);
    border-radius: 5px;
    border: 2px solid transparent;
    background-clip: content-box;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 100, 100, 0.7);
    border: 2px solid transparent;
    background-clip: content-box;
  }
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${({ theme }) => theme.colors.text.secondary};
  text-align: center;
  padding: 20px 0;
`;

const EmptyIcon = styled.div`
  opacity: 0.4;
  margin-bottom: 16px;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const EmptyText = styled.p`
  margin: 0 0 4px;
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const EmptySubtext = styled.p`
  margin: 0;
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
  opacity: 0.7;
`;

const LoadingIndicator = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 13px;
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.error};
  padding: 8px;
  font-size: 14px;
  margin: 8px 16px;
  border-radius: 4px;
  background-color: rgba(244, 67, 54, 0.15);
`;

const StatusBar = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 0 8px;
  font-size: 11px;
  color: ${({ theme }) => theme.mode === 'dark' ? '#cccccc' : '#616161'};
  background-color: ${({ theme }) => theme.mode === 'dark' ? '#252526' : '#f3f3f3'};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  height: 22px;
  align-items: center;
`;

const StatusText = styled.div`
  display: flex;
  align-items: center;
`;

const FileCount = styled.div`
  opacity: 0.7;
`;