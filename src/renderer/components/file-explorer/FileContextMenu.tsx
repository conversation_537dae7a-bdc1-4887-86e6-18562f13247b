// src/renderer/components/file-explorer/FileContextMenu.tsx
import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';

interface FileInfo {
  name: string;
  path: string;
  type: 'file' | 'directory';
  gitStatus?: 'modified' | 'staged' | 'untracked';
}

interface FileContextMenuProps {
  x: number;
  y: number;
  isVisible: boolean;
  isDirectory: boolean;
  onCreateFile: () => void;
  onCreateDirectory: () => void;
  onDelete: () => void;
  onRename: () => void;
  onClose: () => void;
  isGitRepo: boolean;
  item: FileInfo | null;
  rootPath: string;
  onStage: () => void;
  onUnstage: () => void;
  onRefresh: () => void;
}

export const FileContextMenu: React.FC<FileContextMenuProps> = ({
  x,
  y,
  isVisible,
  isDirectory,
  onCreateFile,
  onCreateDirectory,
  onDelete,
  onRename,
  onClose,
  isGitRepo,
  item,
  rootPath,
  onStage,
  onUnstage,
  onRefresh
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  // If not visible, don't render
  if (!isVisible) return null;

  // Handle menu item click safely
  const handleItemClick = (action: () => void) => (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // First close the menu
    onClose();

    // Then perform the action after a short delay
    setTimeout(() => {
      if (action) action();
    }, 50);
  };

  // Check if we can show Git options
  const canShowGitOptions = isGitRepo && item && !isDirectory;

  // Determine if we should show stage or unstage
  const showStage = canShowGitOptions && (!item.gitStatus || item.gitStatus === 'modified' || item.gitStatus === 'untracked');
  const showUnstage = canShowGitOptions && item.gitStatus === 'staged';

  return (
    <MenuContainer
      ref={menuRef}
      style={{ top: y, left: x }}
      className="context-menu"
      onClick={e => e.stopPropagation()}
    >
      {isDirectory && (
        <>
          <MenuItem onClick={handleItemClick(onCreateFile)}>New File</MenuItem>
          <MenuItem onClick={handleItemClick(onCreateDirectory)}>New Folder</MenuItem>
          <Divider />
        </>
      )}
      <MenuItem onClick={handleItemClick(onRename)}>Rename</MenuItem>
      <MenuItem onClick={handleItemClick(onDelete)} className="delete">Delete</MenuItem>

      {canShowGitOptions && <Divider />}

      {showStage && (
        <MenuItem onClick={handleItemClick(onStage)}>
          <GitIcon>+</GitIcon> Stage Changes
        </MenuItem>
      )}

      {showUnstage && (
        <MenuItem onClick={handleItemClick(onUnstage)}>
          <GitIcon>-</GitIcon> Unstage Changes
        </MenuItem>
      )}
    </MenuContainer>
  );
};

const MenuContainer = styled.div`
  position: fixed;
  background-color: ${({ theme }) => theme.colors.foreground};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  min-width: 150px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  overflow: hidden;
`;

const MenuItem = styled.div`
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text.primary};

  &:hover {
    background-color: ${({ theme }) => theme.colors.secondary};
  }

  &.delete {
    color: ${({ theme }) => theme.colors.error};
  }
`;

const Divider = styled.div`
  height: 1px;
  background-color: ${({ theme }) => theme.colors.border};
  margin: 4px 0;
`;

const GitIcon = styled.span`
  display: inline-block;
  width: 16px;
  text-align: center;
  margin-right: 4px;
  font-weight: bold;
`;