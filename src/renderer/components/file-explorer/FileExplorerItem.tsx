// src/renderer/components/file-explorer/FileExplorerItem.tsx
import React, { useState, useRef, useEffect, useContext } from 'react';
import styled from 'styled-components';
import { FileContextMenu } from './FileContextMenu';
import { ContextMenuContext } from '../../App';
import { gitService } from '../../services/GitService';

interface FileInfo {
  name: string;
  path: string;
  type: 'file' | 'directory';
  gitStatus?: 'modified' | 'staged' | 'untracked';
  size?: number;
  modifiedAt?: Date;
}

interface FileExplorerItemProps {
  item: FileInfo;
  level: number;
  onFileSelect: (item: FileInfo) => void;
  selectedPath: string | null;
  onRefresh: () => void;
  isGitRepo: boolean;
  rootPath: string;
}

interface FileTypeInfo {
  color: string;
  label: string;
}

const getFileTypeInfo = (fileName: string): FileTypeInfo => {
  const extension = fileName.split('.').pop()?.toLowerCase() || '';

  // Map common extensions to their types and colors
  const fileTypeMap: Record<string, FileTypeInfo> = {
    // JavaScript and related
    js: { color: '#f7df1e', label: 'JS' },
    jsx: { color: '#61dafb', label: 'JSX' },
    ts: { color: '#3178c6', label: 'TS' },
    tsx: { color: '#3178c6', label: 'TSX' },

    // Web files
    html: { color: '#e34c26', label: 'HTML' },
    css: { color: '#264de4', label: 'CSS' },
    scss: { color: '#c6538c', label: 'SCSS' },
    less: { color: '#1d365d', label: 'LESS' },
    svg: { color: '#ff9900', label: 'SVG' },

    // Configuration
    json: { color: '#f5871f', label: 'O' },
    yaml: { color: '#6a3396', label: 'YML' },
    yml: { color: '#6a3396', label: 'YML' },
    xml: { color: '#f05032', label: 'XML' },
    toml: { color: '#6c8ebe', label: 'TOML' },

    // Backend languages
    php: { color: '#777bb4', label: 'PHP' },
    py: { color: '#3776ab', label: 'PY' },
    rb: { color: '#cc342d', label: 'RB' },
    go: { color: '#00add8', label: 'GO' },
    java: { color: '#b07219', label: 'JV' },
    cs: { color: '#178600', label: 'C#' },
    cpp: { color: '#f34b7d', label: 'C++' },
    c: { color: '#555555', label: 'C' },
    rs: { color: '#dea584', label: 'RS' },

    // Mobile
    swift: { color: '#ffac45', label: 'SWF' },
    kt: { color: '#A97BFF', label: 'KT' },
    dart: { color: '#00B4AB', label: 'DRT' },

    // Documentation
    md: { color: '#083fa1', label: 'MD' },

    // GraphQL
    graphql: { color: '#e10098', label: 'GQL' },
    gql: { color: '#e10098', label: 'GQL' },
  };

  return fileTypeMap[extension] || { color: '#8d8d8d', label: extension.toUpperCase().substring(0, 3) || '?' };
};

export const FileExplorerItem: React.FC<FileExplorerItemProps> = ({
  item,
  level,
  onFileSelect,
  selectedPath,
  onRefresh,
  isGitRepo,
  rootPath
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [children, setChildren] = useState<FileInfo[]>([]);
  const [contextMenu, setContextMenu] = useState<{ x: number; y: number; visible: boolean }>({
    x: 0,
    y: 0,
    visible: false
  });
  const itemRef = useRef<HTMLDivElement>(null);
  const { closeAllMenus } = useContext(ContextMenuContext);

  // Load children when expanding a directory
  const loadChildren = async () => {
    try {
      const response = await window.electron.ipcRenderer.invoke('fs:readDirectory', { dirPath: item.path });
      if (response && response.success && response.files) {
        setChildren(response.files);
      }
    } catch (error) {
      console.error('Error loading directory contents:', error);
    }
  };

  // Handle item click
  const handleItemClick = async () => {
    if (item.type === 'file') {
      onFileSelect(item);
    } else {
      const newExpandedState = !isExpanded;
      setIsExpanded(newExpandedState);

      if (newExpandedState && (!children || children.length === 0)) {
        await loadChildren();
      }
    }
  };

  // Handle right-click to open context menu
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Close any existing context menus first
    closeAllMenus();

    // Then open this one
    setTimeout(() => {
      setContextMenu({
        x: e.clientX,
        y: e.clientY,
        visible: true
      });
    }, 10);
  };

  // Close this context menu
  const closeContextMenu = () => {
    setContextMenu({ ...contextMenu, visible: false });
  };

  // Listen for global close events
  useEffect(() => {
    const handleCloseAllMenus = () => {
      setContextMenu(prev => ({ ...prev, visible: false }));
    };

    window.addEventListener('close-all-context-menus', handleCloseAllMenus);
    return () => {
      window.removeEventListener('close-all-context-menus', handleCloseAllMenus);
    };
  }, []);

  // Create a new file in this directory
  const handleCreateFile = async () => {
    const fileName = prompt('Enter file name:');
    if (!fileName) return;

    const filePath = window.electron.path.join(item.path, fileName);
    try {
      const response = await window.electron.ipcRenderer.invoke('fs:createFile', { filePath });
      if (response.success) {
        // Refresh the directory
        await loadChildren();
        if (!isExpanded) {
          setIsExpanded(true);
        }
      } else {
        alert(`Failed to create file: ${response.error}`);
      }
    } catch (error) {
      console.error('Error creating file:', error);
    }
  };

  // Create a new folder in this directory
  const handleCreateDirectory = async () => {
    const dirName = prompt('Enter folder name:');
    if (!dirName) return;

    const dirPath = window.electron.path.join(item.path, dirName);
    try {
      const response = await window.electron.ipcRenderer.invoke('fs:createDirectory', { dirPath });
      if (response.success) {
        // Refresh the directory
        await loadChildren();
        if (!isExpanded) {
          setIsExpanded(true);
        }
      } else {
        alert(`Failed to create directory: ${response.error}`);
      }
    } catch (error) {
      console.error('Error creating directory:', error);
    }
  };

  // Delete this file or directory
  const handleDelete = async () => {
    if (confirm(`Are you sure you want to delete ${item.name}?`)) {
      try {
        const response = await window.electron.ipcRenderer.invoke('fs:delete', { path: item.path });
        if (response.success) {
          // Notify parent to refresh
          onRefresh();
        } else {
          alert(`Failed to delete: ${response.error}`);
        }
      } catch (error) {
        console.error('Error deleting item:', error);
      }
    }
  };

  // Rename this file or directory
  const handleRename = async () => {
    const newName = prompt('Enter new name:', item.name);
    if (!newName || newName === item.name) return;

    const parentDir = window.electron.path.dirname(item.path);
    const newPath = window.electron.path.join(parentDir, newName);

    try {
      const response = await window.electron.ipcRenderer.invoke('fs:rename', { oldPath: item.path, newPath });
      if (response.success) {
        // Notify parent to refresh
        onRefresh();
      } else {
        alert(`Failed to rename: ${response.error}`);
      }
    } catch (error) {
      console.error('Error renaming item:', error);
    }
  };

  // Add handlers for Git operations
  const handleStageFile = async () => {
    try {
      const response = await gitService.stageFile(item.path, rootPath);
      if (response.success) {
        onRefresh();
      } else {
        console.error('Failed to stage file:', response.error);
      }
    } catch (error) {
      console.error('Error staging file:', error);
    }
  };

  const handleUnstageFile = async () => {
    try {
      const response = await gitService.unstageFile(item.path, rootPath);
      if (response.success) {
        onRefresh();
      } else {
        console.error('Failed to unstage file:', response.error);
      }
    } catch (error) {
      console.error('Error unstaging file:', error);
    }
  };

  const isSelected = selectedPath === item.path;
  const fileTypeInfo = item.type === 'file' ? getFileTypeInfo(item.name) : { color: 'transparent', label: '' };
  const isExpandable = item.type === 'directory';

  return (
    <div>
      <ItemContainer
        ref={itemRef}
        level={level}
        isSelected={isSelected}
        onClick={handleItemClick}
        onContextMenu={handleContextMenu}
      >
        <ItemContent>
          {isExpandable ? (
            <FolderIcon isExpanded={isExpanded}>
              <ChevronIcon isExpanded={isExpanded}>
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </ChevronIcon>
              <FolderLabel>{item.name}</FolderLabel>
            </FolderIcon>
          ) : (
            <FileLabel>
              <FileType color={fileTypeInfo.color}>{fileTypeInfo.label}</FileType>
              <FileName>{item.name}</FileName>
            </FileLabel>
          )}
        </ItemContent>

        {isGitRepo && item.gitStatus && (
          <GitStatusIndicator status={item.gitStatus}>
            {item.gitStatus === 'modified' ? 'M' :
             item.gitStatus === 'staged' ? 'A' :
             item.gitStatus === 'untracked' ? '?' : ''}
          </GitStatusIndicator>
        )}
      </ItemContainer>

      {contextMenu.visible && (
        <FileContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          isVisible={contextMenu.visible}
          isDirectory={item.type === 'directory'}
          onCreateFile={handleCreateFile}
          onCreateDirectory={handleCreateDirectory}
          onDelete={handleDelete}
          onRename={handleRename}
          onClose={closeContextMenu}
          isGitRepo={isGitRepo}
          item={item}
          rootPath={rootPath}
          onStage={handleStageFile}
          onUnstage={handleUnstageFile}
          onRefresh={onRefresh}
        />
      )}

      {isExpanded && children && children.length > 0 && (
        <ChildrenContainer>
          {children.map((child) => (
            <FileExplorerItem
              key={child.path}
              item={child}
              level={level + 1}
              onFileSelect={onFileSelect}
              selectedPath={selectedPath}
              onRefresh={loadChildren}
              isGitRepo={isGitRepo}
              rootPath={rootPath}
            />
          ))}
        </ChildrenContainer>
      )}
    </div>
  );
};

const ItemContainer = styled.div<{ level: number; isSelected: boolean }>`
  display: flex;
  align-items: center;
  padding: 2px 8px;
  padding-left: ${({ level }) => `${level * 16}px`};
  cursor: pointer;
  height: 24px;
  background-color: ${({ isSelected }) =>
    isSelected ? 'rgba(55, 65, 81, 0.6)' : 'transparent'};
  color: ${({ isSelected, theme }) =>
    isSelected ? 'white' : theme.colors.text.primary};
  border-radius: 0;
  margin: 0;
  position: relative;

  &:hover {
    background-color: ${({ isSelected }) =>
      isSelected ? 'rgba(55, 65, 81, 0.6)' : 'rgba(55, 65, 81, 0.3)'};
  }
`;

const ItemContent = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
`;

const FolderIcon = styled.div<{ isExpanded: boolean }>`
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: ${({ isExpanded }) => isExpanded ? '500' : 'normal'};
  font-size: 13px;
  width: 100%;
`;

const ChevronIcon = styled.div<{ isExpanded: boolean }>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 4px;
  color: ${({ theme }) => theme.colors.text.secondary};
  transform: ${({ isExpanded }) => isExpanded ? 'rotate(90deg)' : 'rotate(0)'};
  transition: transform 0.15s ease;
`;

const FolderLabel = styled.span`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const FileLabel = styled.div`
  display: flex;
  align-items: center;
  padding-left: 20px;
  font-size: 13px;
  width: 100%;
`;

const FileType = styled.span<{ color: string }>`
  display: inline-block;
  min-width: 30px;
  text-align: center;
  margin-right: 8px;
  font-size: 11px;
  color: ${({ color }) => color};
  font-weight: 500;
`;

const FileName = styled.span`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
`;

const ChildrenContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const GitStatusIndicator = styled.span<{ status: string }>`
  margin-left: auto;
  font-size: 10px;
  font-weight: bold;
  padding: 0 4px;
  border-radius: 3px;
  color: ${({ status, theme }) => {
    switch (status) {
      case 'modified':
        return theme.colors.text.primary;
      case 'staged':
        return theme.colors.success || '#28a745';
      case 'untracked':
        return theme.colors.error || '#dc3545';
      default:
        return theme.colors.text.secondary;
    }
  }};
  background-color: ${({ status }) => {
    switch (status) {
      case 'modified':
        return 'rgba(255, 171, 0, 0.2)';
      case 'staged':
        return 'rgba(40, 167, 69, 0.2)';
      case 'untracked':
        return 'rgba(220, 53, 69, 0.2)';
      default:
        return 'transparent';
    }
  }};
`;