// src/renderer/components/agent/AgentPanel.tsx

import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { AgentResponse } from '../../../agent/base/Agent';
import {
  MessageSquare, Settings, GitBranch, Bot,
  Send, Save, RefreshCw, RotateCcw, FileText, Zap
} from 'lucide-react';
import { agentService } from '../../services/AgentService';

interface AgentPanelProps {
  currentFilePath: string | null;
  fileContent: string;
  onAgentResponseApply: (response: AgentResponse) => void;
  theme?: 'light' | 'dark';
}

const AgentPanel: React.FC<AgentPanelProps> = ({
  currentFilePath,
  fileContent,
  onAgentResponseApply,
  theme = 'dark'
}) => {
  const [query, setQuery] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [response, setResponse] = useState<AgentResponse | null>(null);
  const [selectedAgent, setSelectedAgent] = useState<string>('micromanager');
  const [availableAgents, setAvailableAgents] = useState<Array<{
    id: string;
    name: string;
    description: string;
    capabilities: string[];
  }>>([]);
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({
    openai: '',
    anthropic: ''
  });
  const [provider, setProvider] = useState<string>('openai');
  const [models, setModels] = useState<Record<string, string[]>>({
    // OpenAI models - updated with latest models
    openai: [
      'gpt-4o-2024-05-13',    // Latest GPT-4o model
      'gpt-4o',               // Multimodal model
      'gpt-4-turbo',          // Turbo model
      'o1-preview',           // Experimental reasoning model
      'o1-mini',              // Smaller reasoning model
      'gpt-4-0613',
      'gpt-4-1106-preview',
      'gpt-4-vision-preview',
      'gpt-3.5-turbo',
      'gpt-3.5-turbo-0125',
      'gpt-3.5-turbo-16k',
    ],

    // Anthropic models - updated with latest Claude models
    anthropic: [
      'claude-3-7-sonnet-20250219',    // Latest Claude model
      'claude-3-5-sonnet-20240620',    // Current 3.5 model
      'claude-3-5-haiku-20241022',     // Faster, smaller 3.5 model
      'claude-3-opus-20240229',        // Most powerful Claude 3
      'claude-3-sonnet-20240229',      // Balanced Claude 3
      'claude-3-haiku-20240307',       // Fastest Claude 3
      'claude-2.1',
      'claude-2.0',
      'claude-instant-1.2'
    ],

    // Google's Gemini models
    gemini: [
      'gemini-2.5-pro',            // Latest full Gemini model
      'gemini-2.5-flash',          // Faster Gemini 2.5 variant
      'gemini-2.5-flash-lite',     // Smallest Gemini 2.5 variant
      'gemini-2.0-pro',
      'gemini-2.0-flash',
      'gemini-2.0-flash-lite',
      'gemini-1.5-pro',
      'gemini-1.5-flash',
      'gemini-1.0-pro',
      'gemini-1.0-pro-vision'
    ],

    // DeepSeek models - new addition
    deepseek: [
      'deepseek-r1',               // Latest reasoning model
      'deepseek-v3',               // 671B MoE model
      'deepseek-coder-v2',
      'deepseek-r1-distill-llama-70b',
      'deepseek-r1-distill-qwen-32b'
    ],

    // Meta's Llama models - new addition
    meta: [
      'llama-3.3-70b-instruct',
      'llama-3.1-405b-instruct',   // Largest model
      'llama-3.1-70b-instruct',
      'llama-3.1-8b-instruct'
    ],

    // Mistral AI models - new addition
    mistral: [
      'mistral-small-3.1-2503',    // Latest model
      'mistral-small-3',
      'mistral-large-latest',
      'mistral-medium'
    ],

    // OpenRouter - meta-platform with access to many models
    openrouter: [
      'openai/gpt-4o',
      'openai/o1-preview',
      'anthropic/claude-3-7-sonnet',
      'google/gemini-2.5-pro',
      'meta-llama/llama-3-70b-instruct',
      'deepseek-ai/deepseek-r1',
      'mistralai/mistral-large',
      'anthropic/claude-3-opus',
      'databricks/dbrx-instruct',
      'phind/phind-34b'
    ],

    // Ollama - local models platform - new addition
    ollama: [
      'llama3.3-70b',
      'llama3.1-405b',
      'mistral-small-3.1',
      'phi-4',
      'gemma-3',
      'deepseek-r1',
      'codellama',
      'llava',
      'qwen3'
    ]
  });
  const [selectedModel, setSelectedModel] = useState<string>('gpt-4o');
  const [isConfigVisible, setIsConfigVisible] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<'chat' | 'diff' | 'settings'>('chat');
  const [chatHistory, setChatHistory] = useState<Array<{
    id: string;
    role: 'user' | 'agent';
    content: string;
    timestamp: Date;
    agentId?: string;
  }>>([]);
  const [diffView, setDiffView] = useState<{ original: string; modified: string }>({
    original: '',
    modified: ''
  });

  const chatContainerRef = useRef<HTMLDivElement>(null);
  const queryInputRef = useRef<HTMLTextAreaElement>(null);

  // Fetch available agents and load saved settings when component mounts
  useEffect(() => {
    // Load saved provider
    const savedProvider = localStorage.getItem('defaultProvider');
    if (savedProvider) {
      setProvider(savedProvider);
    }

    // Load saved API keys
    const savedApiKeys: Record<string, string> = {};
    ['openai', 'anthropic', 'gemini', 'deepseek', 'meta', 'mistral', 'openrouter', 'ollama'].forEach(provider => {
      const key = localStorage.getItem(`apiKey_${provider}`);
      if (key) {
        savedApiKeys[provider] = key;
      }
    });

    if (Object.keys(savedApiKeys).length > 0) {
      setApiKeys(savedApiKeys);
    }

    // Load saved model settings
    const savedModels = localStorage.getItem('selectedModels');
    if (savedModels) {
      try {
        const parsedModels = JSON.parse(savedModels);
        // Update selected model for current provider
        if (parsedModels[provider]) {
          setSelectedModel(parsedModels[provider]);
        }
      } catch (e) {
        console.error('Failed to parse saved models:', e);
      }
    }

    // Fetch available agents
    const fetchAgents = async () => {
      const agents = await agentService.getAgents();
      setAvailableAgents(agents);
    };

    fetchAgents();

    // Add a welcome message to chat history if it's empty
    if (chatHistory.length === 0) {
      setChatHistory([
        {
          id: 'welcome',
          role: 'agent',
          content: 'Welcome to Middlware Agent System. How can I assist you with your code today?',
          timestamp: new Date(),
          agentId: 'micromanager'
        }
      ]);
    }

    // Initialize the agent system if we have API keys
    if (Object.keys(savedApiKeys).length > 0) {
      handleInitialize();
    }
  }, []);

  // Update chat history when project changes
  useEffect(() => {
    const projectPath = currentFilePath ? window.electron.path.dirname(currentFilePath) : '';
    const history = agentService.getChatHistory(projectPath);

    if (history.length > 0) {
      setChatHistory(history);
    } else if (chatHistory.length <= 1) {
      // Only reset if we just have the welcome message
      setChatHistory([
        {
          id: 'welcome',
          role: 'agent',
          content: `Welcome to Middlware Agent System. I'm ready to help with ${currentFilePath ? window.electron.path.basename(currentFilePath) : 'your code'}.`,
          timestamp: new Date(),
          agentId: 'micromanager'
        }
      ]);
    }
  }, [currentFilePath]);

  // Scroll to bottom of chat when new messages arrive
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatHistory]);

  // Initialize agent system with API keys for different providers
  const handleInitialize = async () => {
    setIsProcessing(true);
    try {
      // Create a model config object with just the current provider's model
      const modelConfig = { [provider]: selectedModel };

      // Initialize with current keys and selected model
      const success = await agentService.initialize(apiKeys, modelConfig);

      if (success) {
        setIsConfigVisible(false);

        // Add success message
        setChatHistory(prev => [
          ...prev,
          {
            id: Math.random().toString(),
            role: 'agent',
            content: `Agent system initialized successfully with ${provider} using ${selectedModel}. Ready to assist with your code.`,
            timestamp: new Date(),
            agentId: 'micromanager'
          }
        ]);

        // Load available agents
        const agents = agentService.getAvailableAgents();
        setAvailableAgents(agents);
      } else {
        setChatHistory(prev => [
          ...prev,
          {
            id: Math.random().toString(),
            role: 'agent',
            content: `Failed to initialize with ${provider}. Please check your API key and try again.`,
            timestamp: new Date(),
            agentId: 'micromanager'
          }
        ]);
      }
    } catch (error) {
      setChatHistory(prev => [
        ...prev,
        {
          id: Math.random().toString(),
          role: 'agent',
          content: `Error initializing agent system: ${error instanceof Error ? error.message : String(error)}`,
          timestamp: new Date(),
          agentId: 'micromanager'
        }
      ]);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle query submission
  const handleSubmit = async () => {
    if (!query.trim() || isProcessing) return;

    // Add user query to chat history
    setChatHistory(prev => [
      ...prev,
      {
        id: Math.random().toString(),
        role: 'user',
        content: query,
        timestamp: new Date()
      }
    ]);

    setIsProcessing(true);

    try {
      // Ensure agent system is initialized
      if (!agentService.isInitialized()) {
        await handleInitialize();
      }

      // Process the query
      const agentResponse = await agentService.processQuery({
        query,
        fileContent: fileContent,
        currentFile: currentFilePath || '',
        projectPath: currentFilePath ? window.electron.path.dirname(currentFilePath) : '',
        provider: provider,
        model: selectedModel
      });

      // Store response for potential application
      setResponse(agentResponse);

      // If successful response has modified content, update diff view
      if (agentResponse.success && agentResponse.result && currentFilePath) {
        setDiffView({
          original: fileContent,
          modified: agentResponse.result
        });

        // Automatically switch to diff view if we have code changes
        setActiveTab('diff');
      }

      // Get updated chat history
      const projectPath = currentFilePath ? window.electron.path.dirname(currentFilePath) : '';
      const updatedHistory = agentService.getChatHistory(projectPath);
      setChatHistory(updatedHistory);
    } catch (error) {
      console.error('Error processing query:', error);
      setChatHistory(prev => [
        ...prev,
        {
          id: Math.random().toString(),
          role: 'agent',
          content: `Error processing query: ${error instanceof Error ? error.message : String(error)}`,
          timestamp: new Date(),
          agentId: 'micromanager'
        }
      ]);
    } finally {
      setIsProcessing(false);
      setQuery('');
    }
  };

  // Apply agent response to current file
  const handleApplyChanges = () => {
    if (!response || !response.success || !diffView.modified) return;

    onAgentResponseApply({
      success: true,
      result: diffView.modified,
      changes: response.changes
    });

    setChatHistory(prev => [
      ...prev,
      {
        id: Math.random().toString(),
        role: 'agent',
        content: 'Changes applied successfully.',
        timestamp: new Date(),
        agentId: 'micromanager'
      }
    ]);

    // Reset states
    setDiffView({ original: '', modified: '' });
    setResponse(null);
    setActiveTab('chat');
  };

  // Handle multiple file outputs
  const handleMultiFileOutput = async () => {
    if (!response || !response.success || !response.files || response.files.length === 0) return;

    try {
      for (const file of response.files) {
        if (!file.path || !file.content) continue;

        // If path is relative, make it absolute based on current file
        let absolutePath = file.path;
        if (!absolutePath.startsWith('/') && currentFilePath) {
          const projectPath = window.electron.path.dirname(currentFilePath);
          absolutePath = window.electron.path.join(projectPath, file.path);
        }

        // Write the file
        const result = await window.electron.ipcRenderer.invoke('fs:writeFile', {
          filePath: absolutePath,
          content: file.content
        });

        if (!result.success) {
          setChatHistory(prev => [
            ...prev,
            {
              id: Math.random().toString(),
              role: 'agent',
              content: `Failed to write file ${file.path}: ${result.error}`,
              timestamp: new Date(),
              agentId: 'micromanager'
            }
          ]);
        }
      }

      setChatHistory(prev => [
        ...prev,
        {
          id: Math.random().toString(),
          role: 'agent',
          content: `Successfully created/updated ${response.files?.length || 0} files.`,
          timestamp: new Date(),
          agentId: 'micromanager'
        }
      ]);
    } catch (error) {
      console.error('Error writing multiple files:', error);
      setChatHistory(prev => [
        ...prev,
        {
          id: Math.random().toString(),
          role: 'agent',
          content: `Error writing files: ${error instanceof Error ? error.message : String(error)}`,
          timestamp: new Date(),
          agentId: 'micromanager'
        }
      ]);
    }
  };

  // Handle Enter key in the textarea
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  // Format timestamps
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Get agent name from id
  const getAgentName = (agentId?: string): string => {
    if (!agentId) return 'Agent';
    const agent = availableAgents.find(a => a.id === agentId);
    return agent ? agent.name : 'Agent';
  };

  // Get agent color from id
  const getAgentColor = (agentId?: string): string => {
    if (!agentId) return '#0097fb';

    const colorMap: Record<string, string> = {
      'micromanager': '#0097fb', // Blue
      'intern': '#28a745',      // Green
      'junior': '#17a2b8',      // Teal
      'mid-level': '#fd7e14',   // Orange
      'senior': '#dc3545',      // Red
      'researcher': '#6f42c1',  // Purple
      'architect': '#e83e8c'    // Pink
    };

    return colorMap[agentId] || '#0097fb';
  };

  // Parse message content for code blocks
  const parseMessageWithCodeBlocks = (message: string) => {
    const codeBlockRegex = /```([\w-]+)?\s*\n([\s\S]*?)```/g;
    const parts: Array<{ type: 'text' | 'code'; content: string; language?: string }> = [];

    let lastIndex = 0;
    let match;

    while ((match = codeBlockRegex.exec(message)) !== null) {
      // Add text before code block
      if (match.index > lastIndex) {
        parts.push({
          type: 'text',
          content: message.slice(lastIndex, match.index)
        });
      }

      // Add code block
      parts.push({
        type: 'code',
        language: match[1] || 'typescript',
        content: match[2]
      });

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < message.length) {
      parts.push({
        type: 'text',
        content: message.slice(lastIndex)
      });
    }

    return parts;
  };

  // Render message content
  const MessageContent = ({ content }: { content: string }) => {
    const parts = parseMessageWithCodeBlocks(content);

    return (
      <MessageContentWrapper>
        {parts.map((part, index) => {
          if (part.type === 'text') {
            return <TextPart key={index}>{part.content}</TextPart>;
          } else {
            return (
              <CodeBlockWrapper key={index}>
                <CodeBlockHeader>
                  <CodeBlockLanguage>{part.language}</CodeBlockLanguage>
                  <CopyButton onClick={() => navigator.clipboard.writeText(part.content)}>
                    Copy
                  </CopyButton>
                </CodeBlockHeader>
                <CodeBlock language={part.language || 'typescript'}>
                  {part.content}
                </CodeBlock>
              </CodeBlockWrapper>
            );
          }
        })}
      </MessageContentWrapper>
    );
  };

  return (
    <Container>
      <Header>
        <TabContainer>
          <Tab
            isActive={activeTab === 'chat'}
            onClick={() => setActiveTab('chat')}
          >
            <MessageSquare size={16} />
            <TabText>Chat</TabText>
          </Tab>
          <Tab
            isActive={activeTab === 'diff'}
            onClick={() => setActiveTab('diff')}
            disabled={!diffView.modified}
          >
            <GitBranch size={16} />
            <TabText>Diff View</TabText>
          </Tab>
          <Tab
            isActive={activeTab === 'settings'}
            onClick={() => setActiveTab('settings')}
          >
            <Settings size={16} />
            <TabText>Settings</TabText>
          </Tab>
        </TabContainer>
        <HeaderActions>
          <AgentSelector>
            <AgentSelectWrapper>
              <Bot size={16} />
              <Select
                value={selectedAgent}
                onChange={(e) => setSelectedAgent(e.target.value)}
              >
                {availableAgents.map((agent) => (
                  <option key={agent.id} value={agent.id}>
                    {agent.name}
                  </option>
                ))}
              </Select>
            </AgentSelectWrapper>
          </AgentSelector>
        </HeaderActions>
      </Header>

      {isConfigVisible && (
        <ConfigPanel>
          <ConfigTitle>Agent Configuration</ConfigTitle>
          <Label>Provider</Label>
          <Select
            value={provider}
            onChange={(e) => {
              const newProvider = e.target.value;
              setProvider(newProvider);
              // Set default model for the selected provider
              if (models[newProvider] && models[newProvider].length > 0) {
                setSelectedModel(models[newProvider][0]);
              }
            }}
            style={{ marginBottom: '12px' }}
          >
            <option value="openai">OpenAI</option>
            <option value="anthropic">Anthropic</option>
            <option value="gemini">Google Gemini</option>
            <option value="deepseek">DeepSeek</option>
            <option value="meta">Meta Llama</option>
            <option value="mistral">Mistral AI</option>
            <option value="openrouter">OpenRouter</option>
            <option value="ollama">Ollama (Local)</option>
          </Select>

          <Label>Model</Label>
          <Select
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            style={{ marginBottom: '12px' }}
          >
            {models[provider]?.map(model => (
              <option key={model} value={model}>{model}</option>
            ))}
          </Select>

          {provider === 'openai' && (
            <>
              <Label>OpenAI API Key</Label>
              <ApiKeyInput
                type="password"
                value={apiKeys.openai}
                onChange={(e) => setApiKeys({...apiKeys, openai: e.target.value})}
                placeholder="Enter your OpenAI API key"
              />
            </>
          )}

          {provider === 'anthropic' && (
            <>
              <Label>Anthropic API Key</Label>
              <ApiKeyInput
                type="password"
                value={apiKeys.anthropic}
                onChange={(e) => setApiKeys({...apiKeys, anthropic: e.target.value})}
                placeholder="Enter your Anthropic API key"
              />
            </>
          )}

          {provider === 'gemini' && (
            <>
              <Label>Google API Key</Label>
              <ApiKeyInput
                type="password"
                value={apiKeys.gemini || ''}
                onChange={(e) => setApiKeys({...apiKeys, gemini: e.target.value})}
                placeholder="Enter your Google API key"
              />
            </>
          )}

          {provider === 'deepseek' && (
            <>
              <Label>DeepSeek API Key</Label>
              <ApiKeyInput
                type="password"
                value={apiKeys.deepseek || ''}
                onChange={(e) => setApiKeys({...apiKeys, deepseek: e.target.value})}
                placeholder="Enter your DeepSeek API key"
              />
            </>
          )}

          {provider === 'meta' && (
            <>
              <Label>Meta Llama API Key</Label>
              <ApiKeyInput
                type="password"
                value={apiKeys.meta || ''}
                onChange={(e) => setApiKeys({...apiKeys, meta: e.target.value})}
                placeholder="Enter your Meta Llama API key"
              />
            </>
          )}

          {provider === 'mistral' && (
            <>
              <Label>Mistral AI API Key</Label>
              <ApiKeyInput
                type="password"
                value={apiKeys.mistral || ''}
                onChange={(e) => setApiKeys({...apiKeys, mistral: e.target.value})}
                placeholder="Enter your Mistral AI API key"
              />
            </>
          )}

          {provider === 'openrouter' && (
            <>
              <Label>OpenRouter API Key</Label>
              <ApiKeyInput
                type="password"
                value={apiKeys.openrouter || ''}
                onChange={(e) => setApiKeys({...apiKeys, openrouter: e.target.value})}
                placeholder="Enter your OpenRouter API key"
              />
            </>
          )}

          {provider === 'ollama' && (
            <>
              <Label>Ollama Server URL</Label>
              <ApiKeyInput
                type="text"
                value={apiKeys.ollama || 'http://localhost:11434'}
                onChange={(e) => setApiKeys({...apiKeys, ollama: e.target.value})}
                placeholder="Enter your Ollama server URL"
              />
            </>
          )}

          <ButtonGroup>
            <ConfigButton onClick={() => setIsConfigVisible(false)}>
              Cancel
            </ConfigButton>
            <SubmitButton
              onClick={handleInitialize}
              disabled={!apiKeys[provider] || apiKeys[provider].trim() === '' || isProcessing}
            >
              {isProcessing ? 'Initializing...' : 'Save & Initialize'}
            </SubmitButton>
          </ButtonGroup>
        </ConfigPanel>
      )}

      <ContentContainer>
        {activeTab === 'chat' && (
          <>
            <ChatContainer ref={chatContainerRef}>
              {chatHistory.map((message) => (
                <MessageWrapper key={message.id} isUser={message.role === 'user'}>
                  <Message isUser={message.role === 'user'}>
                    {message.role === 'agent' && (
                      <AgentBadge color={getAgentColor(message.agentId)}>
                        {getAgentName(message.agentId)}
                      </AgentBadge>
                    )}
                    <MessageContent content={message.content} />
                    <MessageTime>{formatTime(message.timestamp)}</MessageTime>
                  </Message>
                </MessageWrapper>
              ))}
              {isProcessing && (
                <MessageWrapper isUser={false}>
                  <Message isUser={false}>
                    <AgentBadge color={getAgentColor(selectedAgent)}>
                      {getAgentName(selectedAgent)}
                    </AgentBadge>
                    <TypingIndicator>
                      <Dot delay="0s" />
                      <Dot delay="0.2s" />
                      <Dot delay="0.4s" />
                    </TypingIndicator>
                  </Message>
                </MessageWrapper>
              )}
            </ChatContainer>

            <InputContainer>
              <QueryInput
                ref={queryInputRef}
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Ask a question or request assistance..."
                disabled={isProcessing}
                rows={2}
              />
              <SendButton onClick={handleSubmit} disabled={isProcessing || !query.trim()}>
                {isProcessing ? <RefreshCw size={16} className="rotating" /> : <Send size={16} />}
              </SendButton>
            </InputContainer>
          </>
        )}

        {activeTab === 'diff' && diffView.modified && (
          <DiffContainer>
            <DiffHeader>
              <DiffTitle>Code Changes</DiffTitle>
              <ApplyButton onClick={handleApplyChanges}>
                Apply Changes
              </ApplyButton>
            </DiffHeader>
            <DiffViewContainer>
              <DiffContent>
                <DiffSection>
                  <DiffSectionTitle>Original</DiffSectionTitle>
                  <DiffCode>{diffView.original}</DiffCode>
                </DiffSection>
                <DiffSection>
                  <DiffSectionTitle>Modified</DiffSectionTitle>
                  <DiffCode>{diffView.modified}</DiffCode>
                </DiffSection>
              </DiffContent>
            </DiffViewContainer>
          </DiffContainer>
        )}

        {activeTab === 'settings' && (
          <SettingsContainer>
            <SettingsSection>
              <SectionTitle>Agent Configuration</SectionTitle>
              <SettingsRow>
                <SettingsLabel>Provider</SettingsLabel>
                <Select
                  value={provider}
                  onChange={(e) => {
                    const newProvider = e.target.value;
                    setProvider(newProvider);
                    // Set default model for the selected provider
                    if (models[newProvider] && models[newProvider].length > 0) {
                      setSelectedModel(models[newProvider][0]);
                    }
                  }}
                >
                  <option value="openai">OpenAI</option>
                  <option value="anthropic">Anthropic</option>
                  <option value="gemini">Google Gemini</option>
                  <option value="deepseek">DeepSeek</option>
                  <option value="meta">Meta Llama</option>
                  <option value="mistral">Mistral AI</option>
                  <option value="openrouter">OpenRouter</option>
                  <option value="ollama">Ollama (Local)</option>
                </Select>
              </SettingsRow>
              <SettingsRow>
                <SettingsLabel>Model</SettingsLabel>
                <Select
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value)}
                >
                  {models[provider]?.map(model => (
                    <option key={model} value={model}>{model}</option>
                  ))}
                </Select>
              </SettingsRow>
              {provider === 'openai' && (
                <SettingsRow>
                  <SettingsLabel>OpenAI API Key</SettingsLabel>
                  <ApiKeyInput
                    type="password"
                    value={apiKeys.openai}
                    onChange={(e) => setApiKeys({...apiKeys, openai: e.target.value})}
                    placeholder="Enter your OpenAI API key"
                  />
                </SettingsRow>
              )}
              {provider === 'anthropic' && (
                <SettingsRow>
                  <SettingsLabel>Anthropic API Key</SettingsLabel>
                  <ApiKeyInput
                    type="password"
                    value={apiKeys.anthropic}
                    onChange={(e) => setApiKeys({...apiKeys, anthropic: e.target.value})}
                    placeholder="Enter your Anthropic API key"
                  />
                </SettingsRow>
              )}
              {provider === 'gemini' && (
                <SettingsRow>
                  <SettingsLabel>Google API Key</SettingsLabel>
                  <ApiKeyInput
                    type="password"
                    value={apiKeys.gemini || ''}
                    onChange={(e) => setApiKeys({...apiKeys, gemini: e.target.value})}
                    placeholder="Enter your Google API key"
                  />
                </SettingsRow>
              )}
              {provider === 'deepseek' && (
                <SettingsRow>
                  <SettingsLabel>DeepSeek API Key</SettingsLabel>
                  <ApiKeyInput
                    type="password"
                    value={apiKeys.deepseek || ''}
                    onChange={(e) => setApiKeys({...apiKeys, deepseek: e.target.value})}
                    placeholder="Enter your DeepSeek API key"
                  />
                </SettingsRow>
              )}
              {provider === 'meta' && (
                <SettingsRow>
                  <SettingsLabel>Meta Llama API Key</SettingsLabel>
                  <ApiKeyInput
                    type="password"
                    value={apiKeys.meta || ''}
                    onChange={(e) => setApiKeys({...apiKeys, meta: e.target.value})}
                    placeholder="Enter your Meta Llama API key"
                  />
                </SettingsRow>
              )}
              {provider === 'mistral' && (
                <SettingsRow>
                  <SettingsLabel>Mistral AI API Key</SettingsLabel>
                  <ApiKeyInput
                    type="password"
                    value={apiKeys.mistral || ''}
                    onChange={(e) => setApiKeys({...apiKeys, mistral: e.target.value})}
                    placeholder="Enter your Mistral AI API key"
                  />
                </SettingsRow>
              )}
              {provider === 'openrouter' && (
                <SettingsRow>
                  <SettingsLabel>OpenRouter API Key</SettingsLabel>
                  <ApiKeyInput
                    type="password"
                    value={apiKeys.openrouter || ''}
                    onChange={(e) => setApiKeys({...apiKeys, openrouter: e.target.value})}
                    placeholder="Enter your OpenRouter API key"
                  />
                </SettingsRow>
              )}
              {provider === 'ollama' && (
                <SettingsRow>
                  <SettingsLabel>Ollama Server URL</SettingsLabel>
                  <ApiKeyInput
                    type="text"
                    value={apiKeys.ollama || 'http://localhost:11434'}
                    onChange={(e) => setApiKeys({...apiKeys, ollama: e.target.value})}
                    placeholder="Enter your Ollama server URL"
                  />
                </SettingsRow>
              )}
              <SaveSettingsButton
                onClick={handleInitialize}
                disabled={!apiKeys[provider] || apiKeys[provider].trim() === '' || isProcessing}
              >
                {isProcessing ? 'Saving...' : 'Save API Settings'}
              </SaveSettingsButton>
            </SettingsSection>

            <SettingsSection>
              <SectionTitle>Agent Preferences</SectionTitle>
              <SettingsRow>
                <SettingsLabel>Default Agent</SettingsLabel>
                <Select
                  value={selectedAgent}
                  onChange={(e) => setSelectedAgent(e.target.value)}
                >
                  {availableAgents.map((agent) => (
                    <option key={agent.id} value={agent.id}>
                      {agent.name}
                    </option>
                  ))}
                </Select>
              </SettingsRow>
              <SettingsRow>
                <SettingsLabel>Temperature</SettingsLabel>
                <RangeInput
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  defaultValue="0.7"
                />
                <RangeValue>0.7</RangeValue>
              </SettingsRow>
            </SettingsSection>

            <SettingsSection>
              <SectionTitle>Agent Capabilities</SectionTitle>
              {availableAgents.map((agent) => (
                <AgentCapabilityRow key={agent.id}>
                  <AgentName>{agent.name}</AgentName>
                  <CapabilitiesList>
                    {agent.capabilities.map((capability, index) => (
                      <CapabilityTag key={index}>{capability}</CapabilityTag>
                    ))}
                  </CapabilitiesList>
                </AgentCapabilityRow>
              ))}
            </SettingsSection>

            <SettingsSection>
              <SectionTitle>Actions</SectionTitle>
              <ActionButton onClick={() => {
                const projectPath = currentFilePath ? window.electron.path.dirname(currentFilePath) : '';
                agentService.clearChatHistory(projectPath);
                setChatHistory([
                  {
                    id: 'welcome',
                    role: 'agent',
                    content: `Welcome to Middlware Agent System. I'm ready to help with ${currentFilePath ? window.electron.path.basename(currentFilePath) : 'your code'}.`,
                    timestamp: new Date(),
                    agentId: 'micromanager'
                  }
                ]);
              }}>
                <RotateCcw size={16} />
                <span>Clear Chat History</span>
              </ActionButton>
            </SettingsSection>
          </SettingsContainer>
        )}
      </ContentContainer>

      <Footer>
        {currentFilePath ? (
          <FooterText>
            Working with: {window.electron.path.basename(currentFilePath)}
          </FooterText>
        ) : (
          <FooterText>
            Open a file to work with the agent system
          </FooterText>
        )}
        <FooterActions>
          {response && response.success && response.files && response.files.length > 0 && (
            <MultiFileActionButton onClick={handleMultiFileOutput}>
              Create/Update {response.files.length} Files
            </MultiFileActionButton>
          )}
          {response && response.success && diffView.modified && (
            <ApplyButtonSmall onClick={handleApplyChanges}>
              Apply Changes
            </ApplyButtonSmall>
          )}
        </FooterActions>
      </Footer>
    </Container>
  );
};

// Styled Components
const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text.primary};
  border-left: 1px solid ${({ theme }) => theme.colors.border};
  overflow: visible;
  position: relative;
  z-index: 5;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  background-color: #252526;
  border-bottom: 1px solid #1e1e1e;
  height: 35px;
  min-height: 35px;
  width: 100%;
  overflow: hidden;
`;

const TabContainer = styled.div`
  display: flex;
  height: 100%;
  overflow-x: auto;
  scrollbar-width: none;
  max-width: calc(100% - 80px);

  &::-webkit-scrollbar {
    display: none;
  }
`;

const Tab = styled.div<{ isActive: boolean; disabled?: boolean }>`
  display: flex;
  align-items: center;
  padding: 0 8px;
  min-width: 120px;
  max-width: 200px;
  height: 35px;
  background-color: ${props => props.isActive ? '#1e1e1e' : '#2d2d2d'};
  color: ${props => props.isActive ? '#ffffff' : '#cccccc'};
  cursor: pointer;
  user-select: none;
  position: relative;
  border-right: 1px solid #252526;
  z-index: 1;
  white-space: nowrap;

  &:hover {
    background-color: ${props => props.isActive ? '#1e1e1e' : '#303030'};
  }

  ${props => props.isActive && `
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #007fd4;
    }
  `}
`;

const TabText = styled.span`
  font-size: 12px;
  margin-left: 6px;
`;

const HeaderActions = styled.div`
  display: flex;
  align-items: center;
  padding-right: 8px;
  flex-shrink: 0;
`;

const AgentSelector = styled.div`
  margin-right: 8px;
`;

const AgentSelectWrapper = styled.div`
  display: flex;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  padding: 0 6px;

  svg {
    color: ${({ theme }) => theme.colors.primary};
    margin-right: 8px;
  }
`;

const Select = styled.select`
  padding: 4px 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 12px;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const ConfigButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: ${({ theme }) => theme.colors.secondary};
  }
`;

const ContentContainer = styled.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
`;

const ChatContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 12px;
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 10px;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.3);
    border-radius: 20px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(155, 155, 155, 0.5);
  }
`;

const MessageWrapper = styled.div<{ isUser: boolean }>`
  display: flex;
  justify-content: ${({ isUser }) => isUser ? 'flex-end' : 'flex-start'};
  margin-bottom: 8px;
  max-width: 100%;
`;

const Message = styled.div<{ isUser: boolean }>`
  padding: 8px 12px;
  border-radius: 8px;
  background-color: ${({ isUser, theme }) =>
    isUser ? theme.colors.primary : theme.colors.secondary};
  color: ${({ isUser, theme }) =>
    isUser ? 'white' : theme.colors.text.primary};
  max-width: 85%;
  position: relative;
  word-break: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
`;

const MessageContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
`;

const TextPart = styled.div`
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
`;

const CodeBlockWrapper = styled.div`
  border-radius: 4px;
  overflow: hidden;
  margin: 4px 0;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

const CodeBlockHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background-color: ${({ theme }) => theme.mode === 'dark' ? '#2d2d2d' : '#e0e0e0'};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  font-size: 11px;
`;

const CodeBlockLanguage = styled.span`
  color: ${({ theme }) => theme.colors.text.secondary};
  text-transform: uppercase;
  font-size: 10px;
`;

const CopyButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  font-size: 10px;
  padding: 2px 4px;

  &:hover {
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const CodeBlock = styled.pre<{ language: string }>`
  margin: 0;
  padding: 12px;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  overflow: auto;
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  max-height: 300px;
`;

const MessageTime = styled.div`
  font-size: 10px;
  opacity: 0.7;
  text-align: right;
  margin-top: 4px;
`;

const AgentBadge = styled.div<{ color: string }>`
  display: flex;
  align-items: center;
  font-size: 11px;
  font-weight: 500;
  color: ${({ color }) => color};
  margin-bottom: 6px;

  &::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: ${({ color }) => color};
    margin-right: 6px;
    opacity: 0.8;
  }
`;

const TypingIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 0;
`;

const Dot = styled.div<{ delay: string }>`
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.text.secondary};
  animation: bounce 1.4s infinite ease-in-out;
  animation-delay: ${({ delay }) => delay};

  @keyframes bounce {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-6px);
    }
  }
`;

const InputContainer = styled.div`
  display: flex;
  padding: 12px;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.background};
`;

const QueryInput = styled.textarea`
  flex: 1;
  padding: 10px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 6px;
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  resize: none;
  font-size: 13px;
  font-family: inherit;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }

  &::placeholder {
    color: ${({ theme }) => theme.colors.text.secondary};
    opacity: 0.7;
  }
`;

const SendButton = styled.button`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:hover:not(:disabled) {
    opacity: 0.9;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .rotating {
    animation: rotate 1s linear infinite;
  }

  @keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const Footer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  height: 40px;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const FooterText = styled.div`
  font-size: 11px;
`;

const FooterActions = styled.div`
  display: flex;
  gap: 8px;
`;

const ApplyButtonSmall = styled.button`
  background-color: ${({ theme }) => theme.colors.success || '#28a745'};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  cursor: pointer;

  &:hover {
    opacity: 0.9;
  }
`;

const MultiFileActionButton = styled.button`
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  cursor: pointer;

  &:hover {
    opacity: 0.9;
  }
`;

const DiffContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const DiffHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const DiffTitle = styled.div`
  font-size: 13px;
  font-weight: 500;
`;

const ApplyButton = styled.button`
  background-color: ${({ theme }) => theme.colors.success || '#28a745'};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;

  &:hover {
    opacity: 0.9;
  }
`;

const DiffViewContainer = styled.div`
  flex: 1;
  overflow: auto;
`;

const DiffContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
`;

const DiffSection = styled.div`
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  overflow: hidden;
`;

const DiffSectionTitle = styled.div`
  padding: 8px 12px;
  background-color: ${({ theme }) => theme.colors.secondary};
  font-size: 12px;
  font-weight: 500;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const DiffCode = styled.pre`
  margin: 0;
  padding: 12px;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  overflow: auto;
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  max-height: 300px;
`;

const ConfigPanel = styled.div`
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.background};
`;

const ConfigTitle = styled.h3`
  font-size: 14px;
  margin: 0 0 8px 0;
  font-weight: 500;
`;

const Label = styled.label`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
  margin-bottom: 4px;
`;

const ApiKeyInput = styled.input`
  padding: 8px 10px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 13px;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
`;

const SubmitButton = styled.button`
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;

  &:hover:not(:disabled) {
    opacity: 0.9;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const SettingsContainer = styled.div`
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const SettingsSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const SectionTitle = styled.h3`
  font-size: 14px;
  margin: 0;
  padding-bottom: 4px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  font-weight: 500;
`;

const SettingsRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
`;

const SettingsLabel = styled.label`
  font-size: 13px;
  flex: 1;
`;

const RangeInput = styled.input`
  flex: 1;
  accent-color: ${({ theme }) => theme.colors.primary};
`;

const RangeValue = styled.span`
  width: 30px;
  text-align: right;
  font-size: 12px;
`;

const SaveSettingsButton = styled.button`
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  align-self: flex-end;
  margin-top: 8px;

  &:hover:not(:disabled) {
    opacity: 0.9;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const AgentCapabilityRow = styled.div`
  margin-bottom: 12px;
`;

const AgentName = styled.div`
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 4px;
`;

const CapabilitiesList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
`;

const CapabilityTag = styled.span`
  font-size: 11px;
  background-color: ${({ theme }) => theme.colors.secondary};
  color: ${({ theme }) => theme.colors.text.primary};
  padding: 2px 6px;
  border-radius: 4px;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  color: #cccccc;
  cursor: pointer;
  border-radius: 4px;
  z-index: 2;

  &:hover {
    background-color: #303030;
  }
`;

export default AgentPanel;