// src/renderer/components/editor/EditorPreferencesPanel.tsx
import React from 'react';
import styled from 'styled-components';
import { EditorPreferences } from '../../types/editor';

interface EditorPreferencesPanelProps {
  preferences: EditorPreferences;
  onPreferencesChange: (preferences: EditorPreferences) => void;
  isOpen: boolean;
  onClose: () => void;
}

export const EditorPreferencesPanel: React.FC<EditorPreferencesPanelProps> = ({
  preferences,
  onPreferencesChange,
  isOpen,
  onClose,
}) => {
  if (!isOpen) return null;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    let updatedValue: any = value;
    
    // Handle different input types
    if (type === 'number') {
      updatedValue = parseInt(value, 10);
    } else if (type === 'checkbox') {
      updatedValue = (e.target as HTMLInputElement).checked;
    }
    
    onPreferencesChange({
      ...preferences,
      [name]: updatedValue,
    });
  };

  return (
    <Overlay onClick={onClose}>
      <PanelContainer onClick={(e) => e.stopPropagation()}>
        <PanelHeader>
          <h2>Editor Preferences</h2>
          <CloseButton onClick={onClose}>×</CloseButton>
        </PanelHeader>
        
        <PanelContent>
          <PreferenceGroup>
            <PreferenceLabel>Font Size</PreferenceLabel>
            <Input
              type="number"
              name="fontSize"
              value={preferences.fontSize}
              onChange={handleChange}
              min="8"
              max="30"
            />
          </PreferenceGroup>
          
          <PreferenceGroup>
            <PreferenceLabel>Tab Size</PreferenceLabel>
            <Input
              type="number"
              name="tabSize"
              value={preferences.tabSize}
              onChange={handleChange}
              min="1"
              max="8"
            />
          </PreferenceGroup>
          
          <PreferenceGroup>
            <PreferenceLabel>Word Wrap</PreferenceLabel>
            <Select
              name="wordWrap"
              value={preferences.wordWrap}
              onChange={handleChange}
            >
              <option value="off">Off</option>
              <option value="on">On</option>
              <option value="wordWrapColumn">Column</option>
              <option value="bounded">Bounded</option>
            </Select>
          </PreferenceGroup>
          
          <PreferenceGroup>
            <PreferenceLabel>Line Numbers</PreferenceLabel>
            <Select
              name="lineNumbers"
              value={preferences.lineNumbers}
              onChange={handleChange}
            >
              <option value="on">On</option>
              <option value="off">Off</option>
              <option value="relative">Relative</option>
            </Select>
          </PreferenceGroup>
          
          <PreferenceGroup>
            <PreferenceLabel>Font Family</PreferenceLabel>
            <Input
              type="text"
              name="fontFamily"
              value={preferences.fontFamily}
              onChange={handleChange}
            />
          </PreferenceGroup>
          
          <PreferenceGroup>
            <CheckboxLabel>
              <Checkbox
                type="checkbox"
                name="minimap"
                checked={preferences.minimap}
                onChange={handleChange}
              />
              Show Minimap
            </CheckboxLabel>
          </PreferenceGroup>
          
          <PreferenceGroup>
            <CheckboxLabel>
              <Checkbox
                type="checkbox"
                name="formatOnSave"
                checked={preferences.formatOnSave}
                onChange={handleChange}
              />
              Format On Save
            </CheckboxLabel>
          </PreferenceGroup>
          
          <PreferenceGroup>
            <CheckboxLabel>
              <Checkbox
                type="checkbox"
                name="autoIndent"
                checked={preferences.autoIndent}
                onChange={handleChange}
              />
              Auto Indent
            </CheckboxLabel>
          </PreferenceGroup>
        </PanelContent>
        
        <PanelFooter>
          <Button onClick={onClose}>Close</Button>
        </PanelFooter>
      </PanelContainer>
    </Overlay>
  );
};

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const PanelContainer = styled.div`
  width: 400px;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
`;

const PanelHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.text.secondary};
  
  &:hover {
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const PanelContent = styled.div`
  padding: 16px;
  overflow-y: auto;
  flex: 1;
`;

const PanelFooter = styled.div`
  padding: 16px;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  display: flex;
  justify-content: flex-end;
`;

const PreferenceGroup = styled.div`
  margin-bottom: 16px;
`;

const PreferenceLabel = styled.label`
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const Input = styled.input`
  width: 100%;
  padding: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text.primary};
  cursor: pointer;
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const Button = styled.button`
  padding: 8px 16px;
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  
  &:hover {
    opacity: 0.9;
  }
`;