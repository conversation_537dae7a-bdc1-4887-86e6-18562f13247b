// src/renderer/components/editor/MonacoEditorComponent.tsx
import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import * as monaco from 'monaco-editor';
import { EditorPreferences } from '../../types/editor';
import { FindReplacePanel, FindOptions } from './FindReplacePanel';

interface MonacoEditorProps {
  value: string;
  language: string;
  onChange: (value: string) => void;
  theme?: 'vs-dark' | 'light';
  preferences: EditorPreferences;
}

// Configure monaco environment to handle workers
if (typeof self !== 'undefined') {
  self.MonacoEnvironment = {
    getWorkerUrl: function(_moduleId: string, _label: string) {
      return './editor.worker.js';
    }
  };
}

export const MonacoEditorComponent: React.FC<MonacoEditorProps> = ({
  value,
  language,
  onChange,
  theme = 'vs-dark',
  preferences
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null);
  const [isFindOpen, setIsFindOpen] = useState(false);

  // Register languages and file associations
  useEffect(() => {
    // Monaco has built-in support for many languages
    // Here we can register additional file extensions to language mappings
    monaco.languages.getLanguages().forEach(lang => {
      if (lang.extensions) {
        // The language is already registered with these extensions
        console.log(`Language ${lang.id} supports: ${lang.extensions.join(', ')}`);
      }
    });
  }, []);

  // Initialize editor
  useEffect(() => {
    if (!containerRef.current) return;

    // Set theme
    monaco.editor.setTheme(theme);

    // Create editor with options based on preferences
    const editor = monaco.editor.create(containerRef.current, {
      value,
      language,
      automaticLayout: true,
      minimap: { enabled: preferences.minimap },
      fontSize: preferences.fontSize,
      fontFamily: preferences.fontFamily,
      scrollBeyondLastLine: false,
      lineNumbers: preferences.lineNumbers,
      folding: true,
      tabSize: preferences.tabSize,
      wordWrap: preferences.wordWrap,
      autoIndent: preferences.autoIndent ? 'advanced' : 'none',
      // Disable features that cause issues
      quickSuggestions: false,
      suggestOnTriggerCharacters: false,
      inlayHints: { enabled: 'off' }, // Fix: changed from false to 'off'
      formatOnType: false,
      formatOnPaste: false
    });

    // Set up change handler
    editor.onDidChangeModelContent(() => {
      onChange(editor.getValue());
    });

    editorRef.current = editor;

    // Cleanup
    return () => {
      editor.dispose();
    };
  }, [containerRef, preferences]); // Include preferences in dependencies

  // Add keyboard shortcuts for find
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Open find panel with Ctrl+F or Cmd+F
      if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        setIsFindOpen(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // Update language if changed
  useEffect(() => {
    if (editorRef.current) {
      const model = editorRef.current.getModel();
      if (model) {
        monaco.editor.setModelLanguage(model, language);
      }
    }
  }, [language]);

  // Update theme if changed
  useEffect(() => {
    monaco.editor.setTheme(theme);
  }, [theme]);

  // Update value if changed externally
  useEffect(() => {
    if (editorRef.current) {
      const currentValue = editorRef.current.getValue();
      if (value !== currentValue) {
        editorRef.current.setValue(value);
      }
    }
  }, [value]);

  // Update editor options when preferences change
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.updateOptions({
        minimap: { enabled: preferences.minimap },
        fontSize: preferences.fontSize,
        fontFamily: preferences.fontFamily,
        lineNumbers: preferences.lineNumbers,
        tabSize: preferences.tabSize,
        wordWrap: preferences.wordWrap,
        autoIndent: preferences.autoIndent ? 'advanced' : 'none',
      });
    }
  }, [preferences]);

  // Handle find operation
  const handleFind = (searchTerm: string, options: FindOptions) => {
    if (!editorRef.current || !searchTerm) return;

    // Find all matches
    const findMatches = editorRef.current.getModel()?.findMatches(
      searchTerm,
      true, // Search from beginning
      options.regex,
      options.caseSensitive,
      options.wholeWord ? ' ' : null,
      true
    ) || [];

    if (findMatches.length > 0) {
      // Set selection to the first match
      const firstMatch = findMatches[0].range;
      editorRef.current.revealRangeInCenter(firstMatch);
      editorRef.current.setSelection(firstMatch);
    }
  };

  // Handle replace operation
  const handleReplace = (searchTerm: string, replaceWith: string, options: FindOptions) => {
    if (!editorRef.current || !searchTerm) return;

    const selection = editorRef.current.getSelection();
    if (!selection) return;

    const selectionText = editorRef.current.getModel()?.getValueInRange(selection);

    // Check if the current selection matches the search term
    if (selectionText) {
      let matches = false;

      if (options.regex) {
        try {
          const regex = new RegExp(searchTerm, options.caseSensitive ? '' : 'i');
          matches = regex.test(selectionText);
        } catch (e) {
          console.error('Invalid regex:', e);
        }
      } else if (options.caseSensitive) {
        matches = selectionText === searchTerm;
      } else {
        matches = selectionText.toLowerCase() === searchTerm.toLowerCase();
      }

      if (matches) {
        // Replace current selection
        const edit = { range: selection, text: replaceWith };
        editorRef.current.executeEdits('find-replace', [edit]);

        // Find next occurrence
        handleFind(searchTerm, options);
      } else {
        // If current selection doesn't match, just find the next occurrence
        handleFind(searchTerm, options);
      }
    }
  };

  // Handle replace all operation
  const handleReplaceAll = (searchTerm: string, replaceWith: string, options: FindOptions) => {
    if (!editorRef.current || !searchTerm) return;

    const model = editorRef.current.getModel();
    if (!model) return;

    // Find all matches
    const findMatches = model.findMatches(
      searchTerm,
      true,
      options.regex,
      options.caseSensitive,
      options.wholeWord ? ' ' : null,
      true
    );

    if (findMatches.length > 0) {
      // Create edits for all matches
      const edits = findMatches.map(match => ({
        range: match.range,
        text: replaceWith
      }));

      // Apply all edits at once
      editorRef.current.executeEdits('find-replace-all', edits);
    }
  };

  return (
    <EditorWrapper>
      <EditorContainer ref={containerRef} />
      <FindReplacePanel
        isOpen={isFindOpen}
        onClose={() => setIsFindOpen(false)}
        onFind={handleFind}
        onReplace={handleReplace}
        onReplaceAll={handleReplaceAll}
      />
      <FloatingButtonContainer>
        <FloatingButton onClick={() => setIsFindOpen(true)}>
          🔍
        </FloatingButton>
      </FloatingButtonContainer>
    </EditorWrapper>
  );
};

const EditorWrapper = styled.div`
  position: relative;
  height: 100%;
  width: 100%;
`;

const EditorContainer = styled.div`
  height: 100%;
  width: 100%;
  overflow: hidden;
`;

const FloatingButtonContainer = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
`;

const FloatingButton = styled.button`
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:hover {
    background-color: ${({ theme }) => theme.colors.secondary};
  }
`;