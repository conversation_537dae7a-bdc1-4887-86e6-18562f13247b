// src/renderer/components/editor/FindReplacePanel.tsx
import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';

interface FindReplacePanelProps {
  isOpen: boolean;
  onClose: () => void;
  onFind: (searchTerm: string, options: FindOptions) => void;
  onReplace: (searchTerm: string, replaceWith: string, options: FindOptions) => void;
  onReplaceAll: (searchTerm: string, replaceWith: string, options: FindOptions) => void;
}

export interface FindOptions {
  caseSensitive: boolean;
  wholeWord: boolean;
  regex: boolean;
}

export const FindReplacePanel: React.FC<FindReplacePanelProps> = ({
  isOpen,
  onClose,
  onFind,
  onReplace,
  onReplaceAll
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [replaceWith, setReplaceWith] = useState('');
  const [options, setOptions] = useState<FindOptions>({
    caseSensitive: false,
    wholeWord: false,
    regex: false
  });
  const [isReplaceVisible, setIsReplaceVisible] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Focus search input when the panel opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 50);
    }
  }, [isOpen]);

  // Handle option change
  const handleOptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setOptions({
      ...options,
      [name]: checked
    });
  };

  // Handle key events
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      onFind(searchTerm, options);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  };

  // If panel is closed, don't render
  if (!isOpen) return null;

  return (
    <FindContainer>
      <FindRow>
        <InputContainer>
          <SearchInput
            ref={searchInputRef}
            type="text"
            placeholder="Find"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={handleKeyDown}
          />
        </InputContainer>
        <ButtonGroup>
          <ActionButton 
            onClick={() => onFind(searchTerm, options)}
            disabled={!searchTerm}
          >
            Find
          </ActionButton>
          <ToggleButton 
            $active={isReplaceVisible}
            onClick={() => setIsReplaceVisible(!isReplaceVisible)}
          >
            Replace
          </ToggleButton>
          <CloseButton onClick={onClose}>×</CloseButton>
        </ButtonGroup>
      </FindRow>

      {isReplaceVisible && (
        <ReplaceRow>
          <InputContainer>
            <SearchInput
              type="text"
              placeholder="Replace with"
              value={replaceWith}
              onChange={(e) => setReplaceWith(e.target.value)}
              onKeyDown={handleKeyDown}
            />
          </InputContainer>
          <ButtonGroup>
            <ActionButton 
              onClick={() => onReplace(searchTerm, replaceWith, options)}
              disabled={!searchTerm}
            >
              Replace
            </ActionButton>
            <ActionButton 
              onClick={() => onReplaceAll(searchTerm, replaceWith, options)}
              disabled={!searchTerm}
            >
              Replace All
            </ActionButton>
          </ButtonGroup>
        </ReplaceRow>
      )}

      <OptionsRow>
        <OptionCheckbox>
          <Checkbox
            type="checkbox"
            name="caseSensitive"
            checked={options.caseSensitive}
            onChange={handleOptionChange}
            id="case-sensitive"
          />
          <label htmlFor="case-sensitive">Match Case</label>
        </OptionCheckbox>
        <OptionCheckbox>
          <Checkbox
            type="checkbox"
            name="wholeWord"
            checked={options.wholeWord}
            onChange={handleOptionChange}
            id="whole-word"
          />
          <label htmlFor="whole-word">Whole Word</label>
        </OptionCheckbox>
        <OptionCheckbox>
          <Checkbox
            type="checkbox"
            name="regex"
            checked={options.regex}
            onChange={handleOptionChange}
            id="use-regex"
          />
          <label htmlFor="use-regex">Use Regex</label>
        </OptionCheckbox>
      </OptionsRow>
    </FindContainer>
  );
};

const FindContainer = styled.div`
  position: absolute;
  right: 20px;
  top: 20px;
  background-color: ${({ theme }) => theme.colors.foreground};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  width: 400px;
  z-index: 100;
`;

const FindRow = styled.div`
  display: flex;
  padding: 8px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const ReplaceRow = styled.div`
  display: flex;
  padding: 8px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const InputContainer = styled.div`
  flex: 1;
  margin-right: 8px;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 6px 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  font-size: 14px;
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text.primary};
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 4px;
`;

const ActionButton = styled.button<{ disabled?: boolean }>`
  padding: 4px 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  background-color: ${({ theme, disabled }) => 
    disabled ? theme.colors.border : theme.colors.primary};
  color: white;
  cursor: ${({ disabled }) => disabled ? 'default' : 'pointer'};
  font-size: 12px;
  opacity: ${({ disabled }) => disabled ? 0.6 : 1};
  
  &:hover {
    opacity: ${({ disabled }) => disabled ? 0.6 : 0.9};
  }
`;

const ToggleButton = styled.button<{ $active: boolean }>`
  padding: 4px 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  background-color: ${({ theme, $active }) => 
    $active ? theme.colors.primary : 'transparent'};
  color: ${({ theme, $active }) => 
    $active ? 'white' : theme.colors.text.primary};
  cursor: pointer;
  font-size: 12px;
  
  &:hover {
    background-color: ${({ theme, $active }) => 
      $active ? theme.colors.primary : theme.colors.secondary};
    opacity: 0.9;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 18px;
  line-height: 1;
  padding: 2px 6px;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.text.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    color: ${({ theme }) => theme.colors.text.secondary};
  }
`;

const OptionsRow = styled.div`
  display: flex;
  padding: 8px;
  gap: 10px;
`;

const OptionCheckbox = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const Checkbox = styled.input`
  margin: 0;
`;