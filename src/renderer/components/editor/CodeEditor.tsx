// src/renderer/components/editor/CodeEditor.tsx
import React from 'react';
import Editor from '@monaco-editor/react';
import styled from 'styled-components';

interface CodeEditorProps {
  value: string;
  language: string;
  onChange: (value: string | undefined) => void;
  theme?: 'vs-dark' | 'light';
}

export const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  language,
  onChange,
  theme = 'vs-dark'
}) => {
  const handleEditorDidMount = (editor: any) => {
    // You can add additional configuration here if needed
    console.log("Monaco editor mounted successfully");
  };

  return (
    <EditorContainer>
      <Editor
        height="100%"
        width="100%"
        language={language}
        value={value}
        theme={theme}
        onChange={onChange}
        onMount={handleEditorDidMount}
        options={{
          fontSize: 14,
          minimap: { enabled: true },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          lineNumbers: 'on',
          fontFamily: 'Menlo, Monaco, "Courier New", monospace',
          folding: true,
        }}
      />
    </EditorContainer>
  );
};

const EditorContainer = styled.div`
  height: 100%;
  width: 100%;
  overflow: hidden;
`;