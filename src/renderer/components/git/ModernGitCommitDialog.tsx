// src/renderer/components/git/ModernGitCommitDialog.tsx
import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { gitService } from '../../services/GitService';

interface ModernGitCommitDialogProps {
  isOpen: boolean;
  onClose: () => void;
  rootPath: string;
  onSuccess: () => void;
}

export const ModernGitCommitDialog: React.FC<ModernGitCommitDialogProps> = ({
  isOpen,
  onClose,
  rootPath,
  onSuccess
}) => {
  const [commitMessage, setCommitMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isOpen && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isOpen]);

  const handleCommit = async () => {
    if (!commitMessage.trim()) {
      setError('Please enter a commit message');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await gitService.commitChanges(commitMessage, rootPath);
      if (response.success) {
        onSuccess();
        onClose();
        setCommitMessage('');
      } else {
        setError(response.error || 'Failed to commit changes');
      }
    } catch (error) {
      setError((error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <DialogOverlay onClick={onClose}>
      <DialogContainer onClick={e => e.stopPropagation()}>
        <DialogHeader>
          <DialogTitle>Commit Changes</DialogTitle>
          <CloseButton onClick={onClose}>×</CloseButton>
        </DialogHeader>

        <DialogContent>
          <Label htmlFor="commit-message">Message</Label>
          <MessageTextarea
            id="commit-message"
            ref={textareaRef}
            value={commitMessage}
            onChange={e => setCommitMessage(e.target.value)}
            placeholder="What changes did you make?"
            rows={4}
          />

          {error && <ErrorMessage>{error}</ErrorMessage>}
        </DialogContent>

        <DialogFooter>
          <CancelButton onClick={onClose} disabled={isLoading}>
            Cancel
          </CancelButton>
          <CommitButton 
            onClick={handleCommit} 
            disabled={isLoading || !commitMessage.trim()}
          >
            {isLoading ? 'Committing...' : 'Commit'}
          </CommitButton>
        </DialogFooter>
      </DialogContainer>
    </DialogOverlay>
  );
};

const DialogOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
`;

const DialogContainer = styled.div`
  width: 450px;
  background-color: ${({ theme }) => theme.mode === 'dark' ? '#1e1e2e' : theme.colors.background};
  border-radius: 8px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: fadeIn 0.2s ease-out;

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
  }
`;

const DialogHeader = styled.div`
  padding: 16px 20px;
  border-bottom: 1px solid ${({ theme }) => theme.mode === 'dark' ? '#2d2f3a' : theme.colors.border};
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const DialogTitle = styled.h2`
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 22px;
  line-height: 1;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  
  &:hover {
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const DialogContent = styled.div`
  padding: 20px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const MessageTextarea = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 1px solid ${({ theme }) => theme.mode === 'dark' ? '#2d2f3a' : theme.colors.border};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.mode === 'dark' ? '#1a1b26' : theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
    box-shadow: 0 0 0 2px ${({ theme }) => `${theme.colors.primary}33`};
  }
`;

const ErrorMessage = styled.div`
  margin-top: 12px;
  color: ${({ theme }) => theme.colors.error};
  font-size: 13px;
  background-color: ${({ theme }) => `${theme.colors.error}15`};
  padding: 8px 12px;
  border-radius: 4px;
`;

const DialogFooter = styled.div`
  padding: 16px 20px;
  border-top: 1px solid ${({ theme }) => theme.mode === 'dark' ? '#2d2f3a' : theme.colors.border};
  display: flex;
  justify-content: flex-end;
  gap: 12px;
`;

const Button = styled.button`
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const CancelButton = styled(Button)`
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.mode === 'dark' ? '#2d2f3a' : theme.colors.border};
  color: ${({ theme }) => theme.colors.text.primary};
  
  &:hover:not(:disabled) {
    background-color: ${({ theme }) => theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : theme.colors.secondary};
  }
`;

const CommitButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.primary};
  border: none;
  color: white;
  
  &:hover:not(:disabled) {
    opacity: 0.9;
  }
`;