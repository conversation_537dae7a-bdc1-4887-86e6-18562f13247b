// src/renderer/components/git/GitCommitDialog.tsx
import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { gitService } from '../../services/GitService';

interface GitCommitDialogProps {
  isOpen: boolean;
  onClose: () => void;
  rootPath: string;
  onSuccess: () => void;
}

export const GitCommitDialog: React.FC<GitCommitDialogProps> = ({
  isOpen,
  onClose,
  rootPath,
  onSuccess
}) => {
  const [commitMessage, setCommitMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isOpen && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isOpen]);

  const handleCommit = async () => {
    if (!commitMessage.trim()) {
      setError('Commit message cannot be empty');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await gitService.commitChanges(commitMessage, rootPath);
      if (response.success) {
        onSuccess();
        onClose();
      } else {
        setError(response.error || 'Failed to commit changes');
      }
    } catch (error) {
      setError((error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <Overlay onClick={onClose}>
      <DialogContainer onClick={e => e.stopPropagation()}>
        <DialogHeader>
          <Title>Commit Changes</Title>
          <CloseButton onClick={onClose}>×</CloseButton>
        </DialogHeader>

        <DialogContent>
          <Label htmlFor="commit-message">Commit Message:</Label>
          <MessageTextarea
            id="commit-message"
            ref={textareaRef}
            value={commitMessage}
            onChange={e => setCommitMessage(e.target.value)}
            placeholder="Enter a commit message..."
            rows={4}
          />

          {error && <ErrorMessage>{error}</ErrorMessage>}
        </DialogContent>

        <DialogFooter>
          <CancelButton onClick={onClose} disabled={isLoading}>
            Cancel
          </CancelButton>
          <CommitButton onClick={handleCommit} disabled={isLoading || !commitMessage.trim()}>
            {isLoading ? 'Committing...' : 'Commit'}
          </CommitButton>
        </DialogFooter>
      </DialogContainer>
    </Overlay>
  );
};

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const DialogContainer = styled.div`
  width: 500px;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
`;

const DialogHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Title = styled.h2`
  margin: 0;
  font-size: 18px;
  font-weight: 500;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.text.secondary};
  
  &:hover {
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const DialogContent = styled.div`
  padding: 16px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
`;

const MessageTextarea = styled.textarea`
  width: 100%;
  padding: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const ErrorMessage = styled.div`
  margin-top: 8px;
  color: ${({ theme }) => theme.colors.error};
  font-size: 14px;
`;

const DialogFooter = styled.div`
  padding: 16px;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  display: flex;
  justify-content: flex-end;
  gap: 8px;
`;

const Button = styled.button`
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const CancelButton = styled(Button)`
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.text.primary};
  
  &:hover:not(:disabled) {
    background-color: ${({ theme }) => theme.colors.secondary};
  }
`;

const CommitButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.primary};
  border: none;
  color: white;
  
  &:hover:not(:disabled) {
    opacity: 0.9;
  }
`;