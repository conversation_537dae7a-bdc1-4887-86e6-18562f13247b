// src/renderer/components/shortcuts/KeyboardShortcutsPanel.tsx
import React from 'react';
import styled from 'styled-components';
import { KeyboardShortcut, formatShortcut } from '../../types/shortcuts';

interface KeyboardShortcutsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  shortcuts: KeyboardShortcut[];
}

export const KeyboardShortcutsPanel: React.FC<KeyboardShortcutsPanelProps> = ({
  isOpen,
  onClose,
  shortcuts
}) => {
  if (!isOpen) return null;

  // Group shortcuts by category
  const shortcutsByCategory = shortcuts.reduce<Record<string, KeyboardShortcut[]>>(
    (acc, shortcut) => {
      if (!acc[shortcut.category]) {
        acc[shortcut.category] = [];
      }
      acc[shortcut.category].push(shortcut);
      return acc;
    },
    {}
  );

  // Get sorted categories
  const categories = Object.keys(shortcutsByCategory).sort();

  return (
    <Overlay onClick={onClose}>
      <PanelContainer onClick={e => e.stopPropagation()}>
        <PanelHeader>
          <h2>Keyboard Shortcuts</h2>
          <CloseButton onClick={onClose}>×</CloseButton>
        </PanelHeader>
        
        <PanelContent>
          {categories.map(category => (
            <CategorySection key={category}>
              <CategoryTitle>{category}</CategoryTitle>
              <ShortcutsList>
                {shortcutsByCategory[category].map(shortcut => (
                  <ShortcutItem key={shortcut.id}>
                    <ShortcutDescription>{shortcut.description}</ShortcutDescription>
                    <ShortcutKeys>
                      {formatShortcut(shortcut.keys)}
                    </ShortcutKeys>
                  </ShortcutItem>
                ))}
              </ShortcutsList>
            </CategorySection>
          ))}
        </PanelContent>
        
        <PanelFooter>
          <Button onClick={onClose}>Close</Button>
        </PanelFooter>
      </PanelContainer>
    </Overlay>
  );
};

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const PanelContainer = styled.div`
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
`;

const PanelHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.text.secondary};
  
  &:hover {
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const PanelContent = styled.div`
  padding: 16px;
  overflow-y: auto;
  flex: 1;
`;

const PanelFooter = styled.div`
  padding: 16px;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  display: flex;
  justify-content: flex-end;
`;

const CategorySection = styled.div`
  margin-bottom: 24px;
`;

const CategoryTitle = styled.h3`
  margin: 0 0 12px 0;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.primary};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  padding-bottom: 4px;
`;

const ShortcutsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const ShortcutItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
`;

const ShortcutDescription = styled.div`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const ShortcutKeys = styled.div`
  font-size: 13px;
  color: ${({ theme }) => theme.colors.text.secondary};
  background-color: ${({ theme }) => theme.colors.foreground};
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  font-family: monospace;
`;

const Button = styled.button`
  padding: 8px 16px;
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  
  &:hover {
    opacity: 0.9;
  }
`;