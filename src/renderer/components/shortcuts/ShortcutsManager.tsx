// src/renderer/components/shortcuts/ShortcutsManager.tsx
import { useEffect, useRef, useState } from 'react';
import { KeyboardShortcut } from '../../types/shortcuts';

interface ShortcutsManagerProps {
  shortcuts: KeyboardShortcut[];
  enabled: boolean;
}

export const ShortcutsManager: React.FC<ShortcutsManagerProps> = ({
  shortcuts,
  enabled
}) => {
  const shortcutsRef = useRef(shortcuts);
  const [pendingKeys, setPendingKeys] = useState<string[]>([]);
  const pendingTimeoutRef = useRef<number | null>(null);

  // Update ref when shortcuts change
  useEffect(() => {
    shortcutsRef.current = shortcuts;
  }, [shortcuts]);

  // Setup keyboard event handlers
  useEffect(() => {
    if (!enabled) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't handle shortcuts when user is typing in an input or textarea
      const target = event.target as HTMLElement;
      if (
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.isContentEditable
      ) {
        return;
      }

      // Add current key to pending keys for multi-key shortcuts
      const keyPressed = event.key.toLowerCase();
      let currentPendingKeys = [...pendingKeys];

      // Add modifier keys if pressed
      if (event.ctrlKey || event.metaKey) {
        if (!currentPendingKeys.includes('mod')) {
          currentPendingKeys.push('mod');
        }
      }

      if (event.shiftKey && !currentPendingKeys.includes('shift')) {
        currentPendingKeys.push('shift');
      }

      if (event.altKey && !currentPendingKeys.includes('alt')) {
        currentPendingKeys.push('alt');
      }

      // Add the actual key if it's not a modifier
      if (!['control', 'meta', 'shift', 'alt'].includes(keyPressed)) {
        currentPendingKeys.push(keyPressed);
      }

      // Update pending keys state
      setPendingKeys(currentPendingKeys);

      // Reset pending keys after a timeout (for multi-key sequences)
      if (pendingTimeoutRef.current) {
        window.clearTimeout(pendingTimeoutRef.current);
      }

      pendingTimeoutRef.current = window.setTimeout(() => {
        setPendingKeys([]);
        pendingTimeoutRef.current = null;
      }, 1000);

      // Check if the event matches any of our shortcuts
      for (const shortcut of shortcutsRef.current) {
        // Check if current pending keys match the shortcut
        const matchesShortcut = shortcut.keys.length === currentPendingKeys.length &&
          shortcut.keys.every(key => {
            const normalizedKey = key.toLowerCase();
            return currentPendingKeys.includes(normalizedKey);
          });

        if (matchesShortcut) {
          // Prevent default only if it's not already handled by Monaco
          if (!(target.closest('.monaco-editor') && shortcut.id === 'find-in-file')) {
            event.preventDefault();
          }

          // For close-file, explicitly prevent default to stop browser behavior
          if (shortcut.id === 'close-file') {
            event.preventDefault();
            event.stopPropagation();
          }

          shortcut.action();
          // Reset pending keys after successful match
          setPendingKeys([]);
          return;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      if (pendingTimeoutRef.current) {
        window.clearTimeout(pendingTimeoutRef.current);
      }
    };
  }, [enabled, pendingKeys]);

  return null; // This is a non-rendering component
};