// src/renderer/components/terminal/ModernTerminalTabs.tsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import ModernTerminal from './ModernTerminal';
import { terminalService, TerminalSession } from '../../services/TerminalService';

interface ModernTerminalTabsProps {
  className?: string;
  projectPath?: string;
}

const ModernTerminalTabs: React.FC<ModernTerminalTabsProps> = ({
  className,
  projectPath
}) => {
  const [sessions, setSessions] = useState<TerminalSession[]>([]);
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);
  const [isPanelMaximized, setIsPanelMaximized] = useState(false);

  // Initialize with a default terminal
  useEffect(() => {
    const initTerminal = async () => {
      try {
        const session = await terminalService.createTerminal(projectPath);
        setSessions([session]);
        setActiveSessionId(session.id);
      } catch (error) {
        console.error('Failed to create initial terminal session:', error);
      }
    };

    initTerminal();

    // Cleanup on unmount
    return () => {
      sessions.forEach(session => {
        terminalService.closeTerminal(session.id)
          .catch(err => console.error('Error closing terminal:', err));
      });
    };
  }, []);

  // Handle project path changes
  useEffect(() => {
    if (!projectPath || !activeSessionId) return;

    terminalService.changeDirectory(activeSessionId, projectPath)
      .catch(err => console.error('Error changing directory:', err));
  }, [projectPath, activeSessionId]);

  // Create a new terminal tab
  const handleNewTerminal = async () => {
    try {
      const newName = `Terminal ${sessions.length + 1}`;
      const session = await terminalService.createTerminal(projectPath, newName);
      setSessions(prev => [...prev, session]);
      setActiveSessionId(session.id);
    } catch (error) {
      console.error('Failed to create new terminal:', error);
    }
  };

  // Close a terminal tab
  const handleCloseTerminal = async (id: string, event: React.MouseEvent) => {
    event.stopPropagation();

    try {
      await terminalService.closeTerminal(id);

      // Update sessions list
      setSessions(prev => prev.filter(s => s.id !== id));

      // Update active session if necessary
      if (activeSessionId === id) {
        const remaining = sessions.filter(s => s.id !== id);
        if (remaining.length > 0) {
          setActiveSessionId(remaining[remaining.length - 1].id);
        } else {
          // Create a new session if we closed the last one
          const newSession = await terminalService.createTerminal(projectPath);
          setSessions([newSession]);
          setActiveSessionId(newSession.id);
        }
      }
    } catch (error) {
      console.error('Failed to close terminal:', error);
    }
  };

  // Toggle terminal panel maximize/restore
  const handleToggleMaximize = () => {
    setIsPanelMaximized(prev => !prev);
  };

  // Handle terminal input
  const handleTerminalData = (sessionId: string) => (data: string) => {
    terminalService.writeToTerminal(sessionId, data)
      .catch(err => console.error('Failed to write to terminal:', err));
  };

  return (
    <Container className={className} isMaximized={isPanelMaximized}>
      <TabBar>
        <TabsSection>
          {sessions.map(session => (
            <Tab
              key={session.id}
              isActive={session.id === activeSessionId}
              onClick={() => setActiveSessionId(session.id)}
            >
              <TabIcon>$</TabIcon>
              <TabName>{session.name}</TabName>
              <CloseTabButton onClick={(e) => handleCloseTerminal(session.id, e)}>
                <CloseIcon>×</CloseIcon>
              </CloseTabButton>
            </Tab>
          ))}
          <NewTabButton onClick={handleNewTerminal} title="New Terminal">
            <PlusIcon>+</PlusIcon>
          </NewTabButton>
        </TabsSection>

        <ActionsSection>
          <ActionButton
            onClick={handleToggleMaximize}
            title={isPanelMaximized ? "Restore Terminal" : "Maximize Terminal"}
          >
            {isPanelMaximized ? '⬇' : '⬆'}
          </ActionButton>
        </ActionsSection>
      </TabBar>

      <TerminalContent>
        {sessions.map(session => (
          <TerminalContainer
            key={session.id}
            isVisible={session.id === activeSessionId}
          >
            <ModernTerminal
              id={session.id}
              cwd={session.cwd}
              onData={handleTerminalData(session.id)}
            />
          </TerminalContainer>
        ))}
      </TerminalContent>
    </Container>
  );
};

interface ContainerProps {
  isMaximized: boolean;
}

const Container = styled.div<ContainerProps>`
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #1e1e1e;
  transition: height 0.2s ease-out;
  ${props => props.isMaximized ? `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
  ` : ''}
`;

const TabBar = styled.div`
  display: flex;
  justify-content: space-between;
  background-color: #252526;
  border-bottom: 1px solid #1e1e1e;
  height: 35px;
  min-height: 35px;
  width: 100%;
  overflow: hidden;
`;

const TabsSection = styled.div`
  display: flex;
  overflow-x: auto;
  scrollbar-width: none;
  max-width: calc(100% - 80px);

  &::-webkit-scrollbar {
    display: none;
  }
`;

const ActionsSection = styled.div`
  display: flex;
  align-items: center;
  padding-right: 8px;
  flex-shrink: 0;
`;

const Tab = styled.div<{ isActive: boolean }>`
  display: flex;
  align-items: center;
  padding: 0 8px;
  min-width: 120px;
  max-width: 200px;
  height: 35px;
  background-color: ${props => props.isActive ? '#1e1e1e' : '#2d2d2d'};
  color: ${props => props.isActive ? '#ffffff' : '#cccccc'};
  cursor: pointer;
  user-select: none;
  position: relative;
  border-right: 1px solid #252526;

  &:hover {
    background-color: ${props => props.isActive ? '#1e1e1e' : '#303030'};
  }

  ${props => props.isActive && `
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #007fd4;
    }
  `}
`;

const TabIcon = styled.span`
  margin-right: 6px;
  font-size: 14px;
  opacity: 0.8;
`;

const TabName = styled.span`
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 13px;
`;

const CloseTabButton = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-left: 4px;
  border-radius: 4px;
  opacity: 0.7;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 1;
  }
`;

const CloseIcon = styled.span`
  font-size: 16px;
`;

const NewTabButton = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 35px;
  color: #cccccc;
  cursor: pointer;

  &:hover {
    background-color: #303030;
  }
`;

const PlusIcon = styled.span`
  font-size: 18px;
`;

const ActionButton = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  color: #cccccc;
  cursor: pointer;
  border-radius: 4px;

  &:hover {
    background-color: #303030;
  }
`;

const TerminalContent = styled.div`
  flex: 1;
  position: relative;
  overflow: hidden;
`;

const TerminalContainer = styled.div<{ isVisible: boolean }>`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: ${props => props.isVisible ? 'flex' : 'none'};
  flex-direction: column;
`;

export default ModernTerminalTabs;