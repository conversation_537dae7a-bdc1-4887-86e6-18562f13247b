// src/renderer/components/terminal/ModernTerminal.tsx

import React, { useEffect, useRef, useState } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import styled from 'styled-components';
import '@xterm/xterm/css/xterm.css';

// Define the FitAddon type
interface IFitAddon {
  fit(): void;
  dispose(): void;
}

interface ModernTerminalProps {
  id: string;
  cwd?: string;
  onData: (data: string) => void;
  className?: string;
}

const ModernTerminal: React.FC<ModernTerminalProps> = ({
  id,
  cwd,
  onData,
  className
}) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const xtermRef = useRef<Terminal | null>(null);
  const fitAddonRef = useRef<IFitAddon | null>(null);
  const [isTerminalReady, setIsTerminalReady] = useState<boolean>(false);

  // Initialize terminal
  useEffect(() => {
    if (!terminalRef.current) return;

    // Create terminal instance
    const terminal = new Terminal({
      fontFamily: 'Menlo, Monaco, Consolas, "Courier New", monospace',
      fontSize: 14,
      lineHeight: 1.2,
      cursorBlink: true,
      theme: {
        background: '#1e1e1e',
        foreground: '#cccccc',
        black: '#000000',
        red: '#cd3131',
        green: '#0dbc79',
        yellow: '#e5e510',
        blue: '#2472c8',
        magenta: '#bc3fbc',
        cyan: '#11a8cd',
        white: '#e5e5e5',
        brightBlack: '#666666',
        brightRed: '#f14c4c',
        brightGreen: '#23d18b',
        brightYellow: '#f5f543',
        brightBlue: '#3b8eea',
        brightMagenta: '#d670d6',
        brightCyan: '#29b8db',
        brightWhite: '#e5e5e5'
      },
      scrollback: 5000,
      allowTransparency: false
    });

    xtermRef.current = terminal;

    // Load fit addon
    try {
      const fitAddon = new FitAddon();
      terminal.loadAddon(fitAddon);
      fitAddonRef.current = fitAddon;
    } catch (error) {
      console.error('Error loading FitAddon:', error);
    }

    // Open terminal in container
    terminal.open(terminalRef.current);

    // Handle user input
    terminal.onData((data: string) => {
      onData(data);
    });

    // Display welcome message
    terminal.writeln('Middlware Terminal');
    terminal.writeln('------------------');
    terminal.writeln(`Current directory: ${cwd || 'Not set'}`);
    terminal.writeln('');

    // Fit terminal after a delay
    setTimeout(() => {
      if (fitAddonRef.current) {
        try {
          fitAddonRef.current.fit();
          // Get dimensions and send to main process
          window.electron.ipcRenderer.invoke('terminal:resize', {
            id,
            cols: terminal.cols || 80,
            rows: terminal.rows || 30
          });
          setIsTerminalReady(true);
        } catch (e) {
          console.warn('Error fitting terminal:', e);
        }
      }
    }, 100);

    // Cleanup on unmount
    return () => {
      terminal.dispose();
    };
  }, [cwd, onData, id]);

  // Handle incoming data from terminal process
  useEffect(() => {
    if (!xtermRef.current) return;

    const handleTerminalData = (data: string) => {
      try {
        if (xtermRef.current) {
          xtermRef.current.write(data);
        }
      } catch (error) {
        console.error(`Error writing data to terminal UI for ${id}:`, error);
      }
    };

    // Handle terminal exit
    const handleTerminalExit = ({ code, signal }: { code: number, signal?: string }) => {
      try {
        if (xtermRef.current) {
          xtermRef.current.writeln(`\r\nTerminal process exited with code ${code}${signal ? `, signal: ${signal}` : ''}`);
          xtermRef.current.writeln('\r\nPress any key to restart terminal or close this tab.');
        }
      } catch (error) {
        console.error(`Error handling terminal exit for ${id}:`, error);
      }
    };

    // Subscribe to terminal data from main process
    const dataCleanup = window.electron.ipcRenderer.on(`terminal:data:${id}`, handleTerminalData);
    const exitCleanup = window.electron.ipcRenderer.on(`terminal:exit:${id}`, handleTerminalExit);

    // Handle window resize
    const handleResize = () => {
      if (fitAddonRef.current && xtermRef.current && isTerminalReady) {
        try {
          fitAddonRef.current.fit();

          // Get dimensions and send to main process
          window.electron.ipcRenderer.invoke('terminal:resize', {
            id,
            cols: xtermRef.current.cols || 80,
            rows: xtermRef.current.rows || 30
          }).catch(error => {
            // Silently handle resize errors - terminal might have exited
            console.warn(`Error resizing terminal ${id}:`, error);
          });
        } catch (e) {
          console.warn('Error fitting terminal:', e);
        }
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      dataCleanup();
      exitCleanup();
      window.removeEventListener('resize', handleResize);
    };
  }, [id, isTerminalReady]);

  // Focus terminal when clicked
  const handleClick = () => {
    xtermRef.current?.focus();
  };

  return (
    <TerminalContainer
      ref={terminalRef}
      className={className}
      onClick={handleClick}
    />
  );
};

const TerminalContainer = styled.div`
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #1e1e1e;
  padding: 4px;
`;

export default ModernTerminal;