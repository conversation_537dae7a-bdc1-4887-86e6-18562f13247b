// src/renderer/components/command-palette/CommandPalette.tsx
import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';

export interface Command {
  id: string;
  title: string;
  category: string;
  shortcut?: string;
  execute: () => void;
}

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
  commands: Command[];
}

export const CommandPalette: React.FC<CommandPaletteProps> = ({
  isOpen,
  onClose,
  commands
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCommands, setFilteredCommands] = useState<Command[]>(commands);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  // Filter commands when search term changes
  useEffect(() => {
    if (!searchTerm) {
      setFilteredCommands(commands);
      return;
    }

    const filtered = commands.filter(command => 
      command.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      command.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    setFilteredCommands(filtered);
    setSelectedIndex(0);
  }, [searchTerm, commands]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 50);
    }
  }, [isOpen]);

  // Handle key navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown': 
        e.preventDefault();
        setSelectedIndex(prev => (prev < filteredCommands.length - 1 ? prev + 1 : prev));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : prev));
        break;
      case 'Enter':
        e.preventDefault();
        if (filteredCommands[selectedIndex]) {
          executeCommand(filteredCommands[selectedIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        onClose();
        break;
    }
  };

  // Execute command and close the palette
  const executeCommand = (command: Command) => {
    onClose();
    setTimeout(() => {
      command.execute();
    }, 50);
  };

  // Scroll selected item into view
  useEffect(() => {
    if (listRef.current && filteredCommands.length > 0) {
      const selectedElement = listRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({ block: 'nearest' });
      }
    }
  }, [selectedIndex]);

  // If closed, don't render
  if (!isOpen) return null;

  return (
    <Overlay onClick={onClose}>
      <PaletteContainer onClick={e => e.stopPropagation()}>
        <SearchInput
          ref={inputRef}
          type="text"
          placeholder="Type a command..."
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          onKeyDown={handleKeyDown}
        />
        <CommandList ref={listRef}>
          {filteredCommands.length === 0 ? (
            <NoResults>No matching commands found</NoResults>
          ) : (
            filteredCommands.map((command, index) => (
              <CommandItem 
                key={command.id}
                isSelected={index === selectedIndex}
                onClick={() => executeCommand(command)}
              >
                <CommandTitle>{command.title}</CommandTitle>
                <CommandDetails>
                  <CommandCategory>{command.category}</CommandCategory>
                  {command.shortcut && <CommandShortcut>{command.shortcut}</CommandShortcut>}
                </CommandDetails>
              </CommandItem>
            ))
          )}
        </CommandList>
      </PaletteContainer>
    </Overlay>
  );
};

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 100px;
  z-index: 9999;
`;

const PaletteContainer = styled.div`
  width: 500px;
  max-width: 90%;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const SearchInput = styled.input`
  padding: 12px 16px;
  font-size: 16px;
  border: none;
  outline: none;
  background-color: ${({ theme }) => theme.colors.foreground};
  color: ${({ theme }) => theme.colors.text.primary};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const CommandList = styled.div`
  max-height: 300px;
  overflow-y: auto;
`;

const CommandItem = styled.div<{ isSelected: boolean }>`
  padding: 10px 16px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: ${({ isSelected, theme }) => 
    isSelected ? theme.colors.secondary : 'transparent'};

  &:hover {
    background-color: ${({ theme }) => theme.colors.secondary};
  }
`;

const CommandTitle = styled.div`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const CommandDetails = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const CommandCategory = styled.div`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
  padding: 2px 6px;
  background-color: ${({ theme }) => theme.colors.border};
  border-radius: 4px;
`;

const CommandShortcut = styled.div`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text.secondary};
  padding: 2px 6px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 4px;
`;

const NoResults = styled.div`
  padding: 16px;
  text-align: center;
  color: ${({ theme }) => theme.colors.text.secondary};
  font-style: italic;
`;