// src/renderer/shims.ts
// This file provides compatibility shims for the renderer process
// when using contextIsolation

// Ensure global is defined
(window as any).global = window;

// Import the events module for compatibility
import * as events from 'events';

// With contextIsolation enabled, we'll use the exposed APIs from the preload script
// The window.electron object will be provided by the preload script