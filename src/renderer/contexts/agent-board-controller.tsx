// src/renderer/contexts/agent-board-controller.tsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useBoard, Agent, Card } from './board-context';
import { boardIPCBridge } from '../lib/board-ipc-bridge';

// Agent types - extended from the original kanban-board implementation
export type AgentStatus = "idle" | "working" | "paused" | "error";
export type AgentType = "task-manager" | "developer" | "tester" | "documentation" | "devops" | "designer" | "code" | "docs" | "test";

// Initial agents with resource usage and capabilities
const initialAgents: Agent[] = [
  {
    id: 'agent-1',
    name: 'TaskBot',
    type: 'task-manager',
    status: 'idle',
    capabilities: ["task-management", "prioritization", "scheduling"],
    resourceUsage: {
      cpu: 0,
      memory: 0,
      tokens: 0
    }
  },
  {
    id: 'agent-2',
    name: 'CodeAssistant',
    type: 'developer',
    status: 'idle',
    capabilities: ["javascript", "react", "node"],
    resourceUsage: {
      cpu: 0,
      memory: 0,
      tokens: 0
    }
  },
  {
    id: 'agent-3',
    name: 'QATester',
    type: 'tester',
    status: 'idle',
    capabilities: ["testing", "automation", "quality-assurance"],
    resourceUsage: {
      cpu: 0,
      memory: 0,
      tokens: 0
    }
  },
  {
    id: 'agent-4',
    name: 'DocWriter',
    type: 'documentation',
    status: 'idle',
    capabilities: ["documentation", "technical-writing", "markdown"],
    resourceUsage: {
      cpu: 0,
      memory: 0,
      tokens: 0
    }
  }
];

// Context type
interface AgentBoardControllerContextType {
  agents: Agent[];
  isAgentSystemActive: boolean;
  toggleAgentSystem: () => void;
  registerAgent: (agent: Omit<Agent, 'id'>) => string;
  removeAgent: (agentId: string) => void;
  addAgentToBoard: (agent: any) => void;
  removeAgentFromBoard: (agentId: string) => void;
  assignTaskToAgent: (cardId: string, agentId: string) => Promise<boolean>;
  unassignTaskFromAgent: (cardId: string, agentId: string) => Promise<boolean>;
  updateAgentStatus: (agentId: string, status: AgentStatus, currentTaskId?: string) => void;
  updateAgentResourceUsage: (agentId: string, resourceUsage: Partial<Agent["resourceUsage"]>) => void;
  getAgentById: (agentId: string) => Agent | undefined;
  getAgentsByType: (type: string) => Agent[];
  getAgentsByStatus: (status: AgentStatus) => Agent[];
  getAgentAssignedCards: (agentId: string) => Card[];
  getAgentsAssignedToCard: (cardId: string) => Agent[];
  pauseAgent: (agentId: string) => void;
  resumeAgent: (agentId: string) => void;
}

const AgentBoardControllerContext = createContext<AgentBoardControllerContextType | undefined>(undefined);

export function AgentBoardControllerProvider({ children }: { children: React.ReactNode }) {
  const [agents, setAgents] = useState<Agent[]>(initialAgents);
  const [isAgentSystemActive, setIsAgentSystemActive] = useState(false);
  const boardContext = useBoard();

  // Listen for agent status updates from main process
  useEffect(() => {
    const unsubscribe = boardIPCBridge.registerEventListeners({
      onAgentStatus: (status) => {
        setAgents((prevAgents) =>
          prevAgents.map((agent) => {
            if (agent.id === status.agentId) {
              return {
                ...agent,
                status: status.status as AgentStatus
              };
            }
            return agent;
          })
        );
      },
    });

    return unsubscribe;
  }, []);

  // Toggle agent system
  const toggleAgentSystem = () => {
    const newState = !isAgentSystemActive;
    setIsAgentSystemActive(newState);

    // Notify main process about agent system status
    window.electron.ipcRenderer.send('agent-system:toggle', newState);

    // Update agent statuses when toggling
    if (!newState) {
      setAgents(
        agents.map((agent) => ({
          ...agent,
          status: 'idle' as AgentStatus,
          currentTaskId: undefined,
        }))
      );
    } else {
      // Potentially activate agents or signal the main process to do so.
      // For now, this just toggles the state.
    }
  };

  // Register a new agent
  const registerAgent = (agent: Omit<Agent, 'id'>): string => {
    const id = `agent-${Date.now()}`;
    const newAgent = { ...agent, id, status: 'idle' as AgentStatus };
    setAgents(prev => [...prev, newAgent]);
    return id;
  };

  // Remove an agent
  const removeAgent = (agentId: string) => {
    setAgents(prev => prev.filter(agent => agent.id !== agentId));
    // In an Electron app, this might involve saving the updated agents to persistent storage
    // or informing the main process.
  };

  // Add agent to board
  const addAgentToBoard = (agent: any) => {
    const id = `agent-${Date.now()}`;
    const newAgent = {
      ...agent,
      id,
      status: 'idle' as AgentStatus,
      resourceUsage: {
        cpu: 0,
        memory: 0,
        tokens: 0
      }
    };
    setAgents(prev => [...prev, newAgent]);
  };

  // Remove agent from board
  const removeAgentFromBoard = (agentId: string) => {
    setAgents(prev => prev.filter(agent => agent.id !== agentId));
  };

  // Assign a task to an agent
  const assignTaskToAgent = async (cardId: string, agentId: string): Promise<boolean> => {
    try {
      const agent = agents.find(a => a.id === agentId);
      if (!agent) return false;

      const result = await boardIPCBridge.assignAgent(cardId, agentId, agent.type);

      if (result) {
        // Update agent status
        setAgents(prev => prev.map(a =>
          a.id === agentId ? { ...a, status: 'working', currentTaskId: cardId } : a
        ));
      }

      return result;
    } catch (error) {
      console.error('Failed to assign task to agent:', error);
      return false;
    }
  };

  // Unassign a task from an agent
  const unassignTaskFromAgent = async (cardId: string, agentId: string): Promise<boolean> => {
    try {
      // Update the card to remove the agent assignment
      const card = boardContext.activeBoard?.cards.find(c => c.id === cardId);
      if (!card) return false;

      const updatedAssignments = card.agentAssignments?.filter(a => a.agentId !== agentId) || [];

      await boardContext.updateCardInColumn(
        boardContext.activeBoard?.id || '',
        cardId,
        { agentAssignments: updatedAssignments }
      );

      // Update agent status
      setAgents(prev => prev.map(a =>
        a.id === agentId && a.currentTaskId === cardId ? { ...a, status: 'idle', currentTaskId: undefined } : a
      ));

      return true;
    } catch (error) {
      console.error('Failed to unassign task from agent:', error);
      return false;
    }
  };

  // Update agent status
  const updateAgentStatus = (agentId: string, status: AgentStatus, currentTaskId?: string) => {
    setAgents(prev => prev.map(agent =>
      agent.id === agentId ? { ...agent, status, currentTaskId } : agent
    ));
    // Agent status changes are prime candidates for IPC if actual processes are involved.
  };

  // Update agent resource usage
  const updateAgentResourceUsage = (agentId: string, resourceUsage: Partial<Agent["resourceUsage"]>) => {
    setAgents(prev => prev.map(agent => {
      if (agent.id === agentId) {
        return {
          ...agent,
          resourceUsage: {
            cpu: resourceUsage.cpu ?? agent.resourceUsage.cpu,
            memory: resourceUsage.memory ?? agent.resourceUsage.memory,
            tokens: resourceUsage.tokens ?? agent.resourceUsage.tokens
          }
        };
      }
      return agent;
    }));
    // Resource usage updates would typically come from the main process (if monitoring) or the agents themselves via IPC.
  };

  // Get agent by ID
  const getAgentById = (agentId: string) => {
    return agents.find(agent => agent.id === agentId);
  };

  // Get agents by type
  const getAgentsByType = (type: string) => {
    return agents.filter(agent => agent.type === type);
  };

  // Get agents by status
  const getAgentsByStatus = (status: AgentStatus) => {
    return agents.filter(agent => agent.status === status);
  };

  // Get cards assigned to an agent
  const getAgentAssignedCards = (agentId: string) => {
    if (!boardContext.activeBoard) return [];

    return boardContext.activeBoard.cards.filter(card =>
      card.agentAssignments?.some(assignment => assignment.agentId === agentId)
    );
  };

  // Get agents assigned to a card
  const getAgentsAssignedToCard = (cardId: string) => {
    if (!boardContext.activeBoard) return [];

    const card = boardContext.activeBoard.cards.find(c => c.id === cardId);
    if (!card || !card.agentAssignments) return [];

    return card.agentAssignments.map(assignment =>
      agents.find(agent => agent.id === assignment.agentId)
    ).filter((agent): agent is Agent => agent !== undefined);
  };

  // Pause agent
  const pauseAgent = (agentId: string) => {
    updateAgentStatus(agentId, "paused");

    // Notify main process
    window.electron.ipcRenderer.send('agent:pause', agentId);
  };

  // Resume agent
  const resumeAgent = (agentId: string) => {
    const agent = getAgentById(agentId);
    if (agent) {
      const newStatus = agent.currentTaskId ? "working" : "idle";
      updateAgentStatus(agentId, newStatus);

      // Notify main process
      window.electron.ipcRenderer.send('agent:resume', agentId);
    }
  };

  return (
    <AgentBoardControllerContext.Provider
      value={{
        agents,
        isAgentSystemActive,
        toggleAgentSystem,
        registerAgent,
        removeAgent,
        addAgentToBoard,
        removeAgentFromBoard,
        assignTaskToAgent,
        unassignTaskFromAgent,
        updateAgentStatus,
        updateAgentResourceUsage,
        getAgentById,
        getAgentsByType,
        getAgentsByStatus,
        getAgentAssignedCards,
        getAgentsAssignedToCard,
        pauseAgent,
        resumeAgent,
      }}
    >
      {children}
    </AgentBoardControllerContext.Provider>
  );
}

export const useAgentBoardController = () => {
  const context = useContext(AgentBoardControllerContext);
  if (context === undefined) {
    throw new Error('useAgentBoardController must be used within an AgentBoardControllerProvider');
  }
  return context;
};
