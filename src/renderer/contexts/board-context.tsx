// src/renderer/contexts/board-context.tsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { boardIPCBridge } from '../lib/board-ipc-bridge';

// Types
export interface Card {
  id: string;
  title: string;
  description?: string;
  columnId: string;
  swimlaneId: string;
  priority?: 'low' | 'medium' | 'high';
  type?: string;
  progress?: number;
  dueDate?: string;
  agentAssignments?: Array<{
    agentId: string;
    agentType?: string;
    assignmentTime: string;
  }>;
  dependencies?: string[];
  resourceMetrics?: {
    tokenUsage: number;
    cpuTime: number;
    memoryUsage: number;
  };
  taskHistory?: Array<{
    timestamp: string;
    action: string;
    agentId?: string;
    details?: string;
  }>;
  tags?: string[];
  storyPoints?: number;
  projectId?: string;
  subtasks?: Array<{
    id: string;
    title: string;
    completed: boolean;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface Column {
  id: string;
  name: string;
  order: number;
  color?: string;
  cardIds?: string[];
}

export interface Swimlane {
  id: string;
  name: string;
  order: number;
  expanded: boolean;
  color?: string;
}

export interface CardType {
  id: string;
  name: string;
  color: string;
}

export interface ResourceUsage {
  cpu: number;
  memory: number;
  tokens: number;
}

export interface Agent {
  id: string;
  name: string;
  type: string;
  status?: 'idle' | 'working' | 'paused' | 'error';
  currentTaskId?: string;
  capabilities?: string[];
  resourceUsage: ResourceUsage;
}

export interface BoardFull {
  id: string;
  name: string;
  columns: Column[];
  swimlanes: Swimlane[];
  cards: Card[];
  cardTypes: CardType[];
  agents: Agent[];
}

interface BoardContextType {
  boards: BoardFull[];
  activeBoard: BoardFull | null;
  setActiveBoard: (boardId: string) => void;
  addBoard: (board: Omit<BoardFull, 'id'>) => string;
  updateBoard: (boardId: string, updates: Partial<BoardFull>) => void;
  deleteBoard: (boardId: string) => void;
  addColumn: (boardId: string, column: Omit<Column, 'id'>) => Promise<string>;
  updateColumn: (boardId: string, columnId: string, updates: Partial<Column>) => void;
  deleteColumn: (boardId: string, columnId: string) => void;
  addSwimlane: (boardId: string, swimlane: Omit<Swimlane, 'id'>) => Promise<string>;
  updateSwimlane: (boardId: string, swimlaneId: string, updates: Partial<Swimlane>) => void;
  deleteSwimlane: (boardId: string, swimlaneId: string) => void;
  toggleSwimlaneExpansion: (boardId: string, swimlaneId: string) => void;
  addCardToColumn: (boardId: string, columnId: string, card: Omit<Card, 'id'>) => Promise<string>;
  updateCardInColumn: (boardId: string, cardId: string, updates: Partial<Card>) => void;
  deleteCardFromColumn: (boardId: string, cardId: string) => void;
  moveCard: (boardId: string, cardId: string, toColumnId: string, toSwimlaneId: string) => void;
  updateCardTypes: (boardId: string, cardTypes: CardType[]) => void;
  updateAgents: (boardId: string, agents: Agent[]) => void;
}

const BoardContext = createContext<BoardContextType | undefined>(undefined);

export function BoardProvider({ children }: { children: React.ReactNode }) {
  const [boards, setBoards] = useState<BoardFull[]>([]);
  const [activeBoard, setActiveBoardState] = useState<BoardFull | null>(null);

  // Initialize from IPC bridge
  useEffect(() => {
    const initializeBoard = async () => {
      try {
        const boardState = await boardIPCBridge.getBoardState();
        if (boardState && boardState.id) {
          setBoards([boardState]);
          setActiveBoardState(boardState);
        } else {
          console.error('No valid board state received from IPC');
        }
      } catch (error) {
        console.error('Failed to initialize board from IPC:', error);
      }
    };

    initializeBoard();

    // Register listeners for updates from main process
    const unsubscribe = boardIPCBridge.registerEventListeners({
      onStateUpdate: (state) => {
        if (state && state.id) {
          setBoards([state]);
          setActiveBoardState(state);
        }
      },
      onAgentAction: (action) => {
        // Handle agent actions here
        console.log('Agent action received:', action);
      },
    });

    return unsubscribe;
  }, []);

  // Set active board
  const setActiveBoard = (boardId: string) => {
    const board = boards.find(b => b.id === boardId);
    if (board) {
      setActiveBoardState(board);
    }
  };

  // Add a new board
  const addBoard = (board: Omit<BoardFull, 'id'>): string => {
    const id = `board-${Date.now()}`;
    const newBoard = { ...board, id };
    setBoards(prev => [...prev, newBoard]);
    return id;
  };

  // Update a board
  const updateBoard = (boardId: string, updates: Partial<BoardFull>) => {
    setBoards(prev => prev.map(board =>
      board.id === boardId ? { ...board, ...updates } : board
    ));

    // Update active board if needed
    if (activeBoard?.id === boardId) {
      setActiveBoardState(prev => prev ? { ...prev, ...updates } : null);
    }
  };

  // Delete a board
  const deleteBoard = (boardId: string) => {
    setBoards(prev => prev.filter(board => board.id !== boardId));

    // Update active board if needed
    if (activeBoard?.id === boardId) {
      setActiveBoardState(boards.length > 1 ? boards.find(b => b.id !== boardId) || null : null);
    }
  };

  // Add a column to a board
  const addColumn = async (boardId: string, column: Omit<Column, 'id'>): Promise<string> => {
    try {
      const newColumn = await boardIPCBridge.createColumn(column);
      return newColumn.id;
    } catch (error) {
      console.error('Failed to add column:', error);
      return '';
    }
  };

  // Update a column
  const updateColumn = async (boardId: string, columnId: string, updates: Partial<Column>) => {
    try {
      await boardIPCBridge.updateColumn(columnId, updates);
    } catch (error) {
      console.error('Failed to update column:', error);
    }
  };

  // Delete a column
  const deleteColumn = async (boardId: string, columnId: string) => {
    try {
      await boardIPCBridge.deleteColumn(columnId);
    } catch (error) {
      console.error('Failed to delete column:', error);
    }
  };

  // Add a swimlane to a board
  const addSwimlane = async (boardId: string, swimlane: Omit<Swimlane, 'id'>): Promise<string> => {
    try {
      const newSwimlane = await boardIPCBridge.createSwimlane(swimlane);
      return newSwimlane.id;
    } catch (error) {
      console.error('Failed to add swimlane:', error);
      return '';
    }
  };

  // Update a swimlane
  const updateSwimlane = async (boardId: string, swimlaneId: string, updates: Partial<Swimlane>) => {
    try {
      await boardIPCBridge.updateSwimlane(swimlaneId, updates);
    } catch (error) {
      console.error('Failed to update swimlane:', error);
    }
  };

  // Delete a swimlane
  const deleteSwimlane = async (boardId: string, swimlaneId: string) => {
    try {
      await boardIPCBridge.deleteSwimlane(swimlaneId);
    } catch (error) {
      console.error('Failed to delete swimlane:', error);
    }
  };

  // Toggle swimlane expansion
  const toggleSwimlaneExpansion = async (boardId: string, swimlaneId: string) => {
    if (!activeBoard) return;

    try {
      await boardIPCBridge.toggleSwimlaneExpansion(swimlaneId);
    } catch (error) {
      console.error('Failed to toggle swimlane expansion:', error);
    }
  };

  // Add a card to a column
  const addCardToColumn = async (boardId: string, columnId: string, card: Omit<Card, 'id'>): Promise<string> => {
    try {
      const newCard = await boardIPCBridge.createCard({
        ...card,
        columnId,
      });

      return newCard.id;
    } catch (error) {
      console.error('Failed to add card:', error);
      return '';
    }
  };

  // Update a card
  const updateCardInColumn = async (boardId: string, cardId: string, updates: Partial<Card>) => {
    try {
      await boardIPCBridge.updateCard(cardId, updates);
    } catch (error) {
      console.error('Failed to update card:', error);
    }
  };

  // Delete a card
  const deleteCardFromColumn = async (boardId: string, cardId: string) => {
    try {
      await boardIPCBridge.deleteCard(cardId);
    } catch (error) {
      console.error('Failed to delete card:', error);
    }
  };

  // Move a card
  const moveCard = async (boardId: string, cardId: string, toColumnId: string, toSwimlaneId: string) => {
    try {
      await boardIPCBridge.moveCard(cardId, toColumnId, toSwimlaneId);
    } catch (error) {
      console.error('Failed to move card:', error);
    }
  };

  // Update card types
  const updateCardTypes = async (boardId: string, cardTypes: CardType[]) => {
    try {
      await boardIPCBridge.updateCardTypes(boardId, cardTypes);
    } catch (error) {
      console.error('Failed to update card types:', error);
      // Fallback to local update
      updateBoard(boardId, { cardTypes });
    }
  };

  // Update agents
  const updateAgents = async (boardId: string, agents: Agent[]) => {
    try {
      await boardIPCBridge.updateAgents(boardId, agents);
    } catch (error) {
      console.error('Failed to update agents:', error);
      // Fallback to local update
      updateBoard(boardId, { agents });
    }
  };

  return (
    <BoardContext.Provider
      value={{
        boards,
        activeBoard,
        setActiveBoard,
        addBoard,
        updateBoard,
        deleteBoard,
        addColumn,
        updateColumn,
        deleteColumn,
        addSwimlane,
        updateSwimlane,
        deleteSwimlane,
        toggleSwimlaneExpansion,
        addCardToColumn,
        updateCardInColumn,
        deleteCardFromColumn,
        moveCard,
        updateCardTypes,
        updateAgents,
      }}
    >
      {children}
    </BoardContext.Provider>
  );
}

export const useBoard = () => {
  const context = useContext(BoardContext);
  if (context === undefined) {
    throw new Error('useBoard must be used within a BoardProvider');
  }
  return context;
};
