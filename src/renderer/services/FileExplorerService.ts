// src/renderer/services/FileExplorerService.ts
export interface FileInfo {
    name: string;
    path: string;
    type: 'file' | 'directory';
    children?: FileInfo[];
  }
  
  export interface FileSystemResponse<T = any> {
    success: boolean;
    error?: string;
    canceled?: boolean;
    files?: FileInfo[];
    content?: string;
    path?: string;
  }
  
  class FileExplorerService {
    private checkElectron() {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }
    }
  
    async readDirectory(dirPath: string): Promise<FileSystemResponse<FileInfo[]>> {
      try {
        this.checkElectron();
        return await window.electron.ipcRenderer.invoke('fs:readDirectory', { dirPath });
      } catch (error) {
        console.error('Error reading directory:', error);
        return { success: false, error: (error as Error).message };
      }
    }
  
    async readFile(filePath: string): Promise<FileSystemResponse<string>> {
      try {
        this.checkElectron();
        return await window.electron.ipcRenderer.invoke('fs:readFile', { filePath });
      } catch (error) {
        console.error('Error reading file:', error);
        return { success: false, error: (error as Error).message };
      }
    }
  
    async writeFile(filePath: string, content: string): Promise<FileSystemResponse<null>> {
      try {
        this.checkElectron();
        return await window.electron.ipcRenderer.invoke('fs:writeFile', { filePath, content });
      } catch (error) {
        console.error('Error writing file:', error);
        return { success: false, error: (error as Error).message };
      }
    }
  
    async createFile(filePath: string): Promise<FileSystemResponse<null>> {
      try {
        this.checkElectron();
        return await window.electron.ipcRenderer.invoke('fs:createFile', { filePath });
      } catch (error) {
        console.error('Error creating file:', error);
        return { success: false, error: (error as Error).message };
      }
    }
  
    async createDirectory(dirPath: string): Promise<FileSystemResponse<null>> {
      try {
        this.checkElectron();
        return await window.electron.ipcRenderer.invoke('fs:createDirectory', { dirPath });
      } catch (error) {
        console.error('Error creating directory:', error);
        return { success: false, error: (error as Error).message };
      }
    }
  
    async openDirectoryDialog(): Promise<FileSystemResponse<string>> {
      try {
        this.checkElectron();
        return await window.electron.ipcRenderer.invoke('fs:openDirectory', null);
      } catch (error) {
        console.error('Error opening directory dialog:', error);
        return { success: false, error: (error as Error).message };
      }
    }
  }
  
  export const fileExplorerService = new FileExplorerService();