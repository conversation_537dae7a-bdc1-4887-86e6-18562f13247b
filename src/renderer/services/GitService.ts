// src/renderer/services/GitService.ts
export interface GitStatusResult {
  staged: string[];
  modified: string[];
  untracked: string[];
  isRepo: boolean;
}

export interface GitResponse<T = any> {
  success: boolean;
  error?: string;
  result?: T;
  branch?: string;
  isRepo?: boolean;
}

class GitService {
  private checkElectron() {
    if (!window.electron) {
      throw new Error('Electron API not available');
    }
  }

  async isGitRepository(dirPath: string): Promise<boolean> {
    try {
      this.checkElectron();
      const response = await window.electron.ipcRenderer.invoke('git:isRepo', { dirPath });
      return response.isRepo;
    } catch (error) {
      console.error('Error checking if directory is a Git repository:', error);
      return false;
    }
  }

  async getStatus(dirPath: string): Promise<GitResponse<GitStatusResult>> {
    try {
      this.checkElectron();
      return await window.electron.ipcRenderer.invoke('git:status', { dirPath });
    } catch (error) {
      console.error('Error getting Git status:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  async stageFile(filePath: string, dirPath: string): Promise<GitResponse> {
    try {
      this.checkElectron();
      return await window.electron.ipcRenderer.invoke('git:stage', { filePath, dirPath });
    } catch (error) {
      console.error('Error staging file:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  async unstageFile(filePath: string, dirPath: string): Promise<GitResponse> {
    try {
      this.checkElectron();
      return await window.electron.ipcRenderer.invoke('git:unstage', { filePath, dirPath });
    } catch (error) {
      console.error('Error unstaging file:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  async getCurrentBranch(dirPath: string): Promise<GitResponse> {
    try {
      this.checkElectron();
      return await window.electron.ipcRenderer.invoke('git:currentBranch', { dirPath });
    } catch (error) {
      console.error('Error getting current branch:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  async commitChanges(message: string, dirPath: string): Promise<GitResponse> {
    try {
      this.checkElectron();
      return await window.electron.ipcRenderer.invoke('git:commit', { message, dirPath });
    } catch (error) {
      console.error('Error committing changes:', error);
      return { success: false, error: (error as Error).message };
    }
  }
}

export const gitService = new GitService();