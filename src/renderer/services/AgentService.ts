// src/renderer/services/AgentService.ts
import { AgentContext, AgentResponse } from '../../agent/base/Agent';

interface Agent {
  id: string;
  name: string;
  description: string;
  capabilities: string[];
}

interface ChatMessage {
  id: string;
  role: 'user' | 'agent';
  content: string;
  timestamp: Date;
  agentId?: string;
}

class AgentService {
  private initialized: boolean = false;
  private chatHistory: Map<string, ChatMessage[]> = new Map();
  private provider: string = 'openai';
  private selectedModels: Record<string, string> = {
    openai: 'gpt-4',
    anthropic: 'claude-3-opus-20240229'
  };
  private availableAgents: Agent[] = [];

  isInitialized(): boolean {
    return this.initialized;
  }

  async initialize(apiKeys?: Record<string, string>, selectedModels?: Record<string, string>): Promise<boolean> {
    try {
      // Remember if we're already initialized
      const wasInitialized = this.initialized;

      // Validate API keys before sending to main process
      if (apiKeys) {
        // Store keys securely (using electron's secure store ideally)
        Object.entries(apiKeys).forEach(([provider, key]) => {
          if (key && key.trim() !== '') {
            // Only store non-empty keys
            localStorage.setItem(`apiKey_${provider}`, key);
          }
        });

        // Set default provider based on available keys
        if (apiKeys.openai && apiKeys.openai.trim() !== '') {
          this.provider = 'openai';
          localStorage.setItem('defaultProvider', 'openai');
        } else if (apiKeys.anthropic && apiKeys.anthropic.trim() !== '') {
          this.provider = 'anthropic';
          localStorage.setItem('defaultProvider', 'anthropic');
        } else if (apiKeys.gemini && apiKeys.gemini.trim() !== '') {
          this.provider = 'gemini';
          localStorage.setItem('defaultProvider', 'gemini');
        }
      } else {
        // Load stored keys if not provided
        apiKeys = {};
        const storedProvider = localStorage.getItem('defaultProvider');
        if (storedProvider) {
          this.provider = storedProvider;
        }

        // Load all stored API keys
        ['openai', 'anthropic', 'gemini', 'deepseek', 'meta', 'mistral', 'openrouter', 'ollama'].forEach(provider => {
          const key = localStorage.getItem(`apiKey_${provider}`);
          if (key) {
            apiKeys![provider] = key;
          }
        });
      }

      // Update selected models if provided, otherwise load from storage
      if (selectedModels) {
        this.selectedModels = {...this.selectedModels, ...selectedModels};
        // Store selected models
        localStorage.setItem('selectedModels', JSON.stringify(this.selectedModels));
      } else {
        // Load stored models
        const storedModels = localStorage.getItem('selectedModels');
        if (storedModels) {
          try {
            this.selectedModels = {...this.selectedModels, ...JSON.parse(storedModels)};
          } catch (e) {
            console.error('Failed to parse stored models:', e);
          }
        }
      }

      const response = await window.electron.ipcRenderer.invoke('agent:initialize', {
        apiKeys,
        selectedModels: this.selectedModels
      });

      this.initialized = response.success;

      // If newly initialized, load available agents
      if (this.initialized && !wasInitialized) {
        await this.loadAvailableAgents();
      }

      return response.success;
    } catch (error) {
      console.error('Failed to initialize agent system:', error);
      return false;
    }
  }

  async processQuery(context: AgentContext): Promise<AgentResponse> {
    try {
      if (!this.initialized) {
        const apiKeys: Record<string, string> = {};
        apiKeys[this.provider] = '';
        const initSuccess = await this.initialize(apiKeys);
        if (!initSuccess) {
          return {
            success: false,
            error: 'Agent system not initialized'
          };
        }
      }

      // Add provider to context if not specified
      if (!context.provider) {
        context.provider = this.provider;
      }

      // Override the model if specified in context
      if (context.model) {
        context.model = context.model;
      }

      // Add selected model to context if not specified
      if (!context.model && this.provider) {
        context.model = this.selectedModels[this.provider];
      }

      // Add to chat history
      this.addToChatHistory(context.projectPath, {
        id: this.generateId(),
        role: 'user',
        content: context.query || '',
        timestamp: new Date()
      });

      const response = await window.electron.ipcRenderer.invoke('agent:process', context);

      // Add agent response to chat history
      this.addToChatHistory(context.projectPath, {
        id: this.generateId(),
        role: 'agent',
        content: response.success
          ? (response.result || 'Task completed successfully.')
          : (response.error || 'Failed to process request.'),
        timestamp: new Date(),
        agentId: response.agentId || 'micromanager'
      });

      return response;
    } catch (error) {
      console.error('Agent service error:', error);
      return {
        success: false,
        error: `Agent service error: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  async loadAvailableAgents(): Promise<void> {
    try {
      const response = await window.electron.ipcRenderer.invoke('agent:getAgents', null);
      if (response.success && response.agents) {
        // Store agents for UI to use
        this.availableAgents = response.agents;
      }
    } catch (error) {
      console.error('Failed to load available agents:', error);
    }
  }

  // Get available agents
  getAvailableAgents(): Agent[] {
    return this.availableAgents;
  }

  async getAgents(): Promise<Agent[]> {
    try {
      if (!this.initialized) {
        const apiKeys: Record<string, string> = {};
        apiKeys[this.provider] = '';
        const initSuccess = await this.initialize(apiKeys);
        if (!initSuccess) {
          return [];
        }
      }

      // If we already have agents loaded, return them
      if (this.availableAgents.length > 0) {
        return this.availableAgents;
      }

      const response = await window.electron.ipcRenderer.invoke('agent:getAgents', null);

      if (!response.success) {
        console.error('Failed to get agents:', response.error);
        return [];
      }

      // Store for future use
      this.availableAgents = response.agents;
      return response.agents;
    } catch (error) {
      console.error('Failed to get agents:', error);
      return [];
    }
  }

  getChatHistory(projectPath?: string): ChatMessage[] {
    const path = projectPath || 'default';
    return this.chatHistory.get(path) || [];
  }

  clearChatHistory(projectPath?: string): void {
    const path = projectPath || 'default';
    this.chatHistory.delete(path);
  }

  private addToChatHistory(projectPath: string | undefined, message: ChatMessage): void {
    const path = projectPath || 'default';
    if (!this.chatHistory.has(path)) {
      this.chatHistory.set(path, []);
    }

    const history = this.chatHistory.get(path)!;
    history.push(message);

    // Limit history size
    if (history.length > 100) {
      history.shift();
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 9);
  }
}

export const agentService = new AgentService();