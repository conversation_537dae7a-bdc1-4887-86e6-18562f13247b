// src/renderer/services/TerminalService.ts
export interface TerminalSession {
    id: string;
    name: string;
    cwd?: string;
  }

  class TerminalService {
    private sessions: Map<string, TerminalSession> = new Map();
    private lastId = 0;
    private terminalStatus: Map<string, { active: boolean, reconnectAttempts: number }> = new Map();

    async createTerminal(cwd?: string, name?: string): Promise<TerminalSession> {
      // Generate terminal ID before try block so it's available in catch
      const id = `terminal-${++this.lastId}`;
      const terminalName = name || `Terminal ${this.lastId}`;

      try {
        console.log(`Creating terminal ${id} with cwd: ${cwd || 'default'}`);

        // Set initial terminal status
        this.terminalStatus.set(id, { active: true, reconnectAttempts: 0 });

        const response = await window.electron.ipcRenderer.invoke('terminal:create', { id, cwd });

        if (!response.success) {
          console.error(`Failed to create terminal: ${response.error}`);
          this.terminalStatus.delete(id);
          throw new Error(response.error || 'Failed to create terminal');
        }

        // Use the actual cwd returned from main process
        const session: TerminalSession = {
          id,
          name: terminalName,
          cwd: response.cwd || cwd
        };
        this.sessions.set(id, session);

        // Set up exit handler to update status
        const exitCleanup = window.electron.ipcRenderer.on(`terminal:exit:${id}`, () => {
          console.log(`Terminal ${id} exited, updating status`);
          if (this.terminalStatus.has(id)) {
            const status = this.terminalStatus.get(id)!;
            status.active = false;
          }
        });

        console.log(`Terminal created: ${id} (${terminalName}), cwd: ${session.cwd}`);
        return session;
      } catch (error) {
        console.error(`Error creating terminal ${id}:`, error);
        // Clean up status on error
        this.terminalStatus.delete(id);
        throw error;
      }
    }

    async writeToTerminal(id: string, data: string): Promise<void> {
      if (!this.sessions.has(id)) {
        console.warn(`Attempted to write to non-existent terminal: ${id}`);
        return;
      }

      // Check terminal status
      const status = this.terminalStatus.get(id);
      if (status && !status.active) {
        console.warn(`Attempted to write to inactive terminal: ${id}`);
        return;
      }

      try {
        console.log(`Writing to terminal ${id}: "${data.replace(/\r|\n/g, '\\n')}"`);
        const response = await window.electron.ipcRenderer.invoke('terminal:write', { id, data });

        if (!response.success) {
          console.error(`Failed to write to terminal ${id}: ${response.error}`);

          // Handle specific error cases
          if (response.error === 'Terminal is no longer active' ||
              response.error === 'Terminal not found' ||
              (response.error && response.error.includes('EIO'))) {

            // Mark terminal as inactive
            if (status) {
              status.active = false;
            }

            // Don't throw for expected terminal exit conditions
            console.warn(`Terminal ${id} is no longer active, marking as inactive`);
            return;
          }

          throw new Error(response.error || 'Failed to write to terminal');
        }
      } catch (error) {
        console.error(`Error writing to terminal ${id}:`, error);

        // Check for EIO errors
        if (error instanceof Error && error.message.includes('EIO')) {
          console.warn(`EIO error detected for terminal ${id}, marking as inactive`);
          if (status) {
            status.active = false;
          }
          return; // Don't propagate EIO errors
        }

        throw error;
      }
    }

    async closeTerminal(id: string): Promise<void> {
      if (!this.sessions.has(id)) {
        console.warn(`Attempted to close non-existent terminal: ${id}`);
        // Clean up any lingering status
        this.terminalStatus.delete(id);
        return;
      }

      // Mark terminal as inactive before attempting to close it
      const status = this.terminalStatus.get(id);
      if (status) {
        status.active = false;
      }

      try {
        console.log(`Closing terminal ${id}`);
        const response = await window.electron.ipcRenderer.invoke('terminal:close', { id });

        if (!response.success && response.error !== 'Terminal not found') {
          console.error(`Failed to close terminal ${id}: ${response.error}`);
          // Don't throw here, just log the error
        }
      } catch (error) {
        console.error(`Error closing terminal ${id}:`, error);
        // Don't propagate the error, just log it
      } finally {
        // Always clean up resources
        this.sessions.delete(id);

        // Wait a bit before removing status to allow any pending operations to complete
        setTimeout(() => {
          this.terminalStatus.delete(id);
        }, 1000);
      }
    }

    async changeDirectory(id: string, cwd: string): Promise<void> {
      if (!this.sessions.has(id)) {
        console.warn(`Attempted to change directory for non-existent terminal: ${id}`);
        return;
      }

      // Check terminal status
      const status = this.terminalStatus.get(id);
      if (status && !status.active) {
        console.warn(`Attempted to change directory for inactive terminal: ${id}`);
        return;
      }

      try {
        console.log(`Changing directory for terminal ${id} to: ${cwd}`);
        const response = await window.electron.ipcRenderer.invoke('terminal:changeCwd', { id, cwd });

        if (!response.success) {
          console.error(`Failed to change directory for terminal ${id}: ${response.error}`);

          // Handle specific error cases
          if (response.error === 'Terminal not found' ||
              (response.error && response.error.includes('EIO'))) {

            // Mark terminal as inactive
            if (status) {
              status.active = false;
            }

            console.warn(`Terminal ${id} is no longer active, marking as inactive`);
          }

          return; // Don't throw here - just log the error
        }

        const session = this.sessions.get(id);
        if (session) {
          session.cwd = response.cwd || cwd;
        }
      } catch (error) {
        console.error(`Error changing directory for terminal ${id}:`, error);

        // Check for EIO errors
        if (error instanceof Error && error.message.includes('EIO')) {
          console.warn(`EIO error detected for terminal ${id}, marking as inactive`);
          if (status) {
            status.active = false;
          }
        }
      }
    }

    getSession(id: string): TerminalSession | undefined {
      return this.sessions.get(id);
    }

    getAllSessions(): TerminalSession[] {
      return Array.from(this.sessions.values());
    }
  }

  export const terminalService = new TerminalService();