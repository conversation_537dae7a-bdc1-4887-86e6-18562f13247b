// src/renderer/styles/monacoWorker.ts
// This file is needed to configure Monaco Editor's web workers properly in Electron
// It ensures the workers are properly loaded from the local application rather than from CDN

// Expose global self (required for Monaco web workers)
self.MonacoEnvironment = {
    getWorkerUrl: function (moduleId, label) {
      if (label === 'json') {
        return './json.worker.js';
      }
      if (label === 'css' || label === 'scss' || label === 'less') {
        return './css.worker.js';
      }
      if (label === 'html' || label === 'handlebars' || label === 'razor') {
        return './html.worker.js';
      }
      if (label === 'typescript' || label === 'javascript') {
        return './ts.worker.js';
      }
      return './editor.worker.js';
    }
  };