import { DefaultTheme } from 'styled-components';

// Define the theme interface
export interface Theme {
  mode: 'light' | 'dark';
  colors: {
    background: string;
    foreground: string;
    primary: string;
    primaryDark: string;
    secondary: string;
    accent: string;
    error: string;
    warning: string;
    info: string;
    success: string;
    text: {
      primary: string;
      secondary: string;
      accent: string;
      error: string;
    };
    border: string;
    shadow: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  fontSizes: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    circle: string;
  };
}

// Dark theme (VS Code Dark+)
export const darkTheme: DefaultTheme = {
  mode: 'dark',
  colors: {
    background: '#1e1e1e',
    foreground: '#252526',
    primary: '#007acc',
    primaryDark: '#005a9e',
    secondary: '#2d2d2d',
    accent: '#0097fb',
    error: '#f48771',
    warning: '#cca700',
    info: '#75beff',
    success: '#89d185',
    text: {
      primary: '#d4d4d4',
      secondary: '#a7a7a7',
      accent: '#0097fb',
      error: '#f48771',
    },
    border: '#474747',
    shadow: 'rgba(0, 0, 0, 0.25)',
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
  },
  fontSizes: {
    xs: '10px',
    sm: '12px',
    md: '14px',
    lg: '16px',
    xl: '18px',
  },
  borderRadius: {
    xs: '2px',
    sm: '4px',
    md: '6px',
    lg: '8px',
    circle: '50%',
  },
};

// Light theme (VS Code Light+)
export const lightTheme: DefaultTheme = {
  mode: 'light',
  colors: {
    background: '#ffffff',
    foreground: '#f3f3f3',
    primary: '#007acc',
    primaryDark: '#005c99',
    secondary: '#eeeeee',
    accent: '#0066b5',
    error: '#e51400',
    warning: '#e9a700',
    info: '#1a85ff',
    success: '#388a34',
    text: {
      primary: '#000000',
      secondary: '#616161',
      accent: '#0066b5',
      error: '#e51400',
    },
    border: '#d4d4d4',
    shadow: 'rgba(0, 0, 0, 0.1)',
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
  },
  fontSizes: {
    xs: '10px',
    sm: '12px',
    md: '14px',
    lg: '16px',
    xl: '18px',
  },
  borderRadius: {
    xs: '2px',
    sm: '4px',
    md: '6px',
    lg: '8px',
    circle: '50%',
  },
};