/* VS Code-like styling */

/* Activity Bar (sidebar) */
.activity-bar {
  background-color: #333333;
  color: #ffffff;
}

.activity-bar-item {
  color: rgba(255, 255, 255, 0.5);
}

.activity-bar-item:hover,
.activity-bar-item.active {
  color: #ffffff;
}

.activity-bar-indicator {
  background-color: #ffffff;
}

/* Sidebar */
.sidebar {
  background-color: #252526;
  color: #cccccc;
}

.sidebar-section {
  padding: 0;
}

.sidebar-header {
  padding: 8px 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  color: #bbbbbb;
}

.sidebar-item {
  padding: 6px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
}

.sidebar-item:hover {
  background-color: #2a2d2e;
}

.sidebar-item.active {
  background-color: #37373d;
}

/* Editor */
.editor-container {
  background-color: #1e1e1e;
}

.editor-tabs {
  background-color: #252526;
  height: 35px;
  display: flex;
  align-items: center;
  padding-left: 8px;
}

.editor-tab {
  height: 35px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  background-color: #2d2d2d;
  color: #969696;
  border-right: 1px solid #252526;
  cursor: pointer;
  font-size: 13px;
}

.editor-tab.active {
  background-color: #1e1e1e;
  color: #ffffff;
}

.editor-tab:hover:not(.active) {
  background-color: #2a2a2a;
}

.editor-tab-close {
  margin-left: 8px;
  opacity: 0.7;
}

.editor-tab-close:hover {
  opacity: 1;
}

/* Terminal */
.terminal-container {
  background-color: #1e1e1e;
  border-top: 1px solid #474747;
}

.terminal-tabs {
  background-color: #252526;
  height: 35px;
  display: flex;
  align-items: center;
  padding-left: 8px;
}

.terminal-tab {
  height: 35px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  background-color: #2d2d2d;
  color: #969696;
  border-right: 1px solid #252526;
  cursor: pointer;
  font-size: 13px;
}

.terminal-tab.active {
  background-color: #1e1e1e;
  color: #ffffff;
}

.terminal-tab:hover:not(.active) {
  background-color: #2a2a2a;
}

/* Status Bar */
.status-bar {
  background-color: #007acc;
  color: #ffffff;
  height: 22px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.status-bar-item {
  padding: 0 8px;
  height: 100%;
  display: flex;
  align-items: center;
}

.status-bar-item:hover {
  background-color: rgba(255, 255, 255, 0.12);
}

.status-bar-item.active {
  background-color: rgba(255, 255, 255, 0.18);
}

.status-bar-separator {
  width: 1px;
  height: 22px;
  background-color: rgba(255, 255, 255, 0.2);
}

/* Scrollbars */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(121, 121, 121, 0.4);
  border-radius: 5px;
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 100, 100, 0.7);
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* Tooltips */
.tooltip {
  background-color: #252526;
  color: #cccccc;
  border: 1px solid #454545;
  padding: 6px 8px;
  font-size: 12px;
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Modals and Dialogs */
.modal {
  background-color: #252526;
  color: #cccccc;
  border: 1px solid #454545;
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.modal-header {
  padding: 12px 16px;
  border-bottom: 1px solid #454545;
  font-size: 14px;
  font-weight: 600;
}

.modal-body {
  padding: 16px;
}

.modal-footer {
  padding: 12px 16px;
  border-top: 1px solid #454545;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* Buttons */
.button {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 2px;
  font-size: 13px;
  cursor: pointer;
}

.button:hover {
  background-color: #1177bb;
}

.button.secondary {
  background-color: #3a3d41;
  color: #cccccc;
}

.button.secondary:hover {
  background-color: #45494e;
}

/* Inputs */
.input {
  background-color: #3c3c3c;
  color: #cccccc;
  border: 1px solid #3c3c3c;
  padding: 6px 8px;
  border-radius: 2px;
  font-size: 13px;
}

.input:focus {
  border-color: #007fd4;
  outline: none;
}

/* Dropdowns */
.dropdown {
  background-color: #3c3c3c;
  color: #cccccc;
  border: 1px solid #3c3c3c;
  padding: 6px 8px;
  border-radius: 2px;
  font-size: 13px;
  appearance: none;
}

.dropdown:focus {
  border-color: #007fd4;
  outline: none;
}
