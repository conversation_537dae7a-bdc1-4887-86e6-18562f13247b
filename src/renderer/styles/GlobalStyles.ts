import { createGlobalStyle } from 'styled-components';

export const GlobalStyles = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html, body {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: ${({ theme }) => theme.colors.background};
    color: ${({ theme }) => theme.colors.text.primary};
    font-size: 13px;
    line-height: 1.4;
    user-select: none;
  }

  code, pre, .monaco-editor {
    font-family: 'Cascadia Code', 'Fira Code', Menlo, Monaco, Consolas, 'Courier New',
      monospace;
    font-feature-settings: "calt" 1;  /* For font ligatures */
  }

  /* Custom scrollbar styling - VS Code style */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.mode === 'dark' ? 'rgba(121, 121, 121, 0.4)' : 'rgba(100, 100, 100, 0.4)'};
    border-radius: 5px;
    border: 2px solid transparent;
    background-clip: content-box;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: ${({ theme }) => theme.mode === 'dark' ? 'rgba(100, 100, 100, 0.7)' : 'rgba(96, 96, 96, 0.7)'};
    border: 2px solid transparent;
    background-clip: content-box;
  }

  ::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Focus styles */
  :focus {
    outline: 1px solid ${({ theme }) => theme.colors.primary};
    outline-offset: -1px;
  }

  /* Selection styles */
  ::selection {
    background-color: ${({ theme }) => theme.mode === 'dark' ? 'rgba(0, 122, 204, 0.5)' : 'rgba(0, 122, 204, 0.3)'};
  }
`;