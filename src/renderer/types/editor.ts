// src/renderer/types/editor.ts
export interface EditorPreferences {
    fontSize: number;
    tabSize: number;
    wordWrap: 'off' | 'on' | 'wordWrapColumn' | 'bounded';
    minimap: boolean;
    lineNumbers: 'on' | 'off' | 'relative';
    fontFamily: string;
    formatOnSave: boolean;
    autoIndent: boolean;
  }
  
  export const defaultEditorPreferences: EditorPreferences = {
    fontSize: 14,
    tabSize: 2,
    wordWrap: 'off',
    minimap: true,
    lineNumbers: 'on',
    fontFamily: 'Menlo, Monaco, "Courier New", monospace',
    formatOnSave: false,
    autoIndent: true,
  };