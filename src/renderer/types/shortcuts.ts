// src/renderer/types/shortcuts.ts
export interface KeyboardShortcut {
    id: string;
    description: string;
    keys: string[];
    category: string;
    action: () => void;
  }
  
  export const formatShortcut = (keys: string[]): string => {
    return keys.map(key => 
      key === 'mod' 
        ? navigator.platform.includes('Mac') ? '⌘' : 'Ctrl' 
        : key.charAt(0).toUpperCase() + key.slice(1)
    ).join('+');
  };