// src/renderer/themes.ts
export const lightTheme = {
    colors: {
      primary: '#2c6fdb',
      secondary: '#f3f3f3',
      background: '#ffffff',
      foreground: '#f9f9f9',
      border: '#e1e1e1',
      error: '#e53935',
      text: {
        primary: '#333333',
        secondary: '#757575'
      }
    },
    spacing: {
      xs: '4px',
      sm: '8px',
      md: '16px',
      lg: '24px',
      xl: '32px'
    },
    fontSizes: {
      xs: '10px',
      sm: '12px',
      md: '14px',
      lg: '16px',
      xl: '20px'
    }
  };
  
  export const darkTheme = {
    colors: {
      primary: '#2c6fdb',
      secondary: '#2d2d2d',
      background: '#1e1e1e',
      foreground: '#252526',
      border: '#3c3c3c',
      error: '#f44336',
      text: {
        primary: '#e0e0e0',
        secondary: '#a0a0a0'
      }
    },
    spacing: {
      xs: '4px',
      sm: '8px',
      md: '16px',
      lg: '24px',
      xl: '32px'
    },
    fontSizes: {
      xs: '10px',
      sm: '12px',
      md: '14px',
      lg: '16px',
      xl: '20px'
    }
  };