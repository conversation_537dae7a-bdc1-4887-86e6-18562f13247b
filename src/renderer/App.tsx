// src/renderer/App.tsx
import React, { useState, useEffect, createContext } from 'react';
import { DashboardLayout } from './components/layout/DashboardLayout';
import { BoardProvider } from './contexts/board-context';
import { AgentBoardControllerProvider } from './contexts/agent-board-controller';
import { ErrorBoundary } from './components/error/ErrorBoundary';
import { Toaster } from './components/ui/toaster';
import { handleError, ErrorType, ErrorSeverity } from './lib/error-handler';

// Import Monaco and xterm CSS properly
import 'monaco-editor/min/vs/editor/editor.main.css';
import '@xterm/xterm/css/xterm.css';

// Import VS Code-like styling
import './styles/vscode.css';

// Create context for managing context menus globally
export const ContextMenuContext = createContext<{
  closeAllMenus: () => void;
}>({
  closeAllMenus: () => {}
});

const App: React.FC = () => {
  const [theme, setTheme] = useState<'light' | 'dark'>('dark');
  const [ipcResponse, setIpcResponse] = useState<string | null>(null);

  // Test IPC communication on startup
  useEffect(() => {
    const testIpc = async () => {
      try {
        if (window.electron) {
          console.log('Electron IPC is available, attempting ping...');
          const response = await window.electron.ipcRenderer.invoke('ping', null);
          setIpcResponse(response);
          console.log('IPC test response:', response);
        } else {
          console.log('Electron IPC is not available in this environment');
        }
      } catch (error) {
        console.error('IPC test error:', error);
      }
    };

    testIpc();
  }, []);

  // Function to close all context menus
  const closeAllMenus = () => {
    // Dispatch a custom event that context menu components can listen for
    window.dispatchEvent(new CustomEvent('close-all-context-menus'));
  };

  // Add global handlers
  useEffect(() => {
    // Close all menus on escape key
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closeAllMenus();
      }
    };

    // Global mousedown to close menus
    const handleGlobalMouseDown = (e: MouseEvent) => {
      // Don't close if clicking on a context menu or its children
      const target = e.target as HTMLElement;
      if (!target.closest('.context-menu')) {
        closeAllMenus();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    document.addEventListener('mousedown', handleGlobalMouseDown);

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.removeEventListener('mousedown', handleGlobalMouseDown);
    };
  }, []);

  const handleThemeChange = (newTheme: 'light' | 'dark') => {
    setTheme(newTheme);
  };

  // Handle uncaught errors
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Uncaught error:', error, errorInfo);
    // You could also send this to an error reporting service
  };

  return (
    <ErrorBoundary onError={handleError}>
      <ContextMenuContext.Provider value={{ closeAllMenus }}>
        <BoardProvider>
          <AgentBoardControllerProvider>
            <DashboardLayout
              theme={theme}
              onThemeChange={handleThemeChange}
            />
            <Toaster />
          </AgentBoardControllerProvider>
        </BoardProvider>
      </ContextMenuContext.Provider>
    </ErrorBoundary>
  );
};

export default App;
