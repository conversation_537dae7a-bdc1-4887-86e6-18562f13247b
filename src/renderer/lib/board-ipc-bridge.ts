// src/renderer/lib/board-ipc-bridge.ts

// Board events coming from main process
export const BOARD_EVENTS = {
  AGENT_ACTION: 'board:agent-action',
  STATE_UPDATE: 'board:state-update',
  AGENT_STATUS: 'board:agent-status',
};

// Board commands sent to main process
export const BOARD_COMMANDS = {
  GET_STATE: 'board:get-state',
  CREATE_CARD: 'board:create-card',
  UPDATE_CARD: 'board:update-card',
  MOVE_CARD: 'board:move-card',
  DELETE_CARD: 'board:delete-card',
  ASSIGN_AGENT: 'board:assign-agent',
  UNASSIGN_AGENT: 'board:unassign-agent',
  GET_AGENT_STATUS: 'board:get-agent-status',
  GET_AGENT_RESOURCE_USAGE: 'board:get-agent-resource-usage',
  CREATE_COLUMN: 'board:create-column',
  UPDATE_COLUMN: 'board:update-column',
  DELETE_COLUMN: 'board:delete-column',
  CREATE_SWIMLANE: 'board:create-swimlane',
  UPDATE_SWIMLANE: 'board:update-swimlane',
  DELETE_SWIMLANE: 'board:delete-swimlane',
  TOGGLE_SWIMLANE_EXPANSION: 'board:toggle-swimlane-expansion',
  UPDATE_CARD_TYPES: 'board:update-card-types',
  UPDATE_AGENTS: 'board:update-agents',
  SAVE_BOARD_STATE: 'board:save-state',
  LOAD_BOARD_STATE: 'board:load-state',
};

export class BoardIPCBridge {
  // Register listeners for events from main process
  registerEventListeners(handlers: {
    onAgentAction?: (action: any) => void;
    onStateUpdate?: (state: any) => void;
    onAgentStatus?: (status: any) => void;
  }) {
    const cleanupFunctions: (() => void)[] = [];

    if (handlers.onAgentAction) {
      const cleanup = window.electron.ipcRenderer.on(BOARD_EVENTS.AGENT_ACTION, handlers.onAgentAction);
      cleanupFunctions.push(cleanup);
    }

    if (handlers.onStateUpdate) {
      const cleanup = window.electron.ipcRenderer.on(BOARD_EVENTS.STATE_UPDATE, handlers.onStateUpdate);
      cleanupFunctions.push(cleanup);
    }

    if (handlers.onAgentStatus) {
      const cleanup = window.electron.ipcRenderer.on(BOARD_EVENTS.AGENT_STATUS, handlers.onAgentStatus);
      cleanupFunctions.push(cleanup);
    }

    // Return unsubscribe function
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }

  // Send commands to main process
  async getBoardState() {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.GET_STATE, {});
  }

  async createCard(cardData: any, agentId?: string) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.CREATE_CARD, { cardData, agentId });
  }

  async updateCard(cardId: string, updates: any, agentId?: string) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.UPDATE_CARD, { cardId, updates, agentId });
  }

  async moveCard(cardId: string, toColumnId: string, toSwimlaneId: string, agentId?: string) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.MOVE_CARD, { cardId, toColumnId, toSwimlaneId, agentId });
  }

  async deleteCard(cardId: string, agentId?: string) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.DELETE_CARD, { cardId, agentId });
  }

  async assignAgent(cardId: string, agentId: string, agentType: string) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.ASSIGN_AGENT, { cardId, agentId, agentType });
  }

  async unassignAgent(cardId: string, agentId: string) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.UNASSIGN_AGENT, { cardId, agentId });
  }

  async getAgentStatus(agentId: string) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.GET_AGENT_STATUS, { agentId });
  }

  async getAgentResourceUsage(agentId: string) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.GET_AGENT_RESOURCE_USAGE, { agentId });
  }

  async createColumn(columnData: any) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.CREATE_COLUMN, { columnData });
  }

  async updateColumn(columnId: string, updates: any) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.UPDATE_COLUMN, { columnId, updates });
  }

  async deleteColumn(columnId: string) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.DELETE_COLUMN, { columnId });
  }

  async createSwimlane(swimlaneData: any) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.CREATE_SWIMLANE, { swimlaneData });
  }

  async updateSwimlane(swimlaneId: string, updates: any) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.UPDATE_SWIMLANE, { swimlaneId, updates });
  }

  async deleteSwimlane(swimlaneId: string) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.DELETE_SWIMLANE, { swimlaneId });
  }

  async toggleSwimlaneExpansion(swimlaneId: string) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.TOGGLE_SWIMLANE_EXPANSION, { swimlaneId });
  }

  async updateCardTypes(boardId: string, cardTypes: any[]) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.UPDATE_CARD_TYPES, { boardId, cardTypes });
  }

  async updateAgents(boardId: string, agents: any[]) {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.UPDATE_AGENTS, { boardId, agents });
  }

  async saveBoardState() {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.SAVE_BOARD_STATE, {});
  }

  async loadBoardState() {
    return window.electron.ipcRenderer.invoke(BOARD_COMMANDS.LOAD_BOARD_STATE, {});
  }
}

export const boardIPCBridge = new BoardIPCBridge();
