/**
 * Error handling utility for the application
 */

// Define error types
export enum ErrorType {
  NETWORK = 'NETWORK',
  IPC = 'IPC',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  INTERNAL = 'INTERNAL',
  UNKNOWN = 'UNKNOWN'
}

// Define error severity
export enum ErrorSeverity {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL'
}

// Application error class
export class AppError extends Error {
  type: ErrorType;
  severity: ErrorSeverity;
  originalError?: Error;
  context?: Record<string, any>;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.ERROR,
    originalError?: Error,
    context?: Record<string, any>
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.severity = severity;
    this.originalError = originalError;
    this.context = context;
  }
}

// Error handler class
export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorListeners: Array<(error: AppError) => void> = [];

  private constructor() {}

  // Get singleton instance
  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  // Handle an error
  public handleError(error: Error | AppError, context?: Record<string, any>): AppError {
    let appError: AppError;

    if (error instanceof AppError) {
      appError = error;
      if (context) {
        appError.context = { ...appError.context, ...context };
      }
    } else {
      // Convert regular Error to AppError
      appError = this.createAppError(error, context);
    }

    // Log the error
    this.logError(appError);

    // Notify listeners
    this.notifyListeners(appError);

    return appError;
  }

  // Create an AppError from a regular Error
  private createAppError(error: Error, context?: Record<string, any>): AppError {
    let type = ErrorType.UNKNOWN;
    let severity = ErrorSeverity.ERROR;

    // Try to determine error type from message or name
    if (error.message.includes('network') || error.message.includes('connection')) {
      type = ErrorType.NETWORK;
    } else if (error.message.includes('IPC') || error.message.includes('electron')) {
      type = ErrorType.IPC;
    } else if (error.message.includes('validation') || error.message.includes('invalid')) {
      type = ErrorType.VALIDATION;
      severity = ErrorSeverity.WARNING;
    } else if (error.message.includes('auth') || error.message.includes('login')) {
      type = ErrorType.AUTHENTICATION;
    } else if (error.message.includes('permission') || error.message.includes('access')) {
      type = ErrorType.AUTHORIZATION;
    } else if (error.message.includes('not found') || error.message.includes('404')) {
      type = ErrorType.NOT_FOUND;
      severity = ErrorSeverity.WARNING;
    }

    return new AppError(
      error.message,
      type,
      severity,
      error,
      context
    );
  }

  // Log the error
  private logError(error: AppError): void {
    const logMessage = {
      message: error.message,
      type: error.type,
      severity: error.severity,
      stack: error.stack,
      originalError: error.originalError ? {
        message: error.originalError.message,
        stack: error.originalError.stack
      } : undefined,
      context: error.context
    };

    // Log based on severity
    switch (error.severity) {
      case ErrorSeverity.INFO:
        console.info('APP ERROR [INFO]:', logMessage);
        break;
      case ErrorSeverity.WARNING:
        console.warn('APP ERROR [WARNING]:', logMessage);
        break;
      case ErrorSeverity.ERROR:
        console.error('APP ERROR [ERROR]:', logMessage);
        break;
      case ErrorSeverity.CRITICAL:
        console.error('APP ERROR [CRITICAL]:', logMessage);
        // Could send to error reporting service here
        break;
    }

    // Send to main process for logging to file
    if (window.electron && window.electron.ipcRenderer) {
      window.electron.ipcRenderer.send('log:error', logMessage);
    }
  }

  // Add an error listener
  public addListener(listener: (error: AppError) => void): () => void {
    this.errorListeners.push(listener);
    
    // Return function to remove the listener
    return () => {
      this.errorListeners = this.errorListeners.filter(l => l !== listener);
    };
  }

  // Notify all listeners
  private notifyListeners(error: AppError): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(error);
      } catch (listenerError) {
        console.error('Error in error listener:', listenerError);
      }
    });
  }
}

// Helper functions
export function handleError(error: Error | AppError, context?: Record<string, any>): AppError {
  return ErrorHandler.getInstance().handleError(error, context);
}

export function createError(
  message: string,
  type: ErrorType = ErrorType.UNKNOWN,
  severity: ErrorSeverity = ErrorSeverity.ERROR,
  originalError?: Error,
  context?: Record<string, any>
): AppError {
  return new AppError(message, type, severity, originalError, context);
}

export function addErrorListener(listener: (error: AppError) => void): () => void {
  return ErrorHandler.getInstance().addListener(listener);
}

// Async error wrapper
export function withErrorHandling<T>(
  fn: () => Promise<T>,
  context?: Record<string, any>
): Promise<T> {
  return fn().catch(error => {
    throw handleError(error, context);
  });
}
