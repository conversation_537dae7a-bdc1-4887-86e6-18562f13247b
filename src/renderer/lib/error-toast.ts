import { AppError, ErrorSeverity, ErrorType, addErrorListener } from './error-handler';
import { useToast } from '../hooks/use-toast';

// Initialize error toast handler
export function initErrorToastHandler() {
  const { toast } = useToast();
  
  // Add listener to the error handler
  const removeListener = addErrorListener((error: AppError) => {
    // Only show toast for errors that should be visible to the user
    if (shouldShowErrorToast(error)) {
      showErrorToast(error, toast);
    }
  });
  
  return removeListener;
}

// Determine if an error should be shown as a toast
function shouldShowErrorToast(error: AppError): boolean {
  // Don't show toasts for info level errors
  if (error.severity === ErrorSeverity.INFO) {
    return false;
  }
  
  // Don't show toasts for certain error types that are handled elsewhere
  if (error.type === ErrorType.VALIDATION && error.context?.silent) {
    return false;
  }
  
  return true;
}

// Show error toast
function showErrorToast(error: AppError, toast: any): void {
  const variant = getToastVariant(error);
  const title = getErrorTitle(error);
  const description = error.message;
  
  toast({
    variant,
    title,
    description,
    duration: getToastDuration(error),
  });
}

// Get toast variant based on error severity
function getToastVariant(error: AppError): 'default' | 'destructive' | 'success' {
  switch (error.severity) {
    case ErrorSeverity.WARNING:
      return 'default';
    case ErrorSeverity.ERROR:
    case ErrorSeverity.CRITICAL:
      return 'destructive';
    default:
      return 'default';
  }
}

// Get error title based on error type
function getErrorTitle(error: AppError): string {
  switch (error.type) {
    case ErrorType.NETWORK:
      return 'Network Error';
    case ErrorType.IPC:
      return 'Communication Error';
    case ErrorType.VALIDATION:
      return 'Validation Error';
    case ErrorType.AUTHENTICATION:
      return 'Authentication Error';
    case ErrorType.AUTHORIZATION:
      return 'Authorization Error';
    case ErrorType.NOT_FOUND:
      return 'Not Found';
    case ErrorType.INTERNAL:
      return 'Application Error';
    case ErrorType.UNKNOWN:
    default:
      return 'Error';
  }
}

// Get toast duration based on error severity
function getToastDuration(error: AppError): number {
  switch (error.severity) {
    case ErrorSeverity.WARNING:
      return 5000; // 5 seconds
    case ErrorSeverity.ERROR:
      return 7000; // 7 seconds
    case ErrorSeverity.CRITICAL:
      return 10000; // 10 seconds
    default:
      return 5000; // 5 seconds
  }
}
