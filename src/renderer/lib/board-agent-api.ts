/**
 * Board Agent API
 *
 * This service provides methods for AI agents to interact with the Kanban board.
 */

import { boardIPCBridge } from './board-ipc-bridge';

export class BoardAgentAPI {
  private boardContext: any;

  constructor(boardContext: any) {
    this.boardContext = boardContext;
  }

  /**
   * Bind to IPC events for agent communication
   */
  bindToIPC() {
    if (typeof window !== 'undefined' && window.electron) {
      const cleanupFunctions: (() => void)[] = [];

      const agentActionCleanup = window.electron.ipcRenderer.on('board:agent-action', this.handleAgentAction);
      cleanupFunctions.push(agentActionCleanup);

      const agentSuggestionCleanup = window.electron.ipcRenderer.on('board:agent-suggestion', this.handleAgentSuggestion);
      cleanupFunctions.push(agentSuggestionCleanup);

      const agentStatusCleanup = window.electron.ipcRenderer.on('board:agent-status', this.handleAgentStatusChange);
      cleanupFunctions.push(agentStatusCleanup);

      // Return cleanup function
      return () => {
        cleanupFunctions.forEach(cleanup => cleanup());
      };
    }

    return () => {}; // Empty cleanup function if window or electron is not available
  }

  /**
   * Handle agent actions received via IPC
   */
  handleAgentAction = (data: any) => {
    console.log('Received agent action:', data);

    switch (data.action) {
      case 'create_card':
        this.createTaskCard(data.task, data.agentId);
        break;
      case 'move_card':
        this.moveCardToColumn(data.cardId, data.columnId, data.agentId);
        break;
      case 'update_progress':
        this.updateCardProgress(data.cardId, data.progress, data.agentId);
        break;
      default:
        console.warn('Unknown agent action:', data.action);
    }
  };

  /**
   * Handle agent suggestions
   */
  handleAgentSuggestion = (data: any) => {
    console.log('Received agent suggestion:', data);
    // Implementation would depend on how suggestions are handled in the UI
  };

  /**
   * Handle agent status changes
   */
  handleAgentStatusChange = (data: any) => {
    console.log('Received agent status change:', data);
    // Implementation would depend on how agent statuses are tracked
  };

  /**
   * Send board updates to main process
   */
  sendBoardUpdate(boardState: any) {
    if (typeof window !== 'undefined' && window.electron) {
      window.electron.ipcRenderer.invoke('board:save-state', {});
    }
  }

  /**
   * Suggest dependencies based on code analysis
   */
  async suggestDependencies(cardId: string) {
    // Get card details
    const card = this.getCardById(cardId);
    if (!card) return [];

    // Check if we have access to the code context provider
    if (typeof window === 'undefined' || !window.contextProvider) {
      return [];
    }

    try {
      // Analyze code context to suggest dependencies
      const codeContext = await window.contextProvider?.getCodeContext() || {};

      // Use AI to suggest dependencies based on code
      const suggestedDeps = window.agentSystem ?
        await window.agentSystem.analyzeTaskDependencies(card.title, card.description, codeContext) :
        [];

      return suggestedDeps;
    } catch (error) {
      // Log the error
      console.error('Error suggesting dependencies:', error);
      // Use the error handler if available
      try {
        import('./error-handler').then(({ handleError, ErrorType }) => {
          handleError(error instanceof Error ? error : new Error(String(error)), {
            component: 'BoardAgentAPI',
            method: 'suggestDependencies',
            cardId: card.id,
            cardTitle: card.title
          });
        });
      } catch (e) {
        console.error('Error handling error:', e);
      }
      return [];
    }
  }

  /**
   * Get the current state of the board
   */
  async getBoardState() {
    try {
      return await boardIPCBridge.getBoardState();
    } catch (error) {
      console.error('Error getting board state:', error);
      return this.boardContext ? {
        columns: this.boardContext.columns,
        cards: this.getAllCards(),
        swimlanes: this.boardContext.swimlanes,
      } : null;
    }
  }

  /**
   * Get a card by ID
   */
  getCardById(cardId: string) {
    if (!this.boardContext || !this.boardContext.activeBoard) return null;

    for (const column of this.boardContext.activeBoard.columns) {
      const card = column.cards.find((c: any) => c.id === cardId);
      if (card) return card;
    }

    return null;
  }

  /**
   * Get all cards from all columns
   */
  getAllCards() {
    if (!this.boardContext || !this.boardContext.activeBoard) return [];

    return this.boardContext.activeBoard.columns.flatMap((column: any) => column.cards);
  }

  /**
   * Create a new task card
   */
  async createTaskCard(task: any, agentId: string) {
    try {
      return await boardIPCBridge.createCard(task, agentId);
    } catch (error) {
      console.error('Error creating task card:', error);

      // Fallback to local implementation if IPC fails
      if (this.boardContext && this.boardContext.addCardToColumn) {
        const now = new Date().toISOString();

        const newCard = {
          title: task.title,
          description: task.description || '',
          priority: task.priority || 'medium',
          columnId: task.columnId || this.boardContext.activeBoard.columns[0]?.id,
          swimlaneId: task.swimlaneId || this.boardContext.activeBoard.swimlanes[0]?.id,
          agentAssignments: [
            {
              agentId,
              agentType: 'AI',
              assignmentTime: now,
            },
          ],
          dependencies: task.dependencies || [],
          resourceMetrics: {
            tokenUsage: 0,
            cpuTime: 0,
            memoryUsage: 0,
          },
          taskHistory: [
            {
              timestamp: now,
              action: 'created',
              agentId,
              details: 'Card created by AI agent',
            },
          ],
          createdAt: now,
          updatedAt: now,
        };

        const cardId = this.boardContext.addCardToColumn(
          this.boardContext.activeBoard.id,
          newCard.columnId,
          newCard
        );

        return { ...newCard, id: cardId };
      }

      return null;
    }
  }

  /**
   * Move a card to a different column
   */
  async moveCardToColumn(cardId: string, columnId: string, agentId: string) {
    try {
      // Get the card to find its current swimlane
      const card = this.getCardById(cardId);
      if (!card) return null;

      return await boardIPCBridge.moveCard(cardId, columnId, card.swimlaneId, agentId);
    } catch (error) {
      console.error('Error moving card:', error);

      // Fallback to local implementation
      if (this.boardContext && this.boardContext.moveCard) {
        this.boardContext.moveCard(
          this.boardContext.activeBoard.id,
          cardId,
          columnId
        );

        return true;
      }

      return false;
    }
  }

  /**
   * Update card progress
   */
  async updateCardProgress(cardId: string, progress: number, agentId: string) {
    try {
      const card = this.getCardById(cardId);
      if (!card) return null;

      return await boardIPCBridge.updateCard(cardId, { progress }, agentId);
    } catch (error) {
      console.error('Error updating card progress:', error);

      // Fallback to local implementation
      if (this.boardContext && this.boardContext.updateCardInColumn) {
        const card = this.getCardById(cardId);
        if (!card) return null;

        const updatedCard = {
          ...card,
          progress: Math.min(100, Math.max(0, progress)),
          taskHistory: [
            ...(card.taskHistory || []),
            {
              timestamp: new Date().toISOString(),
              action: 'progress-update',
              agentId,
              details: `Updated progress to ${progress}%`,
            },
          ],
        };

        this.boardContext.updateCardInColumn(
          this.boardContext.activeBoard.id,
          card.columnId,
          cardId,
          updatedCard
        );

        return updatedCard;
      }

      return null;
    }
  }
}
