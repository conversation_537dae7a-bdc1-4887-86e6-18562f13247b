<!-- src/renderer/index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://unpkg.com; style-src 'self' 'unsafe-inline'; font-src 'self' data:; connect-src 'self' https://*; img-src 'self' data:;">
  <title>Middlware</title>
  <script>
    // Initialize global variables that will be populated by the preload script
    window.global = window;
    global = window;
  </script>
</head>
<body>
  <div id="root"></div>
</body>
</html>