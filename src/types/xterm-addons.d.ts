// src/types/xterm-addons.d.ts
declare module '@xterm/addon-fit' {
  import { Terminal } from '@xterm/xterm';
  
  export class FitAddon {
    constructor();
    activate(terminal: Terminal): void;
    fit(): void;
    dispose(): void;
  }
}

declare module '@xterm/addon-search' {
  import { Terminal } from '@xterm/xterm';
  
  export interface ISearchOptions {
    regex?: boolean;
    wholeWord?: boolean;
    caseSensitive?: boolean;
    incremental?: boolean;
  }
  
  export class SearchAddon {
    constructor();
    activate(terminal: Terminal): void;
    findNext(term: string, searchOptions?: ISearchOptions): boolean;
    findPrevious(term: string, searchOptions?: ISearchOptions): boolean;
    dispose(): void;
  }
}

declare module '@xterm/addon-web-links' {
  import { Terminal } from '@xterm/xterm';
  
  export interface ILinkProviderOptions {
    hover?: (event: MouseEvent, text: string, location: ILinkifierHoverEvent) => void;
    leave?: (event: MouseEvent, text: string) => void;
    urlRegex?: RegExp;
  }
  
  export interface ILinkifierHoverEvent {
    row: number;
    column: number;
  }
  
  export class WebLinksAddon {
    constructor(handler: (event: MouseEvent, uri: string) => void, options?: ILinkProviderOptions);
    activate(terminal: Terminal): void;
    dispose(): void;
  }
}

declare module '@xterm/addon-unicode11' {
  import { Terminal } from '@xterm/xterm';
  
  export class Unicode11Addon {
    constructor();
    activate(terminal: Terminal): void;
    dispose(): void;
  }
}
