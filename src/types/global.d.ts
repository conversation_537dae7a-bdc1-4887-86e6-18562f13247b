interface Window {
    electron: {
      ipcRenderer: {
        send: (channel: string, data: any) => void;
        invoke: <T = any>(channel: string, data: any) => Promise<T>;
        on: (channel: string, listener: (...args: any[]) => void) => () => void;
      };
      path: {
        basename: (path: string, ext?: string) => string;
        dirname: (path: string) => string;
        extname: (path: string) => string;
        join: (...paths: string[]) => string;
        resolve: (...paths: string[]) => string;
      }
    };
    contextProvider?: {
      getCodeContext(): Promise<any>;
    };
    agentSystem?: {
      analyzeTaskDependencies(title: string, description: string, context: any): Promise<any[]>;
    };
  }

  declare module 'xterm';
  declare module 'xterm-addon-fit';

  declare module '@xterm/xterm';
  declare module '@xterm/addon-fit';
  declare module '@xterm/addon-search';
  declare module '@xterm/addon-web-links';
  declare module '@xterm/addon-unicode11';

  declare module 'class-variance-authority' {
    export function cva(base: string, config?: any): any;
    export type VariantProps<T> = T extends (...args: any) => any
      ? Parameters<T>[0]
      : never;
  }

  declare module 'tailwind-merge' {
    export function twMerge(...inputs: string[]): string;
  }