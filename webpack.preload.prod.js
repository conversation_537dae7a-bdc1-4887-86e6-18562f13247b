const path = require('path');

module.exports = {
  mode: 'production',
  target: 'electron-preload',
  entry: './src/preload/preload.ts',
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: {
          loader: 'ts-loader',
          options: {
            transpileOnly: true, // Skip type checking
            compilerOptions: {
              noEmitOnError: false
            }
          }
        },
        exclude: /node_modules/,
      },
    ],
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  output: {
    filename: 'preload.js',
    path: path.resolve(__dirname, 'dist/preload'),
  },
};