{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "jsx": "react-jsx", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "noImplicitAny": true, "sourceMap": true, "baseUrl": ".", "outDir": "dist", "lib": ["DOM", "DOM.Iterable", "ESNext"], "paths": {"@/*": ["src/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "noEmitOnError": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}