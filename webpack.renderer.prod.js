const path = require('path');
const webpack = require('webpack');
const { merge } = require('webpack-merge');
const common = require('./webpack.common.js');

module.exports = merge(common, {
  mode: 'production',
  target: 'electron-renderer', // Changed back to electron-renderer
  entry: './src/renderer/index.tsx',
  output: {
    filename: 'renderer.js',
    path: path.resolve(__dirname, 'dist/renderer'),
  },
  resolve: {
    fallback: {
      path: require.resolve('path-browserify'),
      fs: false,
      crypto: false,
      stream: false,
      os: false,
      util: false,
      assert: false,
      events: require.resolve('events/'),
    },
  },
  // Add this to provide a global require function
  plugins: [
    new webpack.ProvidePlugin({
      process: 'process/browser',
    }),
  ],
});